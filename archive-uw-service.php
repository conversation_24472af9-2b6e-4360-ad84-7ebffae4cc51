<?php

use Umimeweby\UWTheme\Form\Generic_Form;
use Umimeweby\UWTheme\Settings\UW_Services_Settings;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;
use Umimeweby\UWTheme\Taxonomies\UW_Service_Custom_Taxonomy;

get_header();

$service_categories = get_terms([
    'taxonomy' => UW_Service_Custom_Taxonomy::TAXONOMY_KEY,
    'hide_empty' => false, // zobrazí i prázdné kategorie
    'meta_key' => UW_Service_Custom_Taxonomy::TAXONOMY_PREFIX . UW_Service_Custom_Taxonomy::TAXONOMY_FIELD_ORDER,
    'orderby' => 'meta_value_num',
    'order' => 'ASC',
]);


$form_title = rwmb_meta(UW_Settings_Page::PREFIX . UW_Services_Settings::FIELD_FORM_TITLE, ['object_type' => 'setting'], UW_Services_Settings::OPTION_NAME);
$description = rwmb_meta(UW_Settings_Page::PREFIX . UW_Services_Settings::FIELD_FORM_SUBTITLE, ['object_type' => 'setting'], UW_Services_Settings::OPTION_NAME);

$form = new Generic_Form();
?>

<main class="w-full pt-4 md:mb-0 md:pt-0">


    <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>


        <!-- ::::::::::::grid uw service categories section :::::::::::: -->
        <section class="relative -mt-4 md:-mt-4 py-16 px-4 md:px-8 bg-gradient-to-br from-theme-purple via-dark-purple to-theme-purple overflow-hidden">
            <!-- Dekorativní pozadí -->
            <div class="absolute inset-0 bg-plants bg-cover bg-no-repeat opacity-20"></div>
            <div class="absolute top-10 left-10 w-24 h-24 bg-theme-pink/10 rounded-full blur-xl"></div>
            <div class="absolute bottom-20 right-16 w-32 h-32 bg-theme-blue/10 rounded-full blur-xl"></div>
            
            <div class="relative z-10 container mx-auto px-4 max-w-[1170px]">
                <!-- Grid služeb -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">

                    <?php if (!empty($service_categories) && !is_wp_error($service_categories)) : ?>
                        <?php foreach ($service_categories as $category) : ?>
                            <?php
                            // Dynamické přiřazení ikon podle názvu kategorie
                            $category_name = strtolower($category->name);
                            $icon_html = '';
                            $category_link = get_term_link( $category );                           
                            
                            if (strpos($category_name, 'vývoj') !== false || strpos($category_name, 'aplikace') !== false || strpos($category_name, 'software') !== false) {
                                // Ikona pro vývoj/aplikace
                                $icon_html = '<svg class="w-8 h-8 text-theme-purple" viewBox="0 0 24 24" fill="none">
                                    <path d="M13 3L4 14h7l-2 7 9-11h-7l2-7z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>';
                            } elseif (strpos($category_name, 'správa') !== false || strpos($category_name, 'údržba') !== false || strpos($category_name, 'podpora') !== false) {
                                // Ikona pro správu/údržbu
                                $icon_html = '<svg class="w-8 h-8 text-theme-purple" viewBox="0 0 24 24" fill="none">
                                    <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>';
                            } elseif (strpos($category_name, 'marketing') !== false || strpos($category_name, 'reklama') !== false || strpos($category_name, 'seo') !== false) {
                                // Ikona pro marketing
                                $icon_html = '<svg class="w-8 h-8 text-theme-purple" viewBox="0 0 24 24" fill="none">
                                    <path d="M3 11l18-5v12L3 14v-3z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M11.6 16.8a3 3 0 1 1-5.8-1.6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>';
                            } elseif (strpos($category_name, 'konzultace') !== false || strpos($category_name, 'poradenství') !== false || strpos($category_name, 'analýza') !== false) {
                                // Ikona pro konzultace
                                $icon_html = '<svg class="w-8 h-8 text-theme-purple" viewBox="0 0 24 24" fill="none">
                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M8 9h8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M8 13h6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>';
                            } else {
                                // Výchozí ikona pro ostatní služby
                                $icon_html = '<svg class="w-8 h-8 text-theme-purple" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M2 17l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>';
                            }
                            ?>

                            <div class="relative bg-white/95 backdrop-blur-sm rounded-3xl p-6 lg:p-8 shadow-2xl hover:shadow-3xl transition-all duration-500 ring-2 ring-white/20 hover:ring-theme-pink/40 hover:ring-offset-2 hover:ring-offset-theme-pink/10 group hover:bg-white hover:-translate-y-2 min-h-[420px] flex flex-col">
                                <!-- Header s ikonou a názvem -->
                                <div class="flex items-center gap-4 mb-6">
                                    <div class="w-12 h-12 lg:w-14 lg:h-14 rounded-2xl bg-gradient-to-br from-theme-purple/10 to-theme-blue/10 flex items-center justify-center flex-shrink-0 group-hover:from-theme-purple/20 group-hover:to-theme-blue/20 transition-all duration-300 group-hover:scale-110">
                                        <?php echo $icon_html; ?>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="text-2xl lg:text-28 font-bold text-theme-purple leading-tight group-hover:text-theme-blue transition-colors">
                                            <?php echo $category->name; ?>
                                        </h3>
                                    </div>
                                </div>

                                <!-- Popis kategorie -->
                                <div class="mb-6">
                                    <p class="text-gray-600 text-base leading-relaxed">
                                        <?php echo $category->description; ?>
                                    </p>
                                </div>

                                <!-- Seznam služeb - dynamicky generovaný -->
                                <?php
                                // Získání prvních 4 custom postů v dané kategorii
                                $services_in_category = get_posts([
                                    'post_type' => 'uw-service',
                                    'posts_per_page' => 4,
                                    'tax_query' => [
                                        [
                                            'taxonomy' => UW_Service_Custom_Taxonomy::TAXONOMY_KEY,
                                            'field'    => 'term_id',
                                            'terms'    => $category->term_id,
                                        ],
                                    ],
                                    'post_status' => 'publish',
                                ]);
                                ?>
                                
                                <?php if (!empty($services_in_category)) : ?>
                                <div class="mb-8 flex-grow">
                                    <ul class="space-y-3 text-base">
                                        <?php foreach ($services_in_category as $service) : ?>
                                            <li>
                                                <a href="<?php echo get_permalink($service->ID); ?>" class="flex items-center gap-3 text-gray-900 hover:text-theme-pink transition-colors group/link">
                                                    <svg class="w-5 h-5 text-theme-pink group-hover/link:scale-110 transition-transform" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" />
                                                    </svg>
                                                    <span class="font-semibold"><?php echo get_the_title($service->ID); ?></span>
                                                </a>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                                <?php endif; ?>

                                <!-- CTA odkaz -->
                                <div class="mt-auto">
                                    <a href="<?php echo($category_link); ?>" class="inline-flex items-center text-theme-pink hover:text-theme-purple transition-colors">
                                        Zjistit více
                                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                                        </svg>
                                    </a>
                                </div>
                            </div>

                        <?php endforeach; ?>
                    <?php endif; ?>


                </div>
            </div>
        </section>

    </article>

    <?php get_template_part('template-parts/content/services/realized-for-our-clients') ?>
    <?php get_template_part('template-parts/content/services/technology') ?>
    <?php get_template_part('template-parts/content/services/why_we_are') ?>

    <!-- :::::::::::: contact form uw services section :::::::::::: -->
    <section class="relative z-20 mx-auto w-full max-w-[1440px]">
        <div class="w-full bg-[#eaeaea] px-5 pt-[60px] md:rounded-t-[100px]">
            <div class="relative mx-auto w-full max-w-[1170px] text-center md:text-left">
                <div class="relative">
                    <h2 class="mb-4 text-2xl font-bold leading-normal text-theme-purple md:text-3xl">
                        <?= $form_title ?? '' ?>
                    </h2>
                    <p class="text-base font-normal leading-5 mb-8">
                        <?= $description ?? '' ?>
                    </p>
                </div>
                <div class="text-lg">
                    <?= $form->get_form_html_code() ?>
                </div>
            </div>
        </div>

        <svg class="hidden h-auto w-full md:block" xmlns="http://www.w3.org/2000/svg" width="1440" height="181"
            fill="none" viewBox="0 0 1440 181">
            <path fill="#EAEAEA"
                d="M71.469 129.359C89.072 161.22 122.598 181 158.999 181H1281c36.4 0 69.93-19.78 87.53-51.641L1440 0H0l71.469 129.359Z" />
        </svg>
    </section>



</main>

<?php get_footer();
