{"private": true, "devDependencies": {"@_tw/themejson": "^0.1.2", "@angular-eslint/template-parser": "^15.1.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.9", "@wordpress/prettier-config": "^2.7.0", "archiver": "^5.3.1", "cross-env": "^7.0.3", "esbuild": "^0.16.16", "eslint": "^8.31.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-php-markup": "^6.0.0", "eslint-plugin-tailwindcss": "^3.8.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.21", "postcss-import-ext-glob": "^2.1.1", "postcss-simple-vars": "^7.0.1", "prettier": "^2.8.2", "prettier-plugin-tailwindcss": "^0.2.1", "tailwindcss": "^3.2.4"}, "scripts": {"development:tailwind:frontend": "npx tailwindcss --postcss -i ./tailwind/tailwind.css -c ./tailwind/tailwind.config.js -o style.css", "development:tailwind:editor": "cross-env _TW_TARGET=editor npx tailwindcss --postcss -i ./tailwind/tailwind.css -c ./tailwind/tailwind.config.js -o style-editor.css", "development:esbuild": "npx esbuild ./javascript/script.js --target=esnext --bundle --outfile=./js/script.min.js", "development": "run-p development:**", "dev": "npm run development", "watch:tailwind:frontend": "npm run development:tailwind:frontend -- --watch", "watch:tailwind:editor": "npm run development:tailwind:editor -- --watch", "watch:esbuild": "npm run development:esbuild -- --watch", "watch": "run-p watch:**", "lint:eslint": "npx eslint /", "lint:prettier": "npx prettier --check .", "lint": "run-p lint:*", "lint-fix:eslint": "npx eslint / --fix", "lint-fix:prettier": "npx prettier --write .", "lint-fix": "run-p lint-fix:*", "production:tailwind:frontend": "cross-env NODE_ENV=production npm run development:tailwind:frontend -- --minify", "production:tailwind:editor": "cross-env NODE_ENV=production npm run development:tailwind:editor -- --minify", "production:esbuild": "npm run development:esbuild -- --minify", "production": "run-p production:**", "prod": "npm run production", "zip": "node node_scripts/zip.js zm", "bundle": "run-s production zip"}, "prettier": "@wordpress/prettier-config", "dependencies": {"flowbite": "^1.6.4", "swiper": "^9.4.1"}}