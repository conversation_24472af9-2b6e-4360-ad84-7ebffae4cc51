/*!
Theme Name: <PERSON><PERSON><PERSON> Weby
Theme URI: https://umimeweby.cz
Author: Team UW
Author URI: https://umimeweby.cz
Description: Firemní web Umime Weby
Version: 1.4.14
Tested up to: 6.0
Requires PHP: 8.1
License: GNU General Public License v2 or later
License URI: LICENSE
Text Domain: uw
Tags:

This theme, like WordPress, is licensed under the GPL.
Use it to make something cool, have fun, and share what you've learned.

Umíme Weby is based on _tw https://underscoretw.com/, (C) 2021-2022 Greg <PERSON>
_tw is distributed under the terms of the GNU GPL v2 or later.

_tw is based on Underscores https://underscores.me/ and Varia https://github.com/Automattic/themes/tree/master/varia, (C) 2012-2022 Automattic, Inc.
Underscores and Varia are distributed under the terms of the GNU GPL v2 or later.
*/

/**
 * The line above injects the WordPress file header. It needs to be first,
 * before this comment.
 */

/**
 * This injects custom `@font-face` rules.
 */

/**
 * Custom `@font-face` rules
 *
 * These will be added immediately before Tailwind’s `base` layer.
 */

/* Light 300 */

@font-face {
  font-family: 'Open Sans';

  font-display: swap;

  font-style: normal;

  font-weight: 300;

  font-stretch: 100%;

  src: url('fonts/OpenSans/OpenSans-Light.woff2') format('woff2');
}

/* Light 300 Italic */

@font-face {
  font-family: 'Open Sans';

  font-display: swap;

  font-style: italic;

  font-weight: 300;

  font-stretch: 100%;

  src: url('fonts/OpenSans/OpenSans-LightItalic.woff2') format('woff2');
}

/* Regular */

@font-face {
  font-family: 'Open Sans';

  font-display: swap;

  font-style: normal;

  font-weight: 400;

  font-stretch: 100%;

  src: url('fonts/OpenSans/OpenSans-Regular.woff2') format('woff2');
}

/* Regular 400 Italic */

@font-face {
  font-family: 'Open Sans';

  font-display: swap;

  font-style: italic;

  font-weight: 400;

  font-stretch: 100%;

  src: url('fonts/OpenSans/OpenSans-Italic.woff2') format('woff2');
}

/* SemiBold 600 */

@font-face {
  font-family: 'Open Sans';

  font-display: swap;

  font-style: normal;

  font-weight: 600;

  font-stretch: 100%;

  src: url('fonts/OpenSans/OpenSans-SemiBold.woff2') format('woff2');
}

/* SemiBold 600 Italic */

@font-face {
  font-family: 'Open Sans';

  font-display: swap;

  font-style: italic;

  font-weight: 600;

  font-stretch: 100%;

  src: url('fonts/OpenSans/OpenSans-SemiBoldItalic.woff2') format('woff2');
}

/* Bold 700 */

@font-face {
  font-family: 'Open Sans';

  font-display: swap;

  font-style: normal;

  font-weight: 700;

  font-stretch: 100%;

  src: url('fonts/OpenSans/OpenSans-Bold.woff2') format('woff2');
}

/* Bold 700 Italic */

@font-face {
  font-family: 'Open Sans';

  font-display: swap;

  font-style: italic;

  font-weight: 700;

  font-stretch: 100%;

  src: url('fonts/OpenSans/OpenSans-BoldItalic.woff2') format('woff2');
}

/* ExtraBold 800 */

@font-face {
  font-family: 'Open Sans';

  font-display: swap;

  font-style: normal;

  font-weight: 800;

  font-stretch: 100%;

  src: url('fonts/OpenSans/OpenSans-ExtraBold.woff2') format('woff2');
}

/* ExtraBold 800 Italic */

@font-face {
  font-family: 'Open Sans';

  font-display: swap;

  font-style: italic;

  font-weight: 800;

  font-stretch: 100%;

  src: url('fonts/OpenSans/OpenSans-ExtraBoldItalic.woff2') format('woff2');
}

/**
 * This injects Tailwind's base styles and any base styles registered by
 * plugins, then adds custom base styles.
 */

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(63 131 248 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(63 131 248 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

.tooltip-arrow,.tooltip-arrow:before {
  position: absolute;
  width: 8px;
  height: 8px;
  background: inherit;
}

.tooltip-arrow {
  visibility: hidden;
}

.tooltip-arrow:before {
  content: "";
  visibility: visible;
  transform: rotate(45deg);
}

[data-tooltip-style^='light'] + .tooltip > .tooltip-arrow:before {
  border-style: solid;
  border-color: #e5e7eb;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='top'] > .tooltip-arrow:before {
  border-bottom-width: 1px;
  border-right-width: 1px;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='right'] > .tooltip-arrow:before {
  border-bottom-width: 1px;
  border-left-width: 1px;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='bottom'] > .tooltip-arrow:before {
  border-top-width: 1px;
  border-left-width: 1px;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='left'] > .tooltip-arrow:before {
  border-top-width: 1px;
  border-right-width: 1px;
}

.tooltip[data-popper-placement^='top'] > .tooltip-arrow {
  bottom: -4px;
}

.tooltip[data-popper-placement^='bottom'] > .tooltip-arrow {
  top: -4px;
}

.tooltip[data-popper-placement^='left'] > .tooltip-arrow {
  right: -4px;
}

.tooltip[data-popper-placement^='right'] > .tooltip-arrow {
  left: -4px;
}

.tooltip.invisible > .tooltip-arrow:before {
  visibility: hidden;
}

[data-popper-arrow],[data-popper-arrow]:before {
  position: absolute;
  width: 8px;
  height: 8px;
  background: inherit;
}

[data-popper-arrow] {
  visibility: hidden;
}

[data-popper-arrow]:before {
  content: "";
  visibility: visible;
  transform: rotate(45deg);
}

[data-popper-arrow]:after {
  content: "";
  visibility: visible;
  transform: rotate(45deg);
  position: absolute;
  width: 9px;
  height: 9px;
  background: inherit;
}

[role="tooltip"] > [data-popper-arrow]:before {
  border-style: solid;
  border-color: #e5e7eb;
}

.dark [role="tooltip"] > [data-popper-arrow]:before {
  border-style: solid;
  border-color: #4b5563;
}

[role="tooltip"] > [data-popper-arrow]:after {
  border-style: solid;
  border-color: #e5e7eb;
}

.dark [role="tooltip"] > [data-popper-arrow]:after {
  border-style: solid;
  border-color: #4b5563;
}

[data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:before {
  border-bottom-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:after {
  border-bottom-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:before {
  border-bottom-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:after {
  border-bottom-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:before {
  border-top-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:after {
  border-top-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:before {
  border-top-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:after {
  border-top-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow] {
  bottom: -5px;
}

[data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow] {
  top: -5px;
}

[data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow] {
  right: -5px;
}

[data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow] {
  left: -5px;
}

[role="tooltip"].invisible > [data-popper-arrow]:before {
  visibility: hidden;
}

[role="tooltip"].invisible > [data-popper-arrow]:after {
  visibility: hidden;
}

[type='text'],[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6B7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #1C64F2;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #1C64F2;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #6B7280;
  opacity: 1;
}

input::placeholder,textarea::placeholder {
  color: #6B7280;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
}

select:not([size]) {
  background-image: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 10 6'%3e %3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 1 4 4 4-4'/%3e %3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 0.75em 0.75em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[multiple] {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #1C64F2;
  background-color: #fff;
  border-color: #6B7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #1C64F2;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked,.dark [type='checkbox']:checked,.dark [type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 0.55em 0.55em;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 12'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M1 5.917 5.724 10.5 15 1.5'/%3e %3c/svg%3e");
  background-repeat: no-repeat;
  background-size: 0.55em 0.55em;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
  background-size: 1em 1em;
}

.dark [type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
  background-size: 1em 1em;
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 12'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M1 5.917 5.724 10.5 15 1.5'/%3e %3c/svg%3e");
  background-color: currentColor;
  border-color: transparent;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 0.55em 0.55em;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px auto inherit;
}

input[type=file]::file-selector-button {
  color: white;
  background: #1F2937;
  border: 0;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 2rem;
  padding-right: 1rem;
  margin-inline-start: -1rem;
  margin-inline-end: 1rem;
}

input[type=file]::file-selector-button:hover {
  background: #374151;
}

.dark input[type=file]::file-selector-button {
  color: white;
  background: #4B5563;
}

.dark input[type=file]::file-selector-button:hover {
  background: #6B7280;
}

input[type="range"]::-webkit-slider-thumb {
  height: 1.25rem;
  width: 1.25rem;
  background: #1C64F2;
  border-radius: 9999px;
  border: 0;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
}

input[type="range"]:disabled::-webkit-slider-thumb {
  background: #9CA3AF;
}

.dark input[type="range"]:disabled::-webkit-slider-thumb {
  background: #6B7280;
}

input[type="range"]:focus::-webkit-slider-thumb {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1px;
  --tw-ring-color: rgb(164 202 254 / var(--tw-ring-opacity));
}

input[type="range"]::-moz-range-thumb {
  height: 1.25rem;
  width: 1.25rem;
  background: #1C64F2;
  border-radius: 9999px;
  border: 0;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
}

input[type="range"]:disabled::-moz-range-thumb {
  background: #9CA3AF;
}

.dark input[type="range"]:disabled::-moz-range-thumb {
  background: #6B7280;
}

input[type="range"]::-moz-range-progress {
  background: #3F83F8;
}

input[type="range"]::-ms-fill-lower {
  background: #3F83F8;
}

.toggle-bg:after {
  content: "";
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  background: white;
  border-color: #D1D5DB;
  border-width: 1px;
  border-radius: 9999px;
  height: 1.25rem;
  width: 1.25rem;
  transition-property: background-color,border-color,color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;
  transition-duration: .15s;
  box-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}

input:checked + .toggle-bg:after {
  transform: translateX(100%);;
  border-color: white;
}

input:checked + .toggle-bg {
  background: #1C64F2;
  border-color: #1C64F2;
}

body {
  font-family: Open Sans, sans-serif, ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;
  line-height: 21px;
  --tw-text-opacity: 1;
  color: rgb(44 1 83 / var(--tw-text-opacity, 1));
}

/**
 * This injects Tailwind's component classes and any component classes
 * registered by plugins, then adds custom component classes.
 *
 */

.container {
  width: 100%;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.block-editor-block-list__layout {
  color: var(--tw-prose-body);
}

.block-editor-block-list__layout :where(p):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.block-editor-block-list__layout :where([class~="lead"]):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: var(--tw-prose-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}

.block-editor-block-list__layout :where(a):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: var(--tw-prose-links);
  text-decoration: underline;
  font-weight: 500;
}

.block-editor-block-list__layout :where(strong):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: var(--tw-prose-bold);
  font-weight: 600;
}

.block-editor-block-list__layout :where(a strong):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: inherit;
}

.block-editor-block-list__layout :where(blockquote strong):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: inherit;
}

.block-editor-block-list__layout :where(thead th strong):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: inherit;
}

.block-editor-block-list__layout :where(ol):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}

.block-editor-block-list__layout :where(ol[type="A"]):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  list-style-type: upper-alpha;
}

.block-editor-block-list__layout :where(ol[type="a"]):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  list-style-type: lower-alpha;
}

.block-editor-block-list__layout :where(ol[type="A" s]):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  list-style-type: upper-alpha;
}

.block-editor-block-list__layout :where(ol[type="a" s]):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  list-style-type: lower-alpha;
}

.block-editor-block-list__layout :where(ol[type="I"]):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  list-style-type: upper-roman;
}

.block-editor-block-list__layout :where(ol[type="i"]):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  list-style-type: lower-roman;
}

.block-editor-block-list__layout :where(ol[type="I" s]):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  list-style-type: upper-roman;
}

.block-editor-block-list__layout :where(ol[type="i" s]):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  list-style-type: lower-roman;
}

.block-editor-block-list__layout :where(ol[type="1"]):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  list-style-type: decimal;
}

.block-editor-block-list__layout :where(ul):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}

.block-editor-block-list__layout :where(ol > li):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *))::marker {
  font-weight: 400;
  color: var(--tw-prose-counters);
}

.block-editor-block-list__layout :where(ul > li):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *))::marker {
  color: var(--tw-prose-bullets);
}

.block-editor-block-list__layout :where(dt):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.25em;
}

.block-editor-block-list__layout :where(hr):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  border-color: var(--tw-prose-hr);
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
  border-bottom: none;
}

.block-editor-block-list__layout :where(blockquote):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-inline-start-width: 0.25rem;
  border-inline-start-color: var(--tw-prose-quote-borders);
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1em;
  border-left-style: solid;
}

.block-editor-block-list__layout :where(blockquote p:first-of-type):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *))::before {
  content: open-quote;
}

.block-editor-block-list__layout :where(blockquote p:last-of-type):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *))::after {
  content: close-quote;
}

.block-editor-block-list__layout :where(h1):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}

.block-editor-block-list__layout :where(h1 strong):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  font-weight: 900;
  color: inherit;
}

.block-editor-block-list__layout :where(h2):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}

.block-editor-block-list__layout :where(h2 strong):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  font-weight: 800;
  color: inherit;
}

.block-editor-block-list__layout :where(h3):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}

.block-editor-block-list__layout :where(h3 strong):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  font-weight: 700;
  color: inherit;
}

.block-editor-block-list__layout :where(h4):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.5;
}

.block-editor-block-list__layout :where(h4 strong):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  font-weight: 700;
  color: inherit;
}

.block-editor-block-list__layout :where(img):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.block-editor-block-list__layout :where(picture):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  display: block;
  margin-top: 2em;
  margin-bottom: 2em;
}

.block-editor-block-list__layout :where(video):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.block-editor-block-list__layout :where(kbd):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  font-weight: 500;
  font-family: inherit;
  color: var(--tw-prose-kbd);
  box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
  font-size: 0.875em;
  border-radius: 0.3125rem;
  padding-top: 0.1875em;
  padding-inline-end: 0.375em;
  padding-bottom: 0.1875em;
  padding-inline-start: 0.375em;
}

.block-editor-block-list__layout :where(code):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875em;
}

.block-editor-block-list__layout :where(code):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *))::before {
  content: "`";
}

.block-editor-block-list__layout :where(code):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *))::after {
  content: "`";
}

.block-editor-block-list__layout :where(a code):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: inherit;
}

.block-editor-block-list__layout :where(h1 code):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: inherit;
}

.block-editor-block-list__layout :where(h2 code):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: inherit;
  font-size: 0.875em;
}

.block-editor-block-list__layout :where(h3 code):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: inherit;
  font-size: 0.9em;
}

.block-editor-block-list__layout :where(h4 code):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: inherit;
}

.block-editor-block-list__layout :where(blockquote code):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: inherit;
}

.block-editor-block-list__layout :where(thead th code):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: inherit;
}

.block-editor-block-list__layout :where(pre):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  padding-inline-end: 1.1428571em;
  padding-bottom: 0.8571429em;
  padding-inline-start: 1.1428571em;
}

.block-editor-block-list__layout :where(pre code):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}

.block-editor-block-list__layout :where(pre code):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *))::before {
  content: none;
}

.block-editor-block-list__layout :where(pre code):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *))::after {
  content: none;
}

.block-editor-block-list__layout :where(table):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  width: 100%;
  table-layout: auto;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
}

.block-editor-block-list__layout :where(thead):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders);
}

.block-editor-block-list__layout :where(thead th):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}

.block-editor-block-list__layout :where(tbody tr):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-td-borders);
}

.block-editor-block-list__layout :where(tbody tr:last-child):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  border-bottom-width: 0;
}

.block-editor-block-list__layout :where(tbody td):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  vertical-align: baseline;
}

.block-editor-block-list__layout :where(tfoot):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders);
}

.block-editor-block-list__layout :where(tfoot td):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  vertical-align: top;
}

.block-editor-block-list__layout :where(th, td):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  text-align: start;
}

.block-editor-block-list__layout :where(figure > *):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.block-editor-block-list__layout :where(figcaption):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: var(--tw-prose-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}

.block-editor-block-list__layout {
  --tw-prose-body: #404040;
  --tw-prose-headings: #171717;
  --tw-prose-lead: #525252;
  --tw-prose-links: #b91c1c;
  --tw-prose-bold: #171717;
  --tw-prose-counters: #737373;
  --tw-prose-bullets: #d4d4d4;
  --tw-prose-hr: #e5e5e5;
  --tw-prose-quotes: #171717;
  --tw-prose-quote-borders: #e5e5e5;
  --tw-prose-captions: #737373;
  --tw-prose-kbd: #111827;
  --tw-prose-kbd-shadows: 17 24 39;
  --tw-prose-code: #171717;
  --tw-prose-pre-code: #e5e5e5;
  --tw-prose-pre-bg: #262626;
  --tw-prose-th-borders: #d4d4d4;
  --tw-prose-td-borders: #e5e5e5;
  --tw-prose-invert-body: #d4d4d4;
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: #a3a3a3;
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: #a3a3a3;
  --tw-prose-invert-bullets: #525252;
  --tw-prose-invert-hr: #404040;
  --tw-prose-invert-quotes: #f5f5f5;
  --tw-prose-invert-quote-borders: #404040;
  --tw-prose-invert-captions: #a3a3a3;
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: 255 255 255;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: #d4d4d4;
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: #525252;
  --tw-prose-invert-td-borders: #404040;
  font-size: 1rem;
  line-height: 1.75;
}

.block-editor-block-list__layout :where(picture > img):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.block-editor-block-list__layout :where(li):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.block-editor-block-list__layout :where(ol > li):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  padding-inline-start: 0.375em;
}

.block-editor-block-list__layout :where(ul > li):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  padding-inline-start: 0.375em;
}

.block-editor-block-list__layout :where(.block-editor-block-list__layout > ul > li p):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.block-editor-block-list__layout :where(.block-editor-block-list__layout > ul > li > p:first-child):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 1.25em;
}

.block-editor-block-list__layout :where(.block-editor-block-list__layout > ul > li > p:last-child):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-bottom: 1.25em;
}

.block-editor-block-list__layout :where(.block-editor-block-list__layout > ol > li > p:first-child):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 1.25em;
}

.block-editor-block-list__layout :where(.block-editor-block-list__layout > ol > li > p:last-child):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-bottom: 1.25em;
}

.block-editor-block-list__layout :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.block-editor-block-list__layout :where(dl):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.block-editor-block-list__layout :where(dd):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 0.5em;
  padding-inline-start: 1.625em;
}

.block-editor-block-list__layout :where(hr + *):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 0;
}

.block-editor-block-list__layout :where(h2 + *):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 0;
}

.block-editor-block-list__layout :where(h3 + *):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 0;
}

.block-editor-block-list__layout :where(h4 + *):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 0;
}

.block-editor-block-list__layout :where(thead th:first-child):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  padding-inline-start: 0;
}

.block-editor-block-list__layout :where(thead th:last-child):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  padding-inline-end: 0;
}

.block-editor-block-list__layout :where(tbody td, tfoot td):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  padding-top: 0.5714286em;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}

.block-editor-block-list__layout :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  padding-inline-start: 0;
}

.block-editor-block-list__layout :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  padding-inline-end: 0;
}

.block-editor-block-list__layout :where(figure):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.block-editor-block-list__layout :where(.block-editor-block-list__layout > :first-child):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-top: 0;
}

.block-editor-block-list__layout :where(.block-editor-block-list__layout > :last-child):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  margin-bottom: 0;
}

.block-editor-block-list__layout :where(blockquote > cite):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *)) {
  color: var(--tw-prose-body);
  font-style: normal;
  font-weight: 400;
}

.block-editor-block-list__layout :where(blockquote > cite):not(:where([class~="not-block-editor-block-list__layout"],[class~="not-block-editor-block-list__layout"] *))::before {
  content: "\2014";
}

/*-- ::::::::::::headings:::::::::::: */

.heading-secondary {
  font-size: 40px;
  font-weight: 700;
  line-height: 54px;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.heading-tertiary {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 2rem;
}

/*-- ::::::::::::navbar:::::::::::: */

.tooltip.nav-menu > .tooltip-arrow:before {
  visibility: hidden;
}

[role="tooltip"].nav-menu > [data-popper-arrow]:before {
  visibility: hidden;
}

[role="tooltip"].nav-menu > [data-popper-arrow]:after {
  visibility: hidden;
}

.nav-menu {
  visibility: hidden;
  position: absolute;
  top: 100%;
  right: 0px;
  z-index: 100;
  display: flex;
  max-height: 0px;
  width: 100%;
  min-width: 140px;
  max-width: 140px;
  transform-origin: top;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  flex-direction: column;
  justify-content: space-between;
  gap: 1rem;
  border-radius: 20px;
  border-top-right-radius: 0px;
  --tw-bg-opacity: 1;
  background-color: rgb(128 120 160 / var(--tw-bg-opacity, 1));
  padding-top: 17px;
  padding-bottom: 17px;
  padding-left: 20px;
  padding-right: 20px;
  opacity: 0;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

@media (min-width: 768px) {
  .nav-menu {
    visibility: visible;
    position: static;
    margin-top: 0.625rem;
    max-height: -moz-max-content;
    max-height: max-content;
    min-width: 100%;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
    background-color: transparent;
    padding-top: 0px;
    padding-bottom: 0px;
    padding-left: 0px;
    padding-right: 0px;
    opacity: 1;
  }
}

.nav-menu li a {
  position: relative;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.nav-menu li a.tooltip > .tooltip-arrow::before:before {
  content: var(--tw-content);
  visibility: hidden;
}

.nav-menu li a[role="tooltip"] > [data-popper-arrow]::before:before {
  content: var(--tw-content);
  visibility: hidden;
}

.nav-menu li a[role="tooltip"] > [data-popper-arrow]::before:after {
  content: var(--tw-content);
  visibility: hidden;
}

.nav-menu li a::before {
  visibility: hidden;
  position: absolute;
  left: 0px;
  right: 0px;
  top: 100%;
  display: block;
  height: 0.125rem;
  width: 0px;
  transform-origin: left;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  opacity: 0;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  --tw-content: '';
  content: var(--tw-content);
}

.nav-menu li a:hover::before {
  visibility: visible;
  width: 100%;
  content: var(--tw-content);
  opacity: 1;
}

@media (min-width: 768px) {
  .nav-menu li a {
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .nav-menu li a {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

.nav-menu li a {
  display: flex;
  width: -moz-fit-content;
  width: fit-content;
  align-items: center;
  gap: 0.375rem;
  text-transform: uppercase;
}

@media (min-width: 1024px) {
  .nav-menu li a {
    gap: 0.625rem;
  }
}

/*-- ::::::::::::footer:::::::::::: */

.footer-services li a {
  display: inline-block;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 2;
  --tw-text-opacity: 1;
  color: rgb(199 195 213 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.footer-services li a:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  text-decoration-line: underline;
}

@media (min-width: 640px) {
  .footer-services li a {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.footer-about li a {
  display: inline-block;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(199 195 213 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.footer-about li a:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  text-decoration-line: underline;
}

@media (min-width: 768px) {
  .footer-about li {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
  }
}

/*-- ::::::::::::custom css:::::::::::: */

.btn-primary {
  display: flex;
  width: -moz-fit-content;
  width: fit-content;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
  border-radius: 10px;
  --tw-bg-opacity: 1;
  background-color: rgb(254 28 166 / var(--tw-bg-opacity, 1));
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 30px;
  padding-right: 30px;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 700;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  --tw-shadow: 0px 0px 50px #FE1CA6;
  --tw-shadow-colored: 0px 0px 50px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.btn-primary:hover {
  --tw-translate-y: -2px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.btn-primary:active {
  --tw-translate-y: 1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@media (min-width: 768px) {
  .btn-primary {
    border-radius: 20px;
    font-size: 1.25rem;
    line-height: 1.75rem;
    line-height: 50px;
  }
}

.slider-dot {
  display: block;
  height: 0.625rem;
  width: 0.625rem;
  cursor: pointer;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(199 195 213 / var(--tw-bg-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

@media (min-width: 768px) {
  .slider-dot {
    height: 1rem;
    width: 1rem;
  }
}

.slider-dot.slider-dot-light {
  --tw-bg-opacity: 1;
  background-color: rgb(234 234 234 / var(--tw-bg-opacity, 1));
}

.slider-dot.slider-dot-active {
  width: 2rem;
  --tw-bg-opacity: 1;
  background-color: rgb(254 28 166 / var(--tw-bg-opacity, 1));
}

@media (min-width: 768px) {
  .slider-dot.slider-dot-active {
    width: 2.75rem;
  }
}

.hero-slide {
  opacity: 0;
}

.hero-slide .btn-primary,
    .hero-slide h1,
    .hero-slide p {
  opacity: 0;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
  transform: rotateX(90deg);
}

.hero-slide img {
  transform-origin: bottom;
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
}

.hero-slide.swiper-slide-active .btn-primary,
    .hero-slide.swiper-slide-active h1,
    .hero-slide.swiper-slide-active p {
  opacity: 1;
  transform: rotateX(0deg);
}

.hero-slide.swiper-slide-active img {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 1;
}

.nav-menu-active {
  visibility: visible;
  max-height: 600px;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 1;
}

.scroll-pink::-webkit-scrollbar-thumb {
  background-color: #fe1ca6;
  border-radius: 9999px;
}

.scroll-pink::-webkit-scrollbar {
  width: 15px;
}

.tooltip.viewText > .tooltip-arrow:before {
  visibility: hidden;
}

[role="tooltip"].viewText > [data-popper-arrow]:before {
  visibility: hidden;
}

[role="tooltip"].viewText > [data-popper-arrow]:after {
  visibility: hidden;
}

.viewText {
  visibility: hidden;
  max-height: 0px;
  transform-origin: top;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

@media (min-width: 640px) {
  .viewText {
    visibility: visible;
    max-height: -moz-max-content;
    max-height: max-content;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    opacity: 1;
  }
}

.viewTextActive {
  visibility: visible;
  max-height: 2000px;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  padding-bottom: 1.75rem;
  opacity: 1;
}

.text-overlay {
  position: absolute;
  bottom: 1rem;
  left: 0px;
  right: 0px;
  z-index: 20;
  display: block;
  height: 5rem;
  width: 100%;
  max-width: 530px;
  --tw-blur: blur(7px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

@media (min-width: 640px) {
  .text-overlay {
    display: none;
  }
}

.tooltip.text-overlayClosed > .tooltip-arrow:before {
  visibility: hidden;
}

[role="tooltip"].text-overlayClosed > [data-popper-arrow]:before {
  visibility: hidden;
}

[role="tooltip"].text-overlayClosed > [data-popper-arrow]:after {
  visibility: hidden;
}

.text-overlayClosed {
  visibility: hidden;
  max-height: 0px;
  opacity: 0;
}

.card-container {
  width: 100%;
}

@media (min-width: 768px) {
  .card-container {
    max-height: 1500px;
    overflow: hidden;
  }
}

.card-container-active {
  height: auto !important;
  max-height: none !important;
  overflow: visible !important;
}

.card-overlay {
  position: absolute;
  bottom: 0px;
  left: 0px;
  right: 0px;
  display: none;
  height: 11rem;
  width: 100%;
  background-image: linear-gradient(0deg, #FFF 0%, rgba(255, 255, 255, 0.00) 100%);
}

@media (min-width: 768px) {
  .card-overlay {
    display: block;
  }
}

.tooltip.cardoverlayClose > .tooltip-arrow:before {
  visibility: hidden;
}

[role="tooltip"].cardoverlayClose > [data-popper-arrow]:before {
  visibility: hidden;
}

[role="tooltip"].cardoverlayClose > [data-popper-arrow]:after {
  visibility: hidden;
}

.cardoverlayClose {
  visibility: hidden;
  height: 0px;
  opacity: 0;
}

.shadowTabActive {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(44 1 83 / var(--tw-bg-opacity, 1)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.shadowTabActive span {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(44 1 83 / var(--tw-text-opacity, 1)) !important;
}

.tabcontent {
  display: none;
}

.animation {
  animation: fadeEffect 1s;
}

@keyframes fadeEffect {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/*-- ::::::::::::wordpress solution:::::::::::: */

.wordpress-solution .active {
  --tw-bg-opacity: 1;
  background-color: rgb(199 195 213 / var(--tw-bg-opacity, 1));
}

.wordpress-solution .active p {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.wordpress-solution-content ul {
  list-style-type: disc;
  padding-left: 1rem;
  text-align: left;
}

.wordpress-solution-content ul li {
  font-size: 0.75rem;
  font-weight: 400;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(199 195 213 / var(--tw-text-opacity, 1));
}

@media (min-width: 768px) {
  .wordpress-solution-content ul li {
    font-size: 1rem;
    line-height: 2rem;
  }
}

.referenceCategory .swiper-slide {
  width: auto !important;
}

#input-search {
  position: relative;
  height: 2.5rem;
  width: 100%;
  border-radius: 10px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(234 234 234 / var(--tw-border-opacity, 1));
}

@media (min-width: 640px) {
  #input-search {
    max-width: 400px;
  }
}

.page-title,
.entry-title {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1.5rem;
  max-width: 40rem;
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 800;
  --tw-text-opacity: 1;
  color: rgb(23 23 23 / var(--tw-text-opacity, 1));
}

.page-content>*,
.entry-content>* {
  /* Content width from the `theme.json` file */
  margin-left: auto;
  margin-right: auto;
  max-width: 40rem;
}

.entry-content>.alignwide {
  /* Wide width from the `theme.json` file */
  max-width: 60rem;
}

.entry-content>.alignfull {
  max-width: none;
}

.entry-content>.alignleft {
  float: left;
  margin-right: 2rem;
}

.entry-content>.alignright {
  float: right;
  margin-left: 2rem;
}

/*-- :::::::::::: blog navigation :::::::::::: */

.uw-blog-index-navigation a.page-numbers {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.uw-blog-index-navigation span.page-numbers.current {
  --tw-text-opacity: 1;
  color: rgb(254 28 166 / var(--tw-text-opacity, 1));
}

.group[open] .uw-blog-index-navigation span.page-numbers.current {
  --tw-text-opacity: 1;
  color: rgb(254 28 166 / var(--tw-text-opacity, 1));
}

/*-- ::::::::::::Contact Form on Footer:::::::::::: */

.contact-content {
  position: relative;
  z-index: 40;
  margin-top: 1.75rem;
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: flex-start;
  gap: 1.25rem;
}

@media (min-width: 768px) {
  .contact-content {
    margin-top: 3.5rem;
    flex-direction: row;
  }
}

.contact-content > .content {
  width: 100%;
}

@media (min-width: 768px) {
  .contact-content > .content {
    max-width: 300px;
  }
}

.contact-content > .textarea {
  height: 100%;
  width: 100%;
}

@media (min-width: 768px) {
  .contact-content > .textarea {
    max-width: 850px;
  }
}

.info-button {
  position: relative;
  z-index: 40;
  display: flex;
  width: 100%;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.25rem;
  padding-left: 2.5rem;
  padding-right: 2.5rem;
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

@media (min-width: 768px) {
  .info-button {
    --tw-translate-y: 2.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    flex-direction: row;
    justify-content: space-between;
    padding-top: 0px;
    padding-bottom: 0px;
  }
}

@media (min-width: 1024px) {
  .info-button {
    padding-left: 122px;
    padding-right: 122px;
  }
}

@media (min-width: 1280px) {
  .info-button {
    flex-wrap: nowrap;
    align-items: center;
  }
}

@media (min-width: 1536px) {
  .info-button {
    padding-left: 0px;
    padding-right: 0px;
  }
}

.info-button > div {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.25rem;
}

@media (min-width: 640px) {
  .info-button > div {
    flex-direction: row;
  }
}

.info-button > a {
  margin-left: auto;
  margin-right: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(199 195 213 / var(--tw-text-opacity, 1));
}

@media (min-width: 640px) {
  .info-button > a {
    justify-content: flex-start;
  }
}

@media (min-width: 768px) {
  .info-button > a {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.submit-button {
  order: -9999;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  height: 2.5rem;
  width: 100%;
  max-width: 211px;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
  border-radius: 10px;
  --tw-bg-opacity: 1;
  background-color: rgb(199 195 213 / var(--tw-bg-opacity, 1));
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 700;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

@media (min-width: 768px) {
  .submit-button {
    order: 9999;
    margin-left: 0px;
    margin-right: 0px;
    height: 62px;
    border-radius: 20px;
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

#fname, #lname, #mobile {
  margin-bottom: 1.25rem;
  display: block;
  height: 2.5rem;
  width: 100%;
  border-radius: 10px;
  border-bottom-width: 1px;
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  outline: 2px solid transparent;
  outline-offset: 2px;
}

#fname:focus, #lname:focus, #mobile:focus {
  --tw-border-opacity: 1;
  border-color: rgb(254 28 166 / var(--tw-border-opacity, 1));
}

#email {
  display: block;
  height: 2.5rem;
  width: 100%;
  border-radius: 10px;
  border-bottom-width: 1px;
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  outline: 2px solid transparent;
  outline-offset: 2px;
}

#email:focus {
  --tw-border-opacity: 1;
  border-color: rgb(254 28 166 / var(--tw-border-opacity, 1));
}

#message {
  display: block;
  min-height: 220px;
  width: 100%;
  border-radius: 10px;
  border-bottom-width: 1px;
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  outline: 2px solid transparent;
  outline-offset: 2px;
}

#message:focus {
  --tw-border-opacity: 1;
  border-color: rgb(254 28 166 / var(--tw-border-opacity, 1));
}

/*-- ::::::::::::Contact Us:::::::::::: */

.contact-us {
  margin-bottom: 1.25rem;
  width: 100%;
}

.contact-us-content {
  margin-bottom: 20px;
  display: grid;
  width: 100%;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 1.25rem;
}

@media (min-width: 1024px) {
  .contact-us-content {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Úprava vstupních polí */

#first-name, #last-name, #contact-email, #tel {
  height: 3rem;
  width: 100%;
  border-radius: 10px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(234 234 234 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(24 1 45 / var(--tw-text-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

#first-name::-moz-placeholder, #last-name::-moz-placeholder, #contact-email::-moz-placeholder, #tel::-moz-placeholder {
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: rgb(24 1 45 / 0.6);
}

#first-name::placeholder, #last-name::placeholder, #contact-email::placeholder, #tel::placeholder {
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: rgb(24 1 45 / 0.6);
}

#first-name:focus, #last-name:focus, #contact-email:focus, #tel:focus {
  --tw-border-opacity: 1;
  border-color: rgb(254 28 166 / var(--tw-border-opacity, 1));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

/* Úprava textarea */

#contact-message {
  min-height: 160px;
  width: 100%;
  border-radius: 10px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(234 234 234 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(24 1 45 / var(--tw-text-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

#contact-message::-moz-placeholder {
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: rgb(24 1 45 / 0.6);
}

#contact-message::placeholder {
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: rgb(24 1 45 / 0.6);
}

#contact-message:focus {
  --tw-border-opacity: 1;
  border-color: rgb(254 28 166 / var(--tw-border-opacity, 1));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

/* Přidání hover efektu */

#first-name:hover, #last-name:hover, #contact-email:hover, #tel:hover, #contact-message:hover {
  border-color: rgb(199 195 213 / 0.3);
}

/* Úprava aktivního stavu */

#first-name:focus, #last-name:focus, #contact-email:focus, #tel:focus, #contact-message:focus {
  --tw-border-opacity: 1;
  border-color: rgb(254 28 166 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(24 1 45 / var(--tw-text-opacity, 1));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.contact-info-button {
  margin-top: 1rem;
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.25rem;
}

@media (min-width: 768px) {
  .contact-info-button {
    flex-direction: row;
    justify-content: space-between;
  }
}

.contact-info-button a {
  margin-left: auto;
  margin-right: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(199 195 213 / var(--tw-text-opacity, 1));
}

@media (min-width: 640px) {
  .contact-info-button a {
    justify-content: flex-start;
  }
}

@media (min-width: 768px) {
  .contact-info-button a {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.contact-submit-button {
  order: -9999;
  display: flex;
  height: 2.5rem;
  width: 100%;
  max-width: 315px;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
  border-radius: 10px;
  --tw-bg-opacity: 1;
  background-color: rgb(44 1 83 / var(--tw-bg-opacity, 1));
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 700;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

@media (min-width: 768px) {
  .contact-submit-button {
    order: 9999;
    height: 62px;
    border-radius: 20px;
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.grecaptcha-badge {
  visibility: hidden;
}

/*-- :::::::::::: Case Study :::::::::::: */

.case-study-form form {
  display: flex;
  flex-direction: row;
}

#email_case_study {
  position: relative;
  z-index: 40;
  height: 2.5rem;
  width: 100%;
  border-radius: 10px;
  border-bottom-width: 1px;
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(199 195 213 / var(--tw-text-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
}

#email_case_study::-moz-placeholder {
  color: currentColor;
}

#email_case_study::placeholder {
  color: currentColor;
}

#email_case_study:focus {
  --tw-border-opacity: 1;
  border-color: rgb(254 28 166 / var(--tw-border-opacity, 1));
}

@media (min-width: 768px) {
  #email_case_study {
    height: 70px;
    max-width: 450px;
    border-radius: 20px;
  }
}

.submit-case-study {
  display: flex;
  width: -moz-fit-content;
  width: fit-content;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
  border-radius: 10px;
  --tw-bg-opacity: 1;
  background-color: rgb(254 28 166 / var(--tw-bg-opacity, 1));
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 30px;
  padding-right: 30px;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 700;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  --tw-shadow: 0px 0px 50px #FE1CA6;
  --tw-shadow-colored: 0px 0px 50px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.submit-case-study:hover {
  --tw-translate-y: -2px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.submit-case-study:active {
  --tw-translate-y: 1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@media (min-width: 768px) {
  .submit-case-study {
    border-radius: 20px;
    font-size: 1.25rem;
    line-height: 1.75rem;
    line-height: 50px;
  }
}

.hero-slide .submit-case-study {
  opacity: 0;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
  transform: rotateX(90deg);
}

.hero-slide.swiper-slide-active .submit-case-study {
  opacity: 1;
  transform: rotateX(0deg);
}

.submit-case-study {
  margin-left: 1rem;
  height: 2.5rem;
  width: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
}

@media (min-width: 768px) {
  .submit-case-study {
    height: 70px;
    width: -moz-max-content;
    width: max-content;
  }
}

/**
 * This injects Tailwind's utility classes and any utility classes registered
 * by plugins, then adds custom utility classes.
 */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.-bottom-\[20\%\] {
  bottom: -20%;
}

.-bottom-\[37\%\] {
  bottom: -37%;
}

.-bottom-\[41\%\] {
  bottom: -41%;
}

.-left-1 {
  left: -0.25rem;
}

.-left-16 {
  left: -4rem;
}

.-right-7 {
  right: -1.75rem;
}

.-right-8 {
  right: -2rem;
}

.-top-10 {
  top: -2.5rem;
}

.-top-16 {
  top: -4rem;
}

.-top-20 {
  top: -5rem;
}

.-top-24 {
  top: -6rem;
}

.-top-5 {
  top: -1.25rem;
}

.-top-8 {
  top: -2rem;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-20 {
  bottom: 5rem;
}

.bottom-\[60px\] {
  bottom: 60px;
}

.bottom-full {
  bottom: 100%;
}

.left-0 {
  left: 0px;
}

.left-1\/2 {
  left: 50%;
}

.left-10 {
  left: 2.5rem;
}

.left-16 {
  left: 4rem;
}

.left-7 {
  left: 1.75rem;
}

.left-\[21px\] {
  left: 21px;
}

.left-\[8\%\] {
  left: 8%;
}

.right-0 {
  right: 0px;
}

.right-1\/2 {
  right: 50%;
}

.right-10 {
  right: 2.5rem;
}

.right-16 {
  right: 4rem;
}

.right-2 {
  right: 0.5rem;
}

.right-2\.5 {
  right: 0.625rem;
}

.right-20 {
  right: 5rem;
}

.right-5 {
  right: 1.25rem;
}

.right-60 {
  right: 15rem;
}

.right-7 {
  right: 1.75rem;
}

.right-\[8\%\] {
  right: 8%;
}

.top-0 {
  top: 0px;
}

.top-1\/2 {
  top: 50%;
}

.top-10 {
  top: 2.5rem;
}

.top-12 {
  top: 3rem;
}

.top-16 {
  top: 4rem;
}

.top-20 {
  top: 5rem;
}

.top-\[\.1rem\] {
  top: .1rem;
}

.top-\[112\%\] {
  top: 112%;
}

.top-\[20\%\] {
  top: 20%;
}

.top-\[26px\] {
  top: 26px;
}

.top-\[72\%\] {
  top: 72%;
}

.-z-10 {
  z-index: -10;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[100\] {
  z-index: 100;
}

.z-\[200\] {
  z-index: 200;
}

.z-\[500\] {
  z-index: 500;
}

.order-first {
  order: -9999;
}

.order-last {
  order: 9999;
}

.-m-12 {
  margin: -3rem;
}

.m-auto {
  margin: auto;
}

.-my-14 {
  margin-top: -3.5rem;
  margin-bottom: -3.5rem;
}

.mx-5 {
  margin-left: 1.25rem;
  margin-right: 1.25rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.my-14 {
  margin-top: 3.5rem;
  margin-bottom: 3.5rem;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-7 {
  margin-top: 1.75rem;
  margin-bottom: 1.75rem;
}

.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.-mb-16 {
  margin-bottom: -4rem;
}

.-mt-1 {
  margin-top: -0.25rem;
}

.-mt-24 {
  margin-top: -6rem;
}

.-mt-28 {
  margin-top: -7rem;
}

.-mt-32 {
  margin-top: -8rem;
}

.-mt-36 {
  margin-top: -9rem;
}

.-mt-4 {
  margin-top: -1rem;
}

.-mt-5 {
  margin-top: -1.25rem;
}

.-mt-8 {
  margin-top: -2rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-1\.5 {
  margin-bottom: 0.375rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-11 {
  margin-bottom: 2.75rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-14 {
  margin-bottom: 3.5rem;
}

.mb-15 {
  margin-bottom: 60px;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-2\.5 {
  margin-bottom: 0.625rem;
}

.mb-24 {
  margin-bottom: 6rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-3\.5 {
  margin-bottom: 0.875rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-7 {
  margin-bottom: 1.75rem;
}

.mb-7\.5 {
  margin-bottom: 30px;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-9 {
  margin-bottom: 2.25rem;
}

.mb-\[22px\] {
  margin-bottom: 22px;
}

.mb-\[25px\] {
  margin-bottom: 25px;
}

.mb-\[26px\] {
  margin-bottom: 26px;
}

.mb-\[31px\] {
  margin-bottom: 31px;
}

.mb-\[38px\] {
  margin-bottom: 38px;
}

.mb-\[46px\] {
  margin-bottom: 46px;
}

.mb-\[50px\] {
  margin-bottom: 50px;
}

.mb-\[54px\] {
  margin-bottom: 54px;
}

.mb-\[59px\] {
  margin-bottom: 59px;
}

.mb-\[69px\] {
  margin-bottom: 69px;
}

.mb-\[78px\] {
  margin-bottom: 78px;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-auto {
  margin-left: auto;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-1\.5 {
  margin-top: 0.375rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-14 {
  margin-top: 3.5rem;
}

.mt-15 {
  margin-top: 60px;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-2\.5 {
  margin-top: 0.625rem;
}

.mt-20 {
  margin-top: 5rem;
}

.mt-28 {
  margin-top: 7rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-3\.5 {
  margin-top: 0.875rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-7 {
  margin-top: 1.75rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-9 {
  margin-top: 2.25rem;
}

.mt-\[-1\.5rem\] {
  margin-top: -1.5rem;
}

.mt-\[19px\] {
  margin-top: 19px;
}

.mt-\[21px\] {
  margin-top: 21px;
}

.mt-\[22px\] {
  margin-top: 22px;
}

.mt-\[26px\] {
  margin-top: 26px;
}

.mt-\[34px\] {
  margin-top: 34px;
}

.mt-\[46px\] {
  margin-top: 46px;
}

.mt-\[60px\] {
  margin-top: 60px;
}

.mt-\[74px\] {
  margin-top: 74px;
}

.mt-auto {
  margin-top: auto;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.hidden {
  display: none;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.h-0 {
  height: 0px;
}

.h-0\.5 {
  height: 0.125rem;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-16 {
  height: 4rem;
}

.h-24 {
  height: 6rem;
}

.h-32 {
  height: 8rem;
}

.h-4 {
  height: 1rem;
}

.h-48 {
  height: 12rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-7\.5 {
  height: 30px;
}

.h-72 {
  height: 18rem;
}

.h-8 {
  height: 2rem;
}

.h-9 {
  height: 2.25rem;
}

.h-96 {
  height: 24rem;
}

.h-\[100px\] {
  height: 100px;
}

.h-\[160px\] {
  height: 160px;
}

.h-\[170px\] {
  height: 170px;
}

.h-\[197px\] {
  height: 197px;
}

.h-\[1px\] {
  height: 1px;
}

.h-\[240px\] {
  height: 240px;
}

.h-\[287px\] {
  height: 287px;
}

.h-\[300px\] {
  height: 300px;
}

.h-\[30px\] {
  height: 30px;
}

.h-\[340px\] {
  height: 340px;
}

.h-\[3\] {
  height: 3;
}

.h-\[42px\] {
  height: 42px;
}

.h-\[467px\] {
  height: 467px;
}

.h-\[50\%\] {
  height: 50%;
}

.h-\[500px\] {
  height: 500px;
}

.h-\[50px\] {
  height: 50px;
}

.h-\[570px\] {
  height: 570px;
}

.h-\[5px\] {
  height: 5px;
}

.h-\[62px\] {
  height: 62px;
}

.h-\[752px\] {
  height: 752px;
}

.h-\[90px\] {
  height: 90px;
}

.h-\[calc\(100\%-40px\)\] {
  height: calc(100% - 40px);
}

.h-auto {
  height: auto;
}

.h-full {
  height: 100%;
}

.h-max {
  height: -moz-max-content;
  height: max-content;
}

.h-screen {
  height: 100vh;
}

.max-h-0 {
  max-height: 0px;
}

.max-h-\[100px\] {
  max-height: 100px;
}

.max-h-\[300px\] {
  max-height: 300px;
}

.min-h-40 {
  min-height: 10rem;
}

.min-h-\[10\.5rem\] {
  min-height: 10.5rem;
}

.min-h-\[1030px\] {
  min-height: 1030px;
}

.min-h-\[160px\] {
  min-height: 160px;
}

.min-h-\[200px\] {
  min-height: 200px;
}

.min-h-\[270px\] {
  min-height: 270px;
}

.min-h-\[318px\] {
  min-height: 318px;
}

.min-h-\[342px\] {
  min-height: 342px;
}

.min-h-\[420px\] {
  min-height: 420px;
}

.min-h-\[580px\] {
  min-height: 580px;
}

.min-h-\[5rem\] {
  min-height: 5rem;
}

.min-h-\[6rem\] {
  min-height: 6rem;
}

.min-h-\[750px\] {
  min-height: 750px;
}

.w-0\.5 {
  width: 0.125rem;
}

.w-1\.5 {
  width: 0.375rem;
}

.w-1\/2 {
  width: 50%;
}

.w-1\/3 {
  width: 33.333333%;
}

.w-10 {
  width: 2.5rem;
}

.w-11\/12 {
  width: 91.666667%;
}

.w-12 {
  width: 3rem;
}

.w-16 {
  width: 4rem;
}

.w-24 {
  width: 6rem;
}

.w-28 {
  width: 7rem;
}

.w-3\/4 {
  width: 75%;
}

.w-32 {
  width: 8rem;
}

.w-4 {
  width: 1rem;
}

.w-4\/5 {
  width: 80%;
}

.w-5 {
  width: 1.25rem;
}

.w-5\/6 {
  width: 83.333333%;
}

.w-64 {
  width: 16rem;
}

.w-7\.5 {
  width: 30px;
}

.w-8 {
  width: 2rem;
}

.w-\[150px\] {
  width: 150px;
}

.w-\[1px\] {
  width: 1px;
}

.w-\[200px\] {
  width: 200px;
}

.w-\[26px\] {
  width: 26px;
}

.w-\[34px\] {
  width: 34px;
}

.w-\[350px\] {
  width: 350px;
}

.w-\[50px\] {
  width: 50px;
}

.w-\[59px\] {
  width: 59px;
}

.w-\[5px\] {
  width: 5px;
}

.w-\[60\%\] {
  width: 60%;
}

.w-\[70\%\] {
  width: 70%;
}

.w-\[90px\] {
  width: 90px;
}

.w-\[926px\] {
  width: 926px;
}

.w-\[99\%\] {
  width: 99%;
}

.w-\[99vw\] {
  width: 99vw;
}

.w-\[calc\(100\%\+400px\)\] {
  width: calc(100% + 400px);
}

.w-\[calc\(50\%-12px\)\] {
  width: calc(50% - 12px);
}

.w-auto {
  width: auto;
}

.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}

.w-full {
  width: 100%;
}

.w-max {
  width: -moz-max-content;
  width: max-content;
}

.min-w-\[200px\] {
  min-width: 200px;
}

.min-w-\[250px\] {
  min-width: 250px;
}

.min-w-\[300px\] {
  min-width: 300px;
}

.min-w-\[320px\] {
  min-width: 320px;
}

.min-w-full {
  min-width: 100%;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-5xl {
  max-width: 64rem;
}

.max-w-\[1000px\] {
  max-width: 1000px;
}

.max-w-\[100px\] {
  max-width: 100px;
}

.max-w-\[1050px\] {
  max-width: 1050px;
}

.max-w-\[1160px\] {
  max-width: 1160px;
}

.max-w-\[1168px\] {
  max-width: 1168px;
}

.max-w-\[1170px\] {
  max-width: 1170px;
}

.max-w-\[1172px\] {
  max-width: 1172px;
}

.max-w-\[1178px\] {
  max-width: 1178px;
}

.max-w-\[1184px\] {
  max-width: 1184px;
}

.max-w-\[120px\] {
  max-width: 120px;
}

.max-w-\[1210px\] {
  max-width: 1210px;
}

.max-w-\[1230px\] {
  max-width: 1230px;
}

.max-w-\[1250px\] {
  max-width: 1250px;
}

.max-w-\[1280px\] {
  max-width: 1280px;
}

.max-w-\[1330px\] {
  max-width: 1330px;
}

.max-w-\[135px\] {
  max-width: 135px;
}

.max-w-\[1440px\] {
  max-width: 1440px;
}

.max-w-\[1480px\] {
  max-width: 1480px;
}

.max-w-\[170px\] {
  max-width: 170px;
}

.max-w-\[198px\] {
  max-width: 198px;
}

.max-w-\[200px\] {
  max-width: 200px;
}

.max-w-\[203px\] {
  max-width: 203px;
}

.max-w-\[205px\] {
  max-width: 205px;
}

.max-w-\[212px\] {
  max-width: 212px;
}

.max-w-\[213px\] {
  max-width: 213px;
}

.max-w-\[222px\] {
  max-width: 222px;
}

.max-w-\[240px\] {
  max-width: 240px;
}

.max-w-\[242px\] {
  max-width: 242px;
}

.max-w-\[250px\] {
  max-width: 250px;
}

.max-w-\[264px\] {
  max-width: 264px;
}

.max-w-\[280px\] {
  max-width: 280px;
}

.max-w-\[300px\] {
  max-width: 300px;
}

.max-w-\[302px\] {
  max-width: 302px;
}

.max-w-\[303px\] {
  max-width: 303px;
}

.max-w-\[327px\] {
  max-width: 327px;
}

.max-w-\[350px\] {
  max-width: 350px;
}

.max-w-\[356px\] {
  max-width: 356px;
}

.max-w-\[400px\] {
  max-width: 400px;
}

.max-w-\[417px\] {
  max-width: 417px;
}

.max-w-\[430px\] {
  max-width: 430px;
}

.max-w-\[450px\] {
  max-width: 450px;
}

.max-w-\[48\%\] {
  max-width: 48%;
}

.max-w-\[500px\] {
  max-width: 500px;
}

.max-w-\[535px\] {
  max-width: 535px;
}

.max-w-\[540px\] {
  max-width: 540px;
}

.max-w-\[550px\] {
  max-width: 550px;
}

.max-w-\[60vw\] {
  max-width: 60vw;
}

.max-w-\[620px\] {
  max-width: 620px;
}

.max-w-\[70\%\] {
  max-width: 70%;
}

.max-w-\[700px\] {
  max-width: 700px;
}

.max-w-\[710px\] {
  max-width: 710px;
}

.max-w-\[750px\] {
  max-width: 750px;
}

.max-w-\[770px\] {
  max-width: 770px;
}

.max-w-\[780px\] {
  max-width: 780px;
}

.max-w-\[788px\] {
  max-width: 788px;
}

.max-w-\[80\%\] {
  max-width: 80%;
}

.max-w-\[80vw\] {
  max-width: 80vw;
}

.max-w-\[95vw\] {
  max-width: 95vw;
}

.max-w-full {
  max-width: 100%;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-screen-2xl {
  max-width: 1536px;
}

.max-w-screen-lg {
  max-width: 1024px;
}

.max-w-screen-sm {
  max-width: 640px;
}

.max-w-screen-xl {
  max-width: 1280px;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xl {
  max-width: 36rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-shrink {
  flex-shrink: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

.grow {
  flex-grow: 1;
}

.origin-top {
  transform-origin: top;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-\[200px\] {
  --tw-translate-x: -200px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1 {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-full {
  --tw-translate-y: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1\/2 {
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-2\.5 {
  --tw-translate-y: 0.625rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-full {
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform-none {
  transform: none;
}

.cursor-default {
  cursor: default;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.resize {
  resize: both;
}

.list-inside {
  list-style-position: inside;
}

.list-disc {
  list-style-type: disc;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.grid-cols-\[max-content\2c 1fr\] {
  grid-template-columns: max-content 1fr;
}

.grid-cols-\[repeat\(3\2c 240px\)\] {
  grid-template-columns: repeat(3,240px);
}

.grid-cols-\[repeat\(4\2c 240px\)\] {
  grid-template-columns: repeat(4,240px);
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.gap-0\.5 {
  gap: 0.125rem;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-1\.5 {
  gap: 0.375rem;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-12 {
  gap: 3rem;
}

.gap-14 {
  gap: 3.5rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-2\.5 {
  gap: 0.625rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-3\.5 {
  gap: 0.875rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-7 {
  gap: 1.75rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-\[18px\] {
  gap: 18px;
}

.gap-\[22px\] {
  gap: 22px;
}

.gap-\[5px\] {
  gap: 5px;
}

.gap-x-6 {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}

.gap-y-0 {
  row-gap: 0px;
}

.gap-y-10 {
  row-gap: 2.5rem;
}

.gap-y-16 {
  row-gap: 4rem;
}

.gap-y-2 {
  row-gap: 0.5rem;
}

.gap-y-3 {
  row-gap: 0.75rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.gap-y-6 {
  row-gap: 1.5rem;
}

.gap-y-8 {
  row-gap: 2rem;
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-\[5px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(5px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(5px * var(--tw-space-y-reverse));
}

.self-end {
  align-self: flex-end;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-visible {
  overflow: visible;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-3xl {
  border-radius: 1.5rem;
}

.rounded-\[100px\] {
  border-radius: 100px;
}

.rounded-\[10px\] {
  border-radius: 10px;
}

.rounded-\[18px\] {
  border-radius: 18px;
}

.rounded-\[20px\] {
  border-radius: 20px;
}

.rounded-\[50\%\] {
  border-radius: 50%;
}

.rounded-\[50px\] {
  border-radius: 50px;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-l-lg {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.rounded-r-lg {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

.rounded-t-3xl {
  border-top-left-radius: 1.5rem;
  border-top-right-radius: 1.5rem;
}

.rounded-t-\[50px\] {
  border-top-left-radius: 50px;
  border-top-right-radius: 50px;
}

.rounded-t-full {
  border-top-left-radius: 9999px;
  border-top-right-radius: 9999px;
}

.rounded-br-\[150px\] {
  border-bottom-right-radius: 150px;
}

.rounded-tl-\[150px\] {
  border-top-left-radius: 150px;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0px;
}

.border-2 {
  border-width: 2px;
}

.border-\[10px\] {
  border-width: 10px;
}

.border-\[20px\] {
  border-width: 20px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-b-4 {
  border-bottom-width: 4px;
}

.border-l {
  border-left-width: 1px;
}

.border-l-4 {
  border-left-width: 4px;
}

.border-l-\[1px\] {
  border-left-width: 1px;
}

.border-l-\[20px\] {
  border-left-width: 20px;
}

.border-r {
  border-right-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-dashed {
  border-style: dashed;
}

.border-none {
  border-style: none;
}

.border-\[\#EAEAEA\] {
  --tw-border-opacity: 1;
  border-color: rgb(234 234 234 / var(--tw-border-opacity, 1));
}

.border-\[\#d9d9d9\] {
  --tw-border-opacity: 1;
  border-color: rgb(217 217 217 / var(--tw-border-opacity, 1));
}

.border-\[\#eaeaea\] {
  --tw-border-opacity: 1;
  border-color: rgb(234 234 234 / var(--tw-border-opacity, 1));
}

.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(28 100 242 / var(--tw-border-opacity, 1));
}

.border-blue-700 {
  --tw-border-opacity: 1;
  border-color: rgb(26 86 219 / var(--tw-border-opacity, 1));
}

.border-dark-purple {
  --tw-border-opacity: 1;
  border-color: rgb(44 1 83 / var(--tw-border-opacity, 1));
}

.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.border-light-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(234 234 234 / var(--tw-border-opacity, 1));
}

.border-light-purple {
  --tw-border-opacity: 1;
  border-color: rgb(199 195 213 / var(--tw-border-opacity, 1));
}

.border-purple-400 {
  --tw-border-opacity: 1;
  border-color: rgb(172 148 250 / var(--tw-border-opacity, 1));
}

.border-purple-700 {
  --tw-border-opacity: 1;
  border-color: rgb(108 43 217 / var(--tw-border-opacity, 1));
}

.border-theme-pink {
  --tw-border-opacity: 1;
  border-color: rgb(254 28 166 / var(--tw-border-opacity, 1));
}

.border-transparent {
  border-color: transparent;
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-l-\[\#eaeaea\] {
  --tw-border-opacity: 1;
  border-left-color: rgb(234 234 234 / var(--tw-border-opacity, 1));
}

.border-t-\[\#D9D9D9\] {
  --tw-border-opacity: 1;
  border-top-color: rgb(217 217 217 / var(--tw-border-opacity, 1));
}

.border-t-\[\#d9d9d9\] {
  --tw-border-opacity: 1;
  border-top-color: rgb(217 217 217 / var(--tw-border-opacity, 1));
}

.border-t-\[\#eaeaea\] {
  --tw-border-opacity: 1;
  border-top-color: rgb(234 234 234 / var(--tw-border-opacity, 1));
}

.bg-\[\#0E001B\] {
  --tw-bg-opacity: 1;
  background-color: rgb(14 0 27 / var(--tw-bg-opacity, 1));
}

.bg-\[\#18012D\] {
  --tw-bg-opacity: 1;
  background-color: rgb(24 1 45 / var(--tw-bg-opacity, 1));
}

.bg-\[\#1b0259\] {
  --tw-bg-opacity: 1;
  background-color: rgb(27 2 89 / var(--tw-bg-opacity, 1));
}

.bg-\[\#4c03ef\] {
  --tw-bg-opacity: 1;
  background-color: rgb(76 3 239 / var(--tw-bg-opacity, 1));
}

.bg-\[\#8078A0\] {
  --tw-bg-opacity: 1;
  background-color: rgb(128 120 160 / var(--tw-bg-opacity, 1));
}

.bg-\[\#D9D9D9\] {
  --tw-bg-opacity: 1;
  background-color: rgb(217 217 217 / var(--tw-bg-opacity, 1));
}

.bg-\[\#d9d9d9\] {
  --tw-bg-opacity: 1;
  background-color: rgb(217 217 217 / var(--tw-bg-opacity, 1));
}

.bg-\[\#eaeaea\] {
  --tw-bg-opacity: 1;
  background-color: rgb(234 234 234 / var(--tw-bg-opacity, 1));
}

.bg-black\/30 {
  background-color: rgb(0 0 0 / 0.3);
}

.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}

.bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(26 86 219 / var(--tw-bg-opacity, 1));
}

.bg-dark-purple {
  --tw-bg-opacity: 1;
  background-color: rgb(44 1 83 / var(--tw-bg-opacity, 1));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.bg-light-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 234 234 / var(--tw-bg-opacity, 1));
}

.bg-light-purple {
  --tw-bg-opacity: 1;
  background-color: rgb(199 195 213 / var(--tw-bg-opacity, 1));
}

.bg-theme-blue {
  --tw-bg-opacity: 1;
  background-color: rgb(76 3 239 / var(--tw-bg-opacity, 1));
}

.bg-theme-blue\/10 {
  background-color: rgb(76 3 239 / 0.1);
}

.bg-theme-pink {
  --tw-bg-opacity: 1;
  background-color: rgb(254 28 166 / var(--tw-bg-opacity, 1));
}

.bg-theme-pink\/10 {
  background-color: rgb(254 28 166 / 0.1);
}

.bg-theme-purple {
  --tw-bg-opacity: 1;
  background-color: rgb(24 1 45 / var(--tw-bg-opacity, 1));
}

.bg-theme-purple\/10 {
  background-color: rgb(24 1 45 / 0.1);
}

.bg-theme-purple\/50 {
  background-color: rgb(24 1 45 / 0.5);
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/50 {
  background-color: rgb(255 255 255 / 0.5);
}

.bg-white\/95 {
  background-color: rgb(255 255 255 / 0.95);
}

.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}

.bg-opacity-75 {
  --tw-bg-opacity: 0.75;
}

.bg-\[url\(\'assets\/images\/report_cards\/planet\.png\'\)\] {
  background-image: url('assets/images/report_cards/planet.png');
}

.bg-bgborder {
  background-image: linear-gradient(270deg, rgba(255,255,255,0),rgba(254, 28, 166, 1),rgba(255,255,255,0));
}

.bg-bgborderTwo {
  background-image: linear-gradient(270deg, rgba(255,255,255,0),rgba(136, 136, 136, 0.5),rgba(255,255,255,0));
}

.bg-box {
  background-image: linear-gradient(0deg, #FFF 0%, rgba(255, 255, 255, 0.00) 100%);
}

.bg-contactModal {
  background-image: url("./assets/images/sprava/contact-bg.svg");
}

.bg-footer {
  background-image: linear-gradient(180deg, #1B0259 0%, #18012D 100%);
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-hero {
  background-image: url("./assets/images/home/<USER>");
}

.bg-number {
  background-image: linear-gradient(180deg, #4C03EF 0%, #1B0259 100%);
}

.bg-plants {
  background-image: url("./assets/images/home/<USER>");
}

.bg-purple-gradient {
  background-image: linear-gradient(180deg, #2C0154 0%, #18012D 100%);
}

.from-\[\#2A0151\]\/60 {
  --tw-gradient-from: rgb(42 1 81 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(42 1 81 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-theme-purple {
  --tw-gradient-from: #18012D var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(24 1 45 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-theme-purple\/10 {
  --tw-gradient-from: rgb(24 1 45 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(24 1 45 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-dark-purple {
  --tw-gradient-to: rgb(44 1 83 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #2c0153 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.to-\[\#2A0151\] {
  --tw-gradient-to: #2A0151 var(--tw-gradient-to-position);
}

.to-theme-blue\/10 {
  --tw-gradient-to: rgb(76 3 239 / 0.1) var(--tw-gradient-to-position);
}

.to-theme-purple {
  --tw-gradient-to: #18012D var(--tw-gradient-to-position);
}

.bg-cover {
  background-size: cover;
}

.bg-bottom {
  background-position: bottom;
}

.bg-top {
  background-position: top;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.fill-dark-purple {
  fill: #2c0153;
}

.fill-light-purple {
  fill: #C7C3D5;
}

.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.object-bottom {
  -o-object-position: bottom;
     object-position: bottom;
}

.object-center {
  -o-object-position: center;
     object-position: center;
}

.p-1 {
  padding: 0.25rem;
}

.p-12 {
  padding: 3rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-2\.5 {
  padding: 0.625rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.p-\[1px\] {
  padding: 1px;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-16 {
  padding-left: 4rem;
  padding-right: 4rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-20 {
  padding-left: 5rem;
  padding-right: 5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-7 {
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.px-9 {
  padding-left: 2.25rem;
  padding-right: 2.25rem;
}

.px-\[133px\] {
  padding-left: 133px;
  padding-right: 133px;
}

.px-\[30px\] {
  padding-left: 30px;
  padding-right: 30px;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-14 {
  padding-top: 3.5rem;
  padding-bottom: 3.5rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.py-24 {
  padding-top: 6rem;
  padding-bottom: 6rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-40 {
  padding-top: 10rem;
  padding-bottom: 10rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-9 {
  padding-top: 2.25rem;
  padding-bottom: 2.25rem;
}

.py-\[50px\] {
  padding-top: 50px;
  padding-bottom: 50px;
}

.pb-0\.5 {
  padding-bottom: 0.125rem;
}

.pb-1 {
  padding-bottom: 0.25rem;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-11 {
  padding-bottom: 2.75rem;
}

.pb-12 {
  padding-bottom: 3rem;
}

.pb-14 {
  padding-bottom: 3.5rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-2\.5 {
  padding-bottom: 0.625rem;
}

.pb-24 {
  padding-bottom: 6rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pb-3\.5 {
  padding-bottom: 0.875rem;
}

.pb-40 {
  padding-bottom: 10rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pb-7 {
  padding-bottom: 1.75rem;
}

.pb-8 {
  padding-bottom: 2rem;
}

.pb-9 {
  padding-bottom: 2.25rem;
}

.pb-\[53px\] {
  padding-bottom: 53px;
}

.pb-\[6rem\] {
  padding-bottom: 6rem;
}

.pb-\[8rem\] {
  padding-bottom: 8rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-48 {
  padding-left: 12rem;
}

.pl-5 {
  padding-left: 1.25rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pr-12 {
  padding-right: 3rem;
}

.pr-2\.5 {
  padding-right: 0.625rem;
}

.pr-40 {
  padding-right: 10rem;
}

.pt-1 {
  padding-top: 0.25rem;
}

.pt-10 {
  padding-top: 2.5rem;
}

.pt-12 {
  padding-top: 3rem;
}

.pt-14 {
  padding-top: 3.5rem;
}

.pt-16 {
  padding-top: 4rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-2\.5 {
  padding-top: 0.625rem;
}

.pt-20 {
  padding-top: 5rem;
}

.pt-24 {
  padding-top: 6rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-3\.5 {
  padding-top: 0.875rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-8 {
  padding-top: 2rem;
}

.pt-\[38px\] {
  padding-top: 38px;
}

.pt-\[50px\] {
  padding-top: 50px;
}

.pt-\[60px\] {
  padding-top: 60px;
}

.pt-\[65px\] {
  padding-top: 65px;
}

.pt-\[66px\] {
  padding-top: 66px;
}

.pt-\[75px\] {
  padding-top: 75px;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-justify {
  text-align: justify;
}

.text-start {
  text-align: start;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-\[100px\] {
  font-size: 100px;
}

.text-\[10px\] {
  font-size: 10px;
}

.text-\[2\.7rem\] {
  font-size: 2.7rem;
}

.text-\[22px\] {
  font-size: 22px;
}

.text-\[28px\] {
  font-size: 28px;
}

.text-\[75px\] {
  font-size: 75px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.normal-case {
  text-transform: none;
}

.italic {
  font-style: italic;
}

.ordinal {
  --tw-ordinal: ordinal;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.leading-10 {
  line-height: 2.5rem;
}

.leading-3 {
  line-height: .75rem;
}

.leading-35 {
  line-height: 35px;
}

.leading-4 {
  line-height: 1rem;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-7 {
  line-height: 1.75rem;
}

.leading-8 {
  line-height: 2rem;
}

.leading-9 {
  line-height: 2.25rem;
}

.leading-\[120\%\] {
  line-height: 120%;
}

.leading-\[14px\] {
  line-height: 14px;
}

.leading-\[150px\] {
  line-height: 150px;
}

.leading-\[18px\] {
  line-height: 18px;
}

.leading-\[2\.75rem\] {
  line-height: 2.75rem;
}

.leading-\[21px\] {
  line-height: 21px;
}

.leading-\[22px\] {
  line-height: 22px;
}

.leading-\[30px\] {
  line-height: 30px;
}

.leading-loose {
  line-height: 2;
}

.leading-none {
  line-height: 1;
}

.leading-normal {
  line-height: 1.5;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-tight {
  line-height: 1.25;
}

.\!text-theme-purple {
  --tw-text-opacity: 1 !important;
  color: rgb(24 1 45 / var(--tw-text-opacity, 1)) !important;
}

.text-\[\#18034380\] {
  color: #18034380;
}

.text-\[\#4C03EF\] {
  --tw-text-opacity: 1;
  color: rgb(76 3 239 / var(--tw-text-opacity, 1));
}

.text-\[\#8078A0\] {
  --tw-text-opacity: 1;
  color: rgb(128 120 160 / var(--tw-text-opacity, 1));
}

.text-\[\#888\] {
  --tw-text-opacity: 1;
  color: rgb(136 136 136 / var(--tw-text-opacity, 1));
}

.text-\[\#FE1CA6\] {
  --tw-text-opacity: 1;
  color: rgb(254 28 166 / var(--tw-text-opacity, 1));
}

.text-\[\#FF20E9\] {
  --tw-text-opacity: 1;
  color: rgb(255 32 233 / var(--tw-text-opacity, 1));
}

.text-\[\#d9d9d9\] {
  --tw-text-opacity: 1;
  color: rgb(217 217 217 / var(--tw-text-opacity, 1));
}

.text-\[\#eaeaea\] {
  --tw-text-opacity: 1;
  color: rgb(234 234 234 / var(--tw-text-opacity, 1));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(28 100 242 / var(--tw-text-opacity, 1));
}

.text-dark-purple {
  --tw-text-opacity: 1;
  color: rgb(44 1 83 / var(--tw-text-opacity, 1));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.text-light-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(136 136 136 / var(--tw-text-opacity, 1));
}

.text-light-purple {
  --tw-text-opacity: 1;
  color: rgb(199 195 213 / var(--tw-text-opacity, 1));
}

.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(144 97 249 / var(--tw-text-opacity, 1));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(240 82 82 / var(--tw-text-opacity, 1));
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(224 36 36 / var(--tw-text-opacity, 1));
}

.text-theme-pink {
  --tw-text-opacity: 1;
  color: rgb(254 28 166 / var(--tw-text-opacity, 1));
}

.text-theme-purple {
  --tw-text-opacity: 1;
  color: rgb(24 1 45 / var(--tw-text-opacity, 1));
}

.text-theme-purple\/80 {
  color: rgb(24 1 45 / 0.8);
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-white\/20 {
  color: rgb(255 255 255 / 0.2);
}

.text-white\/50 {
  color: rgb(255 255 255 / 0.5);
}

.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}

.text-white\/90 {
  color: rgb(255 255 255 / 0.9);
}

.underline {
  text-decoration-line: underline;
}

.no-underline {
  text-decoration-line: none;
}

.placeholder-light-purple::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(199 195 213 / var(--tw-placeholder-opacity, 1));
}

.placeholder-light-purple::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(199 195 213 / var(--tw-placeholder-opacity, 1));
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-20 {
  opacity: 0.2;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0px_0px_20px_0px_rgba\(0\2c 0\2c 0\2c 0\.1\)\] {
  --tw-shadow: 0px 0px 20px 0px rgba(0,0,0,0.1);
  --tw-shadow-colored: 0px 0px 20px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-blog {
  --tw-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.25);
  --tw-shadow-colored: 0px 0px 20px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-blueBox {
  --tw-shadow:  0px 0px 100px 0px rgba(76, 3, 239, 0.25);
  --tw-shadow-colored: 0px 0px 100px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-btnBlue {
  --tw-shadow: 0px 0px 50px 0px #4C03EF;
  --tw-shadow-colored: 0px 0px 50px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-btnLight {
  --tw-shadow: 0px 0px 50px rgba(254, 28, 166, 0.5);
  --tw-shadow-colored: 0px 0px 50px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-btnwhite {
  --tw-shadow: 0px 0px 50px rgba(128, 120, 160, 0.5);
  --tw-shadow-colored: 0px 0px 50px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-card {
  --tw-shadow: 0px 0px 50px rgba(0, 0, 0, 0.15);
  --tw-shadow-colored: 0px 0px 50px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-form {
  --tw-shadow: 0px 0px 50px 0px rgba(0, 0, 0, 0.15);
  --tw-shadow-colored: 0px 0px 50px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-theme-pink {
  --tw-shadow-color: #FE1CA6;
  --tw-shadow: var(--tw-shadow-colored);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline {
  outline-style: solid;
}

.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-\[10px\] {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(10px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-\[30px\] {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(30px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-light-purple\/10 {
  --tw-ring-color: rgb(199 195 213 / 0.1);
}

.ring-transparent {
  --tw-ring-color: transparent;
}

.ring-white\/20 {
  --tw-ring-color: rgb(255 255 255 / 0.2);
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-xl {
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.uw-code {
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: white;
  text-align: left;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: #1f2937;
}

@media (min-width: 640px) {
  .uw-code {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  --tw-text-opacity: 1;
  color: rgb(254 28 166 / var(--tw-text-opacity, 1));
  text-decoration-line: underline;
}

.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)):hover {
  text-decoration-line: none;
}

[class~="not-prose"]::marker {
  --tw-text-opacity: 1;
  color: rgb(44 1 83 / var(--tw-text-opacity, 1));
}

.uw-pink-underline {
  border-bottom-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(254 28 166 / var(--tw-border-opacity, 1));
}

.\[hostname\:port\] {
  hostname: port;
}

/**
 * Custom styles to immediately follow Tailwind’s `utilities` layer
 *
 * Add your own utility classes to this theme. Complex utility classes should
 * be added using Tailwind’s plugin system:
 *
 * https://tailwindcss.com/docs/plugins#adding-utilities
 */

.tooltip.hide-it > .tooltip-arrow:before {
  visibility: hidden;
}

[role="tooltip"].hide-it > [data-popper-arrow]:before {
  visibility: hidden;
}

[role="tooltip"].hide-it > [data-popper-arrow]:after {
  visibility: hidden;
}

.hide-it {
  pointer-events: none;
  visibility: hidden;
  opacity: 0;
}

@media (min-width: 640px) {
  .sm\:heading-secondary {
    font-size: 40px;
    font-weight: 700;
    line-height: 54px;
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }
}

@media (min-width: 768px) {
  .md\:heading-secondary {
    font-size: 40px;
    font-weight: 700;
    line-height: 54px;
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }
}

@media (min-width: 1024px) {
  .lg\:heading-primary {
    font-size: 50px;
    font-weight: 700;
    line-height: 50px;
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }

  .lg\:heading-secondary {
    font-size: 40px;
    font-weight: 700;
    line-height: 54px;
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }
}

.placeholder\:text-current::-moz-placeholder {
  color: currentColor;
}

.placeholder\:text-current::placeholder {
  color: currentColor;
}

.before\:pointer-events-none::before {
  content: var(--tw-content);
  pointer-events: none;
}

.before\:absolute::before {
  content: var(--tw-content);
  position: absolute;
}

.before\:left-1\/2::before {
  content: var(--tw-content);
  left: 50%;
}

.before\:top-0\.5::before {
  content: var(--tw-content);
  top: 0.125rem;
}

.before\:flex::before {
  content: var(--tw-content);
  display: flex;
}

.before\:hidden::before {
  content: var(--tw-content);
  display: none;
}

.before\:h-5::before {
  content: var(--tw-content);
  height: 1.25rem;
}

.before\:h-px::before {
  content: var(--tw-content);
  height: 1px;
}

.before\:w-5::before {
  content: var(--tw-content);
  width: 1.25rem;
}

.before\:w-full::before {
  content: var(--tw-content);
  width: 100%;
}

.before\:max-w-\[1440px\]::before {
  content: var(--tw-content);
  max-width: 1440px;
}

.before\:-translate-x-1\/2::before {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:items-center::before {
  content: var(--tw-content);
  align-items: center;
}

.before\:justify-center::before {
  content: var(--tw-content);
  justify-content: center;
}

.before\:rounded-\[5px\]::before {
  content: var(--tw-content);
  border-radius: 5px;
}

.before\:border::before {
  content: var(--tw-content);
  border-width: 1px;
}

.before\:border-light-purple::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(199 195 213 / var(--tw-border-opacity, 1));
}

.before\:bg-border-copyright::before {
  content: var(--tw-content);
  background-image: linear-gradient(270deg, rgba(128, 120, 160, 0) 0%, #8078A0 50.52%, rgba(128, 120, 160, 0) 100%);
}

.before\:text-transparent::before {
  content: var(--tw-content);
  color: transparent;
}

.before\:content-\[\'\'\]::before {
  --tw-content: '';
  content: var(--tw-content);
}

.before\:content-\[\'\\2713\'\]::before {
  --tw-content: '\2713';
  content: var(--tw-content);
}

.hover\:z-\[9999\]:hover {
  z-index: 9999;
}

.hover\:-translate-y-2:hover {
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.hover\:border-theme-blue:hover {
  --tw-border-opacity: 1;
  border-color: rgb(76 3 239 / var(--tw-border-opacity, 1));
}

.hover\:bg-blue-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(30 66 159 / var(--tw-bg-opacity, 1));
}

.hover\:bg-dark-purple:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(44 1 83 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-light-purple:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(199 195 213 / var(--tw-bg-opacity, 1));
}

.hover\:bg-theme-blue:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(76 3 239 / var(--tw-bg-opacity, 1));
}

.hover\:bg-theme-pink:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 28 166 / var(--tw-bg-opacity, 1));
}

.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(28 100 242 / var(--tw-text-opacity, 1));
}

.hover\:text-dark-purple:hover {
  --tw-text-opacity: 1;
  color: rgb(44 1 83 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.hover\:text-theme-pink:hover {
  --tw-text-opacity: 1;
  color: rgb(254 28 166 / var(--tw-text-opacity, 1));
}

.hover\:text-theme-purple:hover {
  --tw-text-opacity: 1;
  color: rgb(24 1 45 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:shadow-btnBlue:hover {
  --tw-shadow: 0px 0px 50px 0px #4C03EF;
  --tw-shadow-colored: 0px 0px 50px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-theme-blue:hover {
  --tw-shadow-color: #4C03EF;
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:ring-theme-pink\/20:hover {
  --tw-ring-color: rgb(254 28 166 / 0.2);
}

.hover\:ring-theme-pink\/40:hover {
  --tw-ring-color: rgb(254 28 166 / 0.4);
}

.hover\:ring-offset-2:hover {
  --tw-ring-offset-width: 2px;
}

.hover\:ring-offset-theme-pink\/10:hover {
  --tw-ring-offset-color: rgb(254 28 166 / 0.1);
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-4:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-\[\#FE1CA6\]:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 28 166 / var(--tw-ring-opacity, 1));
}

.focus\:ring-blue-300:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(164 202 254 / var(--tw-ring-opacity, 1));
}

.focus\:ring-gray-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity, 1));
}

.focus\:placeholder\:text-theme-purple:focus::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(24 1 45 / var(--tw-text-opacity, 1));
}

.focus\:placeholder\:text-theme-purple:focus::placeholder {
  --tw-text-opacity: 1;
  color: rgb(24 1 45 / var(--tw-text-opacity, 1));
}

.disabled\:text-\[\#EAEAEA\]:disabled {
  --tw-text-opacity: 1;
  color: rgb(234 234 234 / var(--tw-text-opacity, 1));
}

.disabled\:text-\[\#eaeaea\]:disabled {
  --tw-text-opacity: 1;
  color: rgb(234 234 234 / var(--tw-text-opacity, 1));
}

.group[open] .group-open\:bg-dark-purple {
  --tw-bg-opacity: 1;
  background-color: rgb(44 1 83 / var(--tw-bg-opacity, 1));
}

.group[open] .group-open\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:visible {
  visibility: visible;
}

.group:hover .group-hover\:max-h-full {
  max-height: 100%;
}

.group:hover .group-hover\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/link:hover .group-hover\/link\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:border-\[\#4C03EF\]\/10 {
  border-color: rgb(76 3 239 / 0.1);
}

.group:hover .group-hover\:bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.group:hover .group-hover\:from-theme-purple\/20 {
  --tw-gradient-from: rgb(24 1 45 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(24 1 45 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.group:hover .group-hover\:to-theme-blue\/20 {
  --tw-gradient-to: rgb(76 3 239 / 0.2) var(--tw-gradient-to-position);
}

.group:hover .group-hover\:text-theme-blue {
  --tw-text-opacity: 1;
  color: rgb(76 3 239 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-theme-purple {
  --tw-text-opacity: 1;
  color: rgb(24 1 45 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:ring-white\/20 {
  --tw-ring-color: rgb(255 255 255 / 0.2);
}

.group:disabled .group-disabled\:border-transparent {
  border-color: transparent;
}

.peer:checked ~ .peer-checked\:before\:text-light-purple::before {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgb(199 195 213 / var(--tw-text-opacity, 1));
}

.dark\:border-blue-500:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(63 131 248 / var(--tw-border-opacity, 1));
}

.dark\:border-gray-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.dark\:border-gray-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}

.dark\:border-transparent:is(.dark *) {
  border-color: transparent;
}

.dark\:bg-blue-600:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(28 100 242 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-600:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-800\/50:is(.dark *) {
  background-color: rgb(31 41 55 / 0.5);
}

.dark\:bg-opacity-80:is(.dark *) {
  --tw-bg-opacity: 0.8;
}

.dark\:text-blue-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(63 131 248 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.dark\:text-white:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.dark\:ring-offset-gray-800:is(.dark *) {
  --tw-ring-offset-color: #1F2937;
}

.dark\:hover\:bg-blue-700:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(26 86 219 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-gray-600:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-gray-800:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:text-blue-500:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(63 131 248 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-gray-300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-white:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.dark\:focus\:ring-\[\#FE1CA6\]:focus:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 28 166 / var(--tw-ring-opacity, 1));
}

@media (min-width: 640px) {
  .sm\:right-20 {
    right: 5rem;
  }

  .sm\:right-36 {
    right: 9rem;
  }

  .sm\:top-40 {
    top: 10rem;
  }

  .sm\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .sm\:my-14 {
    margin-top: 3.5rem;
    margin-bottom: 3.5rem;
  }

  .sm\:my-24 {
    margin-top: 6rem;
    margin-bottom: 6rem;
  }

  .sm\:my-5 {
    margin-top: 1.25rem;
    margin-bottom: 1.25rem;
  }

  .sm\:-mt-32 {
    margin-top: -8rem;
  }

  .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .sm\:mb-1\.5 {
    margin-bottom: 0.375rem;
  }

  .sm\:mb-16 {
    margin-bottom: 4rem;
  }

  .sm\:mb-20 {
    margin-bottom: 5rem;
  }

  .sm\:mb-24 {
    margin-bottom: 6rem;
  }

  .sm\:mb-5 {
    margin-bottom: 1.25rem;
  }

  .sm\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .sm\:mb-8 {
    margin-bottom: 2rem;
  }

  .sm\:mb-9 {
    margin-bottom: 2.25rem;
  }

  .sm\:mb-\[100px\] {
    margin-bottom: 100px;
  }

  .sm\:mb-\[70px\] {
    margin-bottom: 70px;
  }

  .sm\:mb-\[76px\] {
    margin-bottom: 76px;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:mt-2\.5 {
    margin-top: 0.625rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:h-\[144px\] {
    height: 144px;
  }

  .sm\:h-\[220px\] {
    height: 220px;
  }

  .sm\:h-\[300px\] {
    height: 300px;
  }

  .sm\:min-h-\[300px\] {
    min-height: 300px;
  }

  .sm\:w-20 {
    width: 5rem;
  }

  .sm\:w-24 {
    width: 6rem;
  }

  .sm\:w-28 {
    width: 7rem;
  }

  .sm\:w-80 {
    width: 20rem;
  }

  .sm\:w-\[490px\] {
    width: 490px;
  }

  .sm\:w-\[calc\(33\.333\%-16px\)\] {
    width: calc(33.333% - 16px);
  }

  .sm\:w-\[calc\(50\%-12px\)\] {
    width: calc(50% - 12px);
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-full {
    width: 100%;
  }

  .sm\:max-w-\[230px\] {
    max-width: 230px;
  }

  .sm\:max-w-\[247px\] {
    max-width: 247px;
  }

  .sm\:max-w-\[272px\] {
    max-width: 272px;
  }

  .sm\:max-w-\[314px\] {
    max-width: 314px;
  }

  .sm\:max-w-\[345px\] {
    max-width: 345px;
  }

  .sm\:max-w-\[388px\] {
    max-width: 388px;
  }

  .sm\:max-w-\[400px\] {
    max-width: 400px;
  }

  .sm\:max-w-\[50vw\] {
    max-width: 50vw;
  }

  .sm\:max-w-\[70vw\] {
    max-width: 70vw;
  }

  .sm\:max-w-full {
    max-width: 100%;
  }

  .sm\:translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-\[minmax\(160px\2c 240px\)\2c 1fr\] {
    grid-template-columns: minmax(160px,240px) 1fr;
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .sm\:items-end {
    align-items: flex-end;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-start {
    justify-content: flex-start;
  }

  .sm\:justify-center {
    justify-content: center;
  }

  .sm\:gap-10 {
    gap: 2.5rem;
  }

  .sm\:gap-8 {
    gap: 2rem;
  }

  .sm\:gap-\[60px\] {
    gap: 60px;
  }

  .sm\:overflow-visible {
    overflow: visible;
  }

  .sm\:border-b {
    border-bottom-width: 1px;
  }

  .sm\:border-\[\#d9d9d9\] {
    --tw-border-opacity: 1;
    border-color: rgb(217 217 217 / var(--tw-border-opacity, 1));
  }

  .sm\:p-12 {
    padding: 3rem;
  }

  .sm\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .sm\:px-20 {
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .sm\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .sm\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .sm\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .sm\:px-\[33\%\] {
    padding-left: 33%;
    padding-right: 33%;
  }

  .sm\:py-15 {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .sm\:pb-12 {
    padding-bottom: 3rem;
  }

  .sm\:pb-24 {
    padding-bottom: 6rem;
  }

  .sm\:pb-40 {
    padding-bottom: 10rem;
  }

  .sm\:pb-8 {
    padding-bottom: 2rem;
  }

  .sm\:pt-16 {
    padding-top: 4rem;
  }

  .sm\:pt-20 {
    padding-top: 5rem;
  }

  @media (min-width: 640px) {
    .sm\:sm\:text-left {
      text-align: left;
    }
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .sm\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .sm\:text-\[120px\] {
    font-size: 120px;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .sm\:leading-5 {
    line-height: 1.25rem;
  }

  .sm\:leading-6 {
    line-height: 1.5rem;
  }

  .sm\:leading-7 {
    line-height: 1.75rem;
  }

  .sm\:leading-\[130px\] {
    line-height: 130px;
  }

  .sm\:leading-\[30px\] {
    line-height: 30px;
  }

  .sm\:leading-normal {
    line-height: 1.5;
  }

  .sm\:leading-tight {
    line-height: 1.25;
  }

  .sm\:text-\[\#D9D9D9\] {
    --tw-text-opacity: 1;
    color: rgb(217 217 217 / var(--tw-text-opacity, 1));
  }

  .sm\:shadow-btn {
    --tw-shadow: 0px 0px 50px #FE1CA6;
    --tw-shadow-colored: 0px 0px 50px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .sm\:\!shadow-theme-blue {
    --tw-shadow-color: #4C03EF !important;
    --tw-shadow: var(--tw-shadow-colored) !important;
  }
}

@media (min-width: 768px) {
  @media (min-width: 640px) {
    .md\:sm\:pb-0 {
      padding-bottom: 0px;
    }
  }

  .md\:pointer-events-none {
    pointer-events: none;
  }

  .md\:-top-16 {
    top: -4rem;
  }

  .md\:-top-20 {
    top: -5rem;
  }

  .md\:left-\[120px\] {
    left: 120px;
  }

  .md\:right-24 {
    right: 6rem;
  }

  .md\:order-first {
    order: -9999;
  }

  .md\:order-none {
    order: 0;
  }

  .md\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .md\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .md\:-mt-12 {
    margin-top: -3rem;
  }

  .md\:-mt-20 {
    margin-top: -5rem;
  }

  .md\:-mt-36 {
    margin-top: -9rem;
  }

  .md\:-mt-4 {
    margin-top: -1rem;
  }

  .md\:-mt-\[150px\] {
    margin-top: -150px;
  }

  .md\:-mt-\[207px\] {
    margin-top: -207px;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mb-10 {
    margin-bottom: 2.5rem;
  }

  .md\:mb-11 {
    margin-bottom: 2.75rem;
  }

  .md\:mb-12 {
    margin-bottom: 3rem;
  }

  .md\:mb-14 {
    margin-bottom: 3.5rem;
  }

  .md\:mb-16 {
    margin-bottom: 4rem;
  }

  .md\:mb-2 {
    margin-bottom: 0.5rem;
  }

  .md\:mb-20 {
    margin-bottom: 5rem;
  }

  .md\:mb-24 {
    margin-bottom: 6rem;
  }

  .md\:mb-4 {
    margin-bottom: 1rem;
  }

  .md\:mb-44 {
    margin-bottom: 11rem;
  }

  .md\:mb-9 {
    margin-bottom: 2.25rem;
  }

  .md\:mb-\[117px\] {
    margin-bottom: 117px;
  }

  .md\:mb-\[17px\] {
    margin-bottom: 17px;
  }

  .md\:mb-\[22px\] {
    margin-bottom: 22px;
  }

  .md\:mb-\[30px\] {
    margin-bottom: 30px;
  }

  .md\:mb-\[50px\] {
    margin-bottom: 50px;
  }

  .md\:mb-\[54px\] {
    margin-bottom: 54px;
  }

  .md\:mb-\[66px\] {
    margin-bottom: 66px;
  }

  .md\:mb-\[70px\] {
    margin-bottom: 70px;
  }

  .md\:mb-\[78px\] {
    margin-bottom: 78px;
  }

  .md\:ml-auto {
    margin-left: auto;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-11 {
    margin-top: 2.75rem;
  }

  .md\:mt-12 {
    margin-top: 3rem;
  }

  .md\:mt-14 {
    margin-top: 3.5rem;
  }

  .md\:mt-2\.5 {
    margin-top: 0.625rem;
  }

  .md\:mt-7 {
    margin-top: 1.75rem;
  }

  .md\:mt-7\.5 {
    margin-top: 30px;
  }

  .md\:mt-\[117px\] {
    margin-top: 117px;
  }

  .md\:mt-\[19px\] {
    margin-top: 19px;
  }

  .md\:mt-\[22px\] {
    margin-top: 22px;
  }

  .md\:mt-\[64px\] {
    margin-top: 64px;
  }

  .md\:mt-\[70px\] {
    margin-top: 70px;
  }

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-15 {
    height: 60px;
  }

  .md\:h-64 {
    height: 16rem;
  }

  .md\:h-\[115px\] {
    height: 115px;
  }

  .md\:h-\[181px\] {
    height: 181px;
  }

  .md\:h-\[50px\] {
    height: 50px;
  }

  .md\:h-\[62px\] {
    height: 62px;
  }

  .md\:min-h-\[360px\] {
    min-height: 360px;
  }

  .md\:min-h-\[5rem\] {
    min-height: 5rem;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-15 {
    width: 60px;
  }

  .md\:w-3\/6 {
    width: 50%;
  }

  .md\:w-80 {
    width: 20rem;
  }

  .md\:w-96 {
    width: 24rem;
  }

  .md\:w-\[calc\(25\%-18px\)\] {
    width: calc(25% - 18px);
  }

  .md\:w-\[calc\(33\.333\%-16px\)\] {
    width: calc(33.333% - 16px);
  }

  .md\:w-\[calc\(50\%-10px\)\] {
    width: calc(50% - 10px);
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:w-max {
    width: -moz-max-content;
    width: max-content;
  }

  .md\:max-w-\[1170px\] {
    max-width: 1170px;
  }

  .md\:max-w-\[120px\] {
    max-width: 120px;
  }

  .md\:max-w-\[190px\] {
    max-width: 190px;
  }

  .md\:max-w-\[242px\] {
    max-width: 242px;
  }

  .md\:max-w-\[260px\] {
    max-width: 260px;
  }

  .md\:max-w-\[297px\] {
    max-width: 297px;
  }

  .md\:max-w-\[300px\] {
    max-width: 300px;
  }

  .md\:max-w-\[400px\] {
    max-width: 400px;
  }

  .md\:max-w-\[422px\] {
    max-width: 422px;
  }

  .md\:max-w-\[440px\] {
    max-width: 440px;
  }

  .md\:max-w-\[450px\] {
    max-width: 450px;
  }

  .md\:max-w-\[460px\] {
    max-width: 460px;
  }

  .md\:max-w-\[50\%\] {
    max-width: 50%;
  }

  .md\:max-w-\[60\%\] {
    max-width: 60%;
  }

  .md\:max-w-\[665px\] {
    max-width: 665px;
  }

  .md\:max-w-\[680px\] {
    max-width: 680px;
  }

  .md\:max-w-\[800px\] {
    max-width: 800px;
  }

  .md\:max-w-full {
    max-width: 100%;
  }

  .md\:flex-shrink-0 {
    flex-shrink: 0;
  }

  .md\:translate-y-10 {
    --tw-translate-y: 2.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:scale-y-\[1\.05\] {
    --tw-scale-y: 1.05;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-\[20px_calc\(100\%-40px\)_20px\] {
    grid-template-columns: 20px calc(100% - 40px) 20px;
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-col {
    flex-direction: column;
  }

  .md\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:items-end {
    align-items: flex-end;
  }

  .md\:justify-start {
    justify-content: flex-start;
  }

  .md\:justify-end {
    justify-content: flex-end;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:gap-0\.5 {
    gap: 0.125rem;
  }

  .md\:gap-10 {
    gap: 2.5rem;
  }

  .md\:gap-3 {
    gap: 0.75rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:gap-5 {
    gap: 1.25rem;
  }

  .md\:gap-7 {
    gap: 1.75rem;
  }

  .md\:gap-8 {
    gap: 2rem;
  }

  .md\:gap-9 {
    gap: 2.25rem;
  }

  .md\:gap-\[30px\] {
    gap: 30px;
  }

  .md\:overflow-hidden {
    overflow: hidden;
  }

  .md\:rounded-\[100px\] {
    border-radius: 100px;
  }

  .md\:rounded-\[20px\] {
    border-radius: 20px;
  }

  .md\:rounded-t-\[100px\] {
    border-top-left-radius: 100px;
    border-top-right-radius: 100px;
  }

  .md\:border-l-2 {
    border-left-width: 2px;
  }

  .md\:border-r {
    border-right-width: 1px;
  }

  .md\:border-t {
    border-top-width: 1px;
  }

  .md\:bg-transparent {
    background-color: transparent;
  }

  .md\:bg-footer {
    background-image: linear-gradient(180deg, #1B0259 0%, #18012D 100%);
  }

  .md\:bg-hero {
    background-image: url("./assets/images/home/<USER>");
  }

  .md\:p-4 {
    padding: 1rem;
  }

  .md\:p-5 {
    padding: 1.25rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:px-11 {
    padding-left: 2.75rem;
    padding-right: 2.75rem;
  }

  .md\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .md\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .md\:py-\[18px\] {
    padding-top: 18px;
    padding-bottom: 18px;
  }

  .md\:py-\[71px\] {
    padding-top: 71px;
    padding-bottom: 71px;
  }

  .md\:pb-0 {
    padding-bottom: 0px;
  }

  .md\:pb-10 {
    padding-bottom: 2.5rem;
  }

  .md\:pb-24 {
    padding-bottom: 6rem;
  }

  .md\:pb-\[69px\] {
    padding-bottom: 69px;
  }

  .md\:pb-\[74px\] {
    padding-bottom: 74px;
  }

  .md\:pr-16 {
    padding-right: 4rem;
  }

  .md\:pr-5 {
    padding-right: 1.25rem;
  }

  .md\:pr-6 {
    padding-right: 1.5rem;
  }

  .md\:pt-0 {
    padding-top: 0px;
  }

  .md\:pt-12 {
    padding-top: 3rem;
  }

  .md\:pt-14 {
    padding-top: 3.5rem;
  }

  .md\:pt-24 {
    padding-top: 6rem;
  }

  .md\:pt-5 {
    padding-top: 1.25rem;
  }

  .md\:pt-\[110px\] {
    padding-top: 110px;
  }

  .md\:pt-\[181px\] {
    padding-top: 181px;
  }

  .md\:pt-\[251px\] {
    padding-top: 251px;
  }

  .md\:pt-\[70px\] {
    padding-top: 70px;
  }

  .md\:text-left {
    text-align: left;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-40 {
    font-size: 40px;
  }

  .md\:text-\[28px\] {
    font-size: 28px;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .md\:leading-6 {
    line-height: 1.5rem;
  }

  .md\:leading-7 {
    line-height: 1.75rem;
  }

  .md\:leading-8 {
    line-height: 2rem;
  }

  .md\:leading-9 {
    line-height: 2.25rem;
  }

  .md\:leading-normal {
    line-height: 1.5;
  }

  .md\:before\:block::before {
    content: var(--tw-content);
    display: block;
  }
}

@media (min-width: 1024px) {
  .lg\:absolute {
    position: absolute;
  }

  .lg\:-top-\[35\%\] {
    top: -35%;
  }

  .lg\:right-\[140px\] {
    right: 140px;
  }

  .lg\:top-0 {
    top: 0px;
  }

  .lg\:z-50 {
    z-index: 50;
  }

  .lg\:order-none {
    order: 0;
  }

  .lg\:-mx-20 {
    margin-left: -5rem;
    margin-right: -5rem;
  }

  .lg\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .lg\:my-16 {
    margin-top: 4rem;
    margin-bottom: 4rem;
  }

  .lg\:my-6 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .lg\:-mt-12 {
    margin-top: -3rem;
  }

  .lg\:-mt-44 {
    margin-top: -11rem;
  }

  .lg\:-mt-\[180px\] {
    margin-top: -180px;
  }

  .lg\:mb-0 {
    margin-bottom: 0px;
  }

  .lg\:mb-12 {
    margin-bottom: 3rem;
  }

  .lg\:mb-20 {
    margin-bottom: 5rem;
  }

  .lg\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .lg\:mb-\[100px\] {
    margin-bottom: 100px;
  }

  .lg\:mb-\[60px\] {
    margin-bottom: 60px;
  }

  .lg\:mt-0 {
    margin-top: 0px;
  }

  .lg\:mt-10 {
    margin-top: 2.5rem;
  }

  .lg\:mt-\[90px\] {
    margin-top: 90px;
  }

  .lg\:block {
    display: block;
  }

  .lg\:inline {
    display: inline;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-14 {
    height: 3.5rem;
  }

  .lg\:h-\[17px\] {
    height: 17px;
  }

  .lg\:h-\[181px\] {
    height: 181px;
  }

  .lg\:h-\[19px\] {
    height: 19px;
  }

  .lg\:h-\[21px\] {
    height: 21px;
  }

  .lg\:h-\[280px\] {
    height: 280px;
  }

  .lg\:h-\[calc\(460px-150px\)\] {
    height: calc(460px - 150px);
  }

  .lg\:w-14 {
    width: 3.5rem;
  }

  .lg\:w-2\/6 {
    width: 33.333333%;
  }

  .lg\:w-28 {
    width: 7rem;
  }

  .lg\:w-\[19px\] {
    width: 19px;
  }

  .lg\:w-\[23px\] {
    width: 23px;
  }

  .lg\:w-\[26px\] {
    width: 26px;
  }

  .lg\:w-\[420px\] {
    width: 420px;
  }

  .lg\:w-\[calc\(16\.666\%-20px\)\] {
    width: calc(16.666% - 20px);
  }

  .lg\:w-\[calc\(25\%-18px\)\] {
    width: calc(25% - 18px);
  }

  .lg\:w-\[calc\(33\.333\%-14px\)\] {
    width: calc(33.333% - 14px);
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:w-max {
    width: -moz-max-content;
    width: max-content;
  }

  .lg\:max-w-\[1132px\] {
    max-width: 1132px;
  }

  .lg\:max-w-\[1170px\] {
    max-width: 1170px;
  }

  .lg\:max-w-\[200px\] {
    max-width: 200px;
  }

  .lg\:max-w-\[300px\] {
    max-width: 300px;
  }

  .lg\:max-w-\[340px\] {
    max-width: 340px;
  }

  .lg\:max-w-\[400px\] {
    max-width: 400px;
  }

  .lg\:max-w-\[402px\] {
    max-width: 402px;
  }

  .lg\:max-w-\[416px\] {
    max-width: 416px;
  }

  .lg\:max-w-\[500px\] {
    max-width: 500px;
  }

  .lg\:max-w-\[550px\] {
    max-width: 550px;
  }

  .lg\:max-w-\[558px\] {
    max-width: 558px;
  }

  .lg\:max-w-\[560px\] {
    max-width: 560px;
  }

  .lg\:max-w-\[570px\] {
    max-width: 570px;
  }

  .lg\:max-w-\[700px\] {
    max-width: 700px;
  }

  .lg\:max-w-\[900px\] {
    max-width: 900px;
  }

  .lg\:max-w-lg {
    max-width: 32rem;
  }

  .lg\:max-w-screen-xl {
    max-width: 1280px;
  }

  .lg\:flex-grow {
    flex-grow: 1;
  }

  .lg\:-translate-x-1\/2 {
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-y-14 {
    --tw-translate-y: 3.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-y-20 {
    --tw-translate-y: 5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-\[1fr\2c max-content\2c 1fr\] {
    grid-template-columns: 1fr max-content 1fr;
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .lg\:items-start {
    align-items: flex-start;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:justify-start {
    justify-content: flex-start;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:gap-10 {
    gap: 2.5rem;
  }

  .lg\:gap-12 {
    gap: 3rem;
  }

  .lg\:gap-2\.5 {
    gap: 0.625rem;
  }

  .lg\:gap-5 {
    gap: 1.25rem;
  }

  .lg\:gap-6 {
    gap: 1.5rem;
  }

  .lg\:gap-y-14 {
    row-gap: 3.5rem;
  }

  .lg\:overflow-auto {
    overflow: auto;
  }

  .lg\:rounded-\[100px\] {
    border-radius: 100px;
  }

  .lg\:rounded-\[50px\] {
    border-radius: 50px;
  }

  .lg\:border {
    border-width: 1px;
  }

  .lg\:bg-\[\#EAEAEA\] {
    --tw-bg-opacity: 1;
    background-color: rgb(234 234 234 / var(--tw-bg-opacity, 1));
  }

  .lg\:p-12 {
    padding: 3rem;
  }

  .lg\:p-7 {
    padding: 1.75rem;
  }

  .lg\:p-8 {
    padding: 2rem;
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .lg\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .lg\:px-20 {
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .lg\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .lg\:px-\[140px\] {
    padding-left: 140px;
    padding-right: 140px;
  }

  .lg\:px-\[30px\] {
    padding-left: 30px;
    padding-right: 30px;
  }

  .lg\:px-\[50px\] {
    padding-left: 50px;
    padding-right: 50px;
  }

  .lg\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .lg\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .lg\:pb-0 {
    padding-bottom: 0px;
  }

  .lg\:pb-10 {
    padding-bottom: 2.5rem;
  }

  .lg\:pb-12 {
    padding-bottom: 3rem;
  }

  .lg\:pb-15 {
    padding-bottom: 60px;
  }

  .lg\:pb-2 {
    padding-bottom: 0.5rem;
  }

  .lg\:pb-7 {
    padding-bottom: 1.75rem;
  }

  .lg\:pb-8 {
    padding-bottom: 2rem;
  }

  .lg\:pb-\[180px\] {
    padding-bottom: 180px;
  }

  .lg\:pb-\[62px\] {
    padding-bottom: 62px;
  }

  .lg\:pb-\[66px\] {
    padding-bottom: 66px;
  }

  .lg\:pl-4 {
    padding-left: 1rem;
  }

  .lg\:pl-48 {
    padding-left: 12rem;
  }

  .lg\:pl-60 {
    padding-left: 15rem;
  }

  .lg\:pl-\[135px\] {
    padding-left: 135px;
  }

  .lg\:pr-24 {
    padding-right: 6rem;
  }

  .lg\:pr-40 {
    padding-right: 10rem;
  }

  .lg\:pr-60 {
    padding-right: 15rem;
  }

  .lg\:pr-\[120px\] {
    padding-right: 120px;
  }

  .lg\:pt-10 {
    padding-top: 2.5rem;
  }

  .lg\:pt-15 {
    padding-top: 60px;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-17 {
    font-size: 17px;
  }

  .lg\:text-28 {
    font-size: 28px;
  }

  .lg\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .lg\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .lg\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .lg\:text-\[2\.7rem\] {
    font-size: 2.7rem;
  }

  .lg\:text-\[40px\] {
    font-size: 40px;
  }

  .lg\:text-\[65px\] {
    font-size: 65px;
  }

  .lg\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .lg\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .lg\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .lg\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .lg\:font-bold {
    font-weight: 700;
  }

  .lg\:leading-23 {
    line-height: 23px;
  }

  .lg\:leading-6 {
    line-height: 1.5rem;
  }

  .lg\:leading-\[22px\] {
    line-height: 22px;
  }

  .lg\:leading-\[48px\] {
    line-height: 48px;
  }

  .lg\:leading-\[50px\] {
    line-height: 50px;
  }

  .lg\:leading-\[80px\] {
    line-height: 80px;
  }

  .lg\:text-theme-purple {
    --tw-text-opacity: 1;
    color: rgb(24 1 45 / var(--tw-text-opacity, 1));
  }

  .lg\:backdrop-blur-\[15px\] {
    --tw-backdrop-blur: blur(15px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  }
}

@media (min-width: 1280px) {
  .xl\:-bottom-\[35\%\] {
    bottom: -35%;
  }

  .xl\:-m-12 {
    margin: -3rem;
  }

  .xl\:-mt-\[181px\] {
    margin-top: -181px;
  }

  .xl\:mb-\[54px\] {
    margin-bottom: 54px;
  }

  .xl\:block {
    display: block;
  }

  .xl\:grid {
    display: grid;
  }

  .xl\:hidden {
    display: none;
  }

  .xl\:h-full {
    height: 100%;
  }

  .xl\:min-h-\[320px\] {
    min-height: 320px;
  }

  .xl\:min-h-\[322px\] {
    min-height: 322px;
  }

  .xl\:w-\[523px\] {
    width: 523px;
  }

  .xl\:w-\[calc\(25\%-38px\)\] {
    width: calc(25% - 38px);
  }

  .xl\:w-auto {
    width: auto;
  }

  .xl\:max-w-\[1170px\] {
    max-width: 1170px;
  }

  .xl\:max-w-\[1172px\] {
    max-width: 1172px;
  }

  .xl\:max-w-\[558px\] {
    max-width: 558px;
  }

  .xl\:max-w-max {
    max-width: -moz-max-content;
    max-width: max-content;
  }

  .xl\:max-w-screen-xl {
    max-width: 1280px;
  }

  .xl\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .xl\:gap-11 {
    gap: 2.75rem;
  }

  .xl\:gap-12 {
    gap: 3rem;
  }

  .xl\:gap-7 {
    gap: 1.75rem;
  }

  .xl\:gap-\[50px\] {
    gap: 50px;
  }

  .xl\:overflow-visible {
    overflow: visible;
  }

  .xl\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .xl\:px-\[133px\] {
    padding-left: 133px;
    padding-right: 133px;
  }

  .xl\:px-\[50px\] {
    padding-left: 50px;
    padding-right: 50px;
  }

  .xl\:py-11 {
    padding-top: 2.75rem;
    padding-bottom: 2.75rem;
  }

  .xl\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .xl\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .xl\:leading-\[27px\] {
    line-height: 27px;
  }

  .xl\:leading-uw-relaxed {
    line-height: 1.3;
  }
}

@media (min-width: 1536px) {
  .\32xl\:max-w-\[1024px\] {
    max-width: 1024px;
  }

  .\32xl\:max-w-\[600px\] {
    max-width: 600px;
  }
}

.\[\&\>\*\]\:py-1>* {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.\[\&\>div\]\:mx-8>div {
  margin-left: 2rem;
  margin-right: 2rem;
}

.\[\&\>div\]\:my-4>div {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.\[\&\>div\]\:flex>div {
  display: flex;
}

.\[\&\>div\]\:w-1\/3>div {
  width: 33.333333%;
}

.\[\&\>div\]\:w-\[350px\]>div {
  width: 350px;
}

.\[\&\>div\]\:w-full>div {
  width: 100%;
}

.\[\&\>div\]\:grow>div {
  flex-grow: 1;
}

.\[\&\>div\]\:flex-col>div {
  flex-direction: column;
}

.\[\&\>div\]\:justify-around>div {
  justify-content: space-around;
}

.\[\&\>div\]\:rounded-3xl>div {
  border-radius: 1.5rem;
}

.\[\&\>div\]\:rounded-\[20\%\]>div {
  border-radius: 20%;
}

.\[\&\>div\]\:bg-\[url\(\'assets\/images\/report_cards\/planet\.png\'\)\]>div {
  background-image: url('assets/images/report_cards/planet.png');
}

.\[\&\>div\]\:p-6>div {
  padding: 1.5rem;
}

.\[\&\>div\]\:px-12>div {
  padding-left: 3rem;
  padding-right: 3rem;
}

.\[\&\>p\>span\]\:font-semibold>p>span {
  font-weight: 600;
}

.\[\&\>p\>span\]\:text-\[\#FE1CA6\]>p>span {
  --tw-text-opacity: 1;
  color: rgb(254 28 166 / var(--tw-text-opacity, 1));
}
