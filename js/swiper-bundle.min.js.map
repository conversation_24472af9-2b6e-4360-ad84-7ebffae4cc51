{"version": 3, "file": "swiper-bundle.min.js", "names": ["isObject", "obj", "constructor", "Object", "extend", "target", "src", "keys", "for<PERSON>ach", "key", "length", "ssrDocument", "body", "addEventListener", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "getElementById", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "createElementNS", "importNode", "location", "hash", "host", "hostname", "href", "origin", "pathname", "protocol", "search", "getDocument", "doc", "document", "ssrWindow", "navigator", "userAgent", "history", "replaceState", "pushState", "go", "back", "CustomEvent", "this", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "setTimeout", "clearTimeout", "matchMedia", "requestAnimationFrame", "callback", "cancelAnimationFrame", "id", "getWindow", "win", "window", "nextTick", "delay", "now", "getTranslate", "el", "axis", "matrix", "curTransform", "transformMatrix", "curStyle", "currentStyle", "WebKitCSSMatrix", "transform", "webkitTransform", "split", "map", "a", "replace", "join", "MozTransform", "OTransform", "MsTransform", "msTransform", "toString", "m41", "parseFloat", "m42", "o", "prototype", "call", "slice", "isNode", "node", "HTMLElement", "nodeType", "to", "arguments", "undefined", "noExtend", "i", "nextSource", "keysArray", "filter", "indexOf", "nextIndex", "len", "<PERSON><PERSON><PERSON>", "desc", "getOwnPropertyDescriptor", "enumerable", "__swiper__", "setCSSProperty", "varName", "varValue", "setProperty", "animateCSSModeScroll", "_ref", "swiper", "targetPosition", "side", "startPosition", "translate", "time", "startTime", "duration", "params", "speed", "wrapperEl", "scrollSnapType", "cssModeFrameID", "dir", "isOutOfBound", "current", "animate", "getTime", "progress", "Math", "max", "min", "easeProgress", "cos", "PI", "currentPosition", "scrollTo", "overflow", "getSlideTransformEl", "slideEl", "shadowEl", "elementChildren", "element", "selector", "matches", "tag", "classes", "classList", "add", "Array", "isArray", "elementOffset", "box", "getBoundingClientRect", "clientTop", "clientLeft", "scrollTop", "scrollY", "scrollLeft", "scrollX", "top", "left", "elementStyle", "prop", "elementIndex", "child", "previousSibling", "elementParents", "parents", "parent", "parentElement", "push", "elementTransitionEnd", "fireCallBack", "e", "elementOuterSize", "size", "<PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "support", "deviceCached", "browser", "getSupport", "smoothScroll", "documentElement", "touch", "DocumentTouch", "calcSupport", "getDevice", "overrides", "_temp", "platform", "ua", "device", "ios", "android", "screenWidth", "width", "screenHeight", "height", "match", "ipad", "ipod", "iphone", "windows", "macos", "os", "calcDevice", "<PERSON><PERSON><PERSON><PERSON>", "needPerspectiveFix", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "String", "includes", "major", "minor", "num", "Number", "isWebView", "test", "calcB<PERSON>er", "eventsEmitter", "on", "events", "handler", "priority", "self", "eventsListeners", "destroyed", "method", "event", "once", "once<PERSON><PERSON><PERSON>", "off", "__emitterProxy", "_len", "args", "_key", "apply", "onAny", "eventsAnyListeners", "offAny", "index", "splice", "<PERSON><PERSON><PERSON><PERSON>", "emit", "data", "context", "_len2", "_key2", "unshift", "update", "updateSize", "clientWidth", "clientHeight", "isHorizontal", "isVertical", "parseInt", "isNaN", "assign", "updateSlides", "getDirectionLabel", "property", "marginRight", "getDirectionPropertyValue", "label", "slidesEl", "swiperSize", "rtlTranslate", "rtl", "wrongRTL", "isVirtual", "virtual", "enabled", "previousSlidesLength", "slides", "slideClass", "<PERSON><PERSON><PERSON><PERSON>", "snapGrid", "slidesGrid", "slidesSizesGrid", "offsetBefore", "slidesOffsetBefore", "offsetAfter", "slidesOffsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "spaceBetween", "slidePosition", "prevSlideSize", "virtualSize", "marginLeft", "marginBottom", "marginTop", "centeredSlides", "cssMode", "gridEnabled", "grid", "rows", "slideSize", "initSlides", "shouldResetSlideSize", "<PERSON><PERSON><PERSON><PERSON>iew", "breakpoints", "slide", "updateSlide", "slideStyles", "currentTransform", "currentWebKitTransform", "roundLengths", "paddingLeft", "paddingRight", "boxSizing", "floor", "swiperSlideSize", "abs", "slidesPerGroup", "slidesPerGroupSkip", "effect", "setWrapperSize", "updateWrapperSize", "newSlidesGrid", "slidesGridItem", "loop", "groups", "ceil", "slidesBefore", "slidesAfter", "groupSize", "_", "slideIndex", "centeredSlidesBounds", "allSlidesSize", "slideSizeValue", "maxSnap", "snap", "centerInsufficientSlides", "allSlidesOffset", "snapIndex", "addToSnapGrid", "addToSlidesGrid", "v", "watchOverflow", "checkOverflow", "watchSlidesProgress", "updateSlidesOffset", "backFaceHiddenClass", "containerModifierClass", "hasClassBackfaceClassAdded", "contains", "maxBackfaceHiddenSlides", "remove", "updateAutoHeight", "activeSlides", "newHeight", "setTransition", "getSlideByIndex", "getAttribute", "visibleSlides", "activeIndex", "offsetHeight", "minusOffset", "isElement", "offsetLeft", "offsetTop", "swiperSlideOffset", "updateSlidesProgress", "offsetCenter", "slideVisibleClass", "visibleSlidesIndexes", "slideOffset", "slideProgress", "minTranslate", "originalSlideProgress", "slideBefore", "slideAfter", "originalProgress", "updateProgress", "multiplier", "translatesDiff", "maxTranslate", "isBeginning", "isEnd", "progressLoop", "wasBeginning", "wasEnd", "isBeginningRounded", "isEndRounded", "firstSlideIndex", "getSlideIndex", "lastSlideIndex", "firstSlideTranslate", "lastSlideTranslate", "translateMax", "translateAbs", "autoHeight", "updateSlidesClasses", "getFilteredSlide", "activeSlide", "slideActiveClass", "slideNextClass", "slidePrevClass", "nextSlide", "nextEls", "nextElement<PERSON><PERSON>ling", "next", "elementNextAll", "prevSlide", "prevEls", "previousElementSibling", "prev", "elementPrevAll", "emitSlidesClasses", "updateActiveIndex", "newActiveIndex", "previousIndex", "realIndex", "previousRealIndex", "previousSnapIndex", "getVirtualRealIndex", "aIndex", "normalizeSlideIndex", "getActiveIndexByTranslate", "skip", "initialized", "runCallbacksOnInit", "updateClickedSlide", "closest", "slideFound", "clickedSlide", "clickedIndex", "slideToClickedSlide", "virtualTranslate", "currentTranslate", "setTranslate", "byController", "newProgress", "x", "y", "previousTranslate", "translateTo", "runCallbacks", "translateBounds", "internal", "animating", "preventInteractionOnTransition", "newTranslate", "isH", "behavior", "onTranslateToWrapperTransitionEnd", "transitionEmit", "direction", "step", "slideTo", "initial", "normalizedTranslate", "normalizedGrid", "normalizedGridNext", "allowSlideNext", "allowSlidePrev", "transitionStart", "transitionEnd", "t", "_immediateVirtual", "_cssModeVirtualInitialSet", "initialSlide", "onSlideToWrapperTransitionEnd", "slideToLoop", "newIndex", "slideNext", "perGroup", "slidesPerGroupAuto", "slidesPerViewDynamic", "increment", "loopPreventsSliding", "loopFix", "_clientLeft", "rewind", "slidePrev", "normalize", "val", "normalizedSnapGrid", "prevSnap", "prevSnapIndex", "prevIndex", "lastIndex", "slideReset", "slideToClosest", "threshold", "currentSnap", "slideToIndex", "slideSelector", "loopedSlides", "loopCreate", "slideRealIndex", "activeSlideIndex", "byMousewheel", "prependSlidesIndexes", "appendSlidesIndexes", "isNext", "isPrev", "slidesPrepended", "slidesAppended", "prepend", "append", "recalcSlides", "currentSlideTranslate", "diff", "touches", "controller", "control", "loopParams", "c", "loop<PERSON><PERSON><PERSON>", "newSlidesOrder", "swiperSlideIndex", "removeAttribute", "onTouchStart", "touchEventsData", "ev<PERSON><PERSON>", "simulate<PERSON>ouch", "pointerType", "originalEvent", "targetEl", "touchEventsTarget", "which", "button", "isTouched", "isMoved", "swipingClassHasValue", "noSwipingClass", "eventPath", "<PERSON><PERSON><PERSON>", "path", "shadowRoot", "noSwipingSelector", "isTargetShadow", "noSwiping", "base", "__closestFrom", "assignedSlot", "found", "getRootNode", "closestElement", "allowClick", "swi<PERSON><PERSON><PERSON><PERSON>", "currentX", "pageX", "currentY", "pageY", "startX", "startY", "edgeSwipeDetection", "iOSEdgeSwipeDetection", "edgeSwipeThreshold", "iOSEdgeSwipeThreshold", "innerWidth", "preventDefault", "allowTouchCallbacks", "isScrolling", "startMoving", "touchStartTime", "swipeDirection", "allowThresholdMove", "focusableElements", "shouldPreventDefault", "allowTouchMove", "touchStartPreventDefault", "touchStartForcePreventDefault", "isContentEditable", "freeMode", "onTouchMove", "pointerIndex", "findIndex", "cachedEv", "pointerId", "targetTouch", "preventedByNestedSwiper", "prevX", "prevY", "touchReleaseOnEdges", "targetTouches", "diffX", "diffY", "sqrt", "touchAngle", "atan2", "zoom", "cancelable", "touchMoveStopPropagation", "nested", "stopPropagation", "touchesDiff", "previousX", "previousY", "oneWayMovement", "touchRatio", "prevTouchesDirection", "touchesDirection", "isLoop", "startTranslate", "evt", "bubbles", "dispatchEvent", "allowMomentumBounce", "grabCursor", "setGrabCursor", "loopFixed", "disableParentSwiper", "resistanceRatio", "resistance", "follow<PERSON><PERSON>", "onTouchEnd", "type", "touchEndTime", "timeDiff", "pathTree", "lastClickTime", "currentPos", "stopIndex", "rewindFirstIndex", "rewindLastIndex", "ratio", "longSwipesMs", "longSwipes", "longSwipesRatio", "shortSwipes", "navigation", "nextEl", "prevEl", "timeout", "onResize", "setBreakpoint", "isVirtualLoop", "autoplay", "running", "paused", "resume", "onClick", "preventClicks", "preventClicksPropagation", "stopImmediatePropagation", "onScroll", "processLazyPreloader", "imageEl", "lazyEl", "lazyPreloaderClass", "onLoad", "dummy<PERSON><PERSON><PERSON>ttached", "dummyEventListener", "capture", "dom<PERSON>ethod", "swiperMethod", "passive", "updateOnWindowResize", "isGridEnabled", "defaults", "init", "resizeObserver", "createElements", "url", "breakpointsBase", "uniqueNavElements", "passiveListeners", "wrapperClass", "_emitClasses", "moduleExtendParams", "allModulesParams", "moduleParamName", "moduleParams", "auto", "prototypes", "transition", "transitionDuration", "moving", "isLocked", "__preventObserver__", "cursor", "unsetGrabCursor", "attachEvents", "bind", "detachEvents", "breakpoint", "getBreakpoint", "currentBreakpoint", "breakpointP<PERSON>ms", "originalParams", "wasMultiRow", "isMultiRow", "wasEnabled", "emitContainerClasses", "fill", "wasModuleEnabled", "isModuleEnabled", "disable", "enable", "directionChanged", "needsReLoop", "changeDirection", "isEnabled", "containerEl", "currentHeight", "innerHeight", "points", "point", "minRatio", "substr", "value", "sort", "b", "wasLocked", "lastSlideRightEdge", "addClasses", "classNames", "suffixes", "entries", "prefix", "resultClasses", "item", "prepareClasses", "autoheight", "centered", "removeClasses", "extendedDefaults", "Swiper", "swipers", "newParams", "modules", "__modules__", "mod", "extendParams", "swiperParams", "passedParams", "eventName", "velocity", "clickTimeout", "velocities", "imagesToLoad", "imagesLoaded", "setProgress", "cls", "className", "getSlideClasses", "updates", "view", "exact", "spv", "breakLoop", "translateValue", "translated", "complete", "newDirection", "needUpdate", "currentDirection", "changeLanguageDirection", "mount", "mounted", "getWrapperSelector", "trim", "getWrapper", "destroy", "deleteInstance", "cleanStyles", "object", "deleteProps", "static", "newDefaults", "module", "m", "installModule", "createElementIfNotDefined", "checkProps", "classesToSelector", "appendSlide", "appendElement", "tempDOM", "innerHTML", "observer", "prependSlide", "prependElement", "addSlide", "activeIndexBuffer", "baseLength", "slidesBuffer", "currentSlide", "removeSlide", "slidesIndexes", "indexToRemove", "removeAllSlides", "effectInit", "overwriteParams", "perspective", "recreateShadows", "getEffectParams", "requireUpdateOnVirtual", "overwriteParamsResult", "_s", "slideShadows", "effect<PERSON>arget", "effectParams", "transformEl", "backfaceVisibility", "effectVirtualTransitionEnd", "transformElements", "allSlides", "transitionEndTarget", "eventTriggered", "parentNode", "getSlide", "createShadow", "shadowClass", "shadow<PERSON><PERSON><PERSON>", "prototypeGroup", "protoMethod", "use", "animationFrame", "resize<PERSON><PERSON>ler", "orientationChangeHandler", "ResizeObserver", "newWidth", "_ref2", "contentBoxSize", "contentRect", "inlineSize", "blockSize", "observe", "unobserve", "observers", "attach", "options", "MutationObserver", "WebkitMutationObserver", "mutations", "observerUpdate", "attributes", "childList", "characterData", "observeParents", "observeSlideChildren", "containerParents", "disconnect", "cssModeTimeout", "cache", "renderSlide", "renderExternal", "renderExternalUpdate", "addSlidesBefore", "addSlidesAfter", "from", "offset", "force", "previousFrom", "previousTo", "previousSlidesGrid", "previousOffset", "offsetProp", "onRendered", "slidesToRender", "prependIndexes", "appendIndexes", "loopFrom", "loopTo", "domSlidesAssigned", "numberOfNewSlides", "newCache", "cachedIndex", "cachedEl", "cachedElIndex", "handle", "kc", "keyCode", "charCode", "pageUpDown", "keyboard", "isPageUp", "isPageDown", "isArrowLeft", "isArrowRight", "isArrowUp", "isArrowDown", "shift<PERSON>ey", "altKey", "ctrl<PERSON>ey", "metaKey", "onlyInViewport", "inView", "swiper<PERSON><PERSON><PERSON>", "swiperHeight", "windowWidth", "windowHeight", "swiperOffset", "swiperCoord", "returnValue", "mousewheel", "releaseOnEdges", "invert", "forceToAxis", "sensitivity", "eventsTarget", "thresholdDel<PERSON>", "thresholdTime", "lastEventBeforeSnap", "lastScrollTime", "recentWheelEvents", "handleMouseEnter", "mouseEntered", "handleMouseLeave", "animateSlider", "newEvent", "delta", "raw", "targetElContainsTarget", "rtlFactor", "sX", "sY", "pX", "pY", "detail", "wheelDelta", "wheelDeltaY", "wheelDeltaX", "HORIZONTAL_AXIS", "deltaY", "deltaX", "deltaMode", "spinX", "spinY", "pixelX", "pixelY", "positions", "sign", "ignoreWheelEvents", "position", "sticky", "shift", "prevEvent", "firstEvent", "snapToThreshold", "autoplayDisableOnInteraction", "stop", "releaseScroll", "hideOnClick", "disabledClass", "hiddenClass", "lockClass", "navigationDisabledClass", "makeElementsArray", "getEl", "res", "toggleEl", "disabled", "subEl", "tagName", "onPrevClick", "onNextClick", "initButton", "destroyButton", "pagination", "clickable", "isHidden", "toggle", "pfx", "bulletSize", "bulletElement", "renderBullet", "renderProgressbar", "renderFraction", "renderCustom", "progressbarOpposite", "dynamicBullets", "dynamicMainBullets", "formatFractionCurrent", "number", "formatFractionTotal", "bulletClass", "bulletActiveClass", "modifierClass", "currentClass", "totalClass", "progressbarFillClass", "progressbarOppositeClass", "clickableClass", "horizontalClass", "verticalClass", "paginationDisabledClass", "bullets", "dynamicBulletIndex", "isPaginationDisabled", "setSideBullets", "bulletEl", "onBulletClick", "total", "firstIndex", "midIndex", "suffix", "bullet", "bulletIndex", "first<PERSON><PERSON>played<PERSON><PERSON>et", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dynamicBulletsLength", "bulletsOffset", "subElIndex", "fractionEl", "textContent", "totalEl", "progressbarDirection", "scale", "scaleX", "scaleY", "progressEl", "render", "paginationHTML", "numberOfBullets", "dragStartPos", "dragSize", "trackSize", "divider", "dragTimeout", "scrollbar", "dragEl", "newSize", "newPos", "hide", "opacity", "display", "getPointerPosition", "clientX", "clientY", "setDragPosition", "positionRatio", "onDragStart", "onDragMove", "onDragEnd", "snapOnRelease", "activeListener", "passiveListener", "eventMethod", "swiperEl", "dragClass", "draggable", "scrollbarDisabledClass", "parallax", "setTransform", "p", "rotate", "currentOpacity", "_swiper", "parallaxEl", "parallaxDuration", "maxRatio", "containerClass", "zoomedSlideClass", "fakeGestureTouched", "fakeGestureMoved", "currentScale", "isScaling", "gesture", "slideWidth", "slideHeight", "imageWrapEl", "image", "minX", "minY", "maxX", "maxY", "touchesStart", "touchesCurrent", "prevPositionX", "prevPositionY", "prevTime", "getDistanceBetweenTouches", "x1", "y1", "x2", "y2", "eventWithinSlide", "onGestureStart", "scaleStart", "originX", "originY", "getScaleOrigin", "transform<PERSON><PERSON>in", "onGestureChange", "scaleMove", "onGestureEnd", "eventWithinZoomContainer", "scaledWidth", "scaledHeight", "onTransitionEnd", "zoomIn", "touchX", "touchY", "offsetX", "offsetY", "translateX", "translateY", "imageWidth", "imageHeight", "translateMinX", "translateMinY", "translateMaxX", "translateMaxY", "touchAction", "forceZoomRatio", "zoomOut", "zoomToggle", "getListeners", "activeListenerWithCapture", "defineProperty", "get", "set", "momentumDurationX", "momentumDurationY", "momentumDistanceX", "newPositionX", "momentumDistanceY", "newPositionY", "momentumDuration", "in", "out", "LinearSpline", "binarySearch", "maxIndex", "minIndex", "guess", "array", "i1", "i3", "interpolate", "removeSpline", "spline", "inverse", "by", "controlElement", "onControllerSwiper", "_t", "controlled", "controlledTranslate", "setControlledTranslate", "getInterpolateFunction", "setControlledTransition", "a11y", "notificationClass", "prevSlideMessage", "nextSlideMessage", "firstSlideMessage", "lastSlideMessage", "paginationBulletMessage", "slideLabelMessage", "containerMessage", "containerRoleDescriptionMessage", "itemRoleDescriptionMessage", "slideRole", "clicked", "liveRegion", "notify", "message", "notification", "makeElFocusable", "makeElNotFocusable", "addElRole", "role", "addElRoleDescription", "description", "addElLabel", "disableEl", "enableEl", "onEnterOrSpaceKey", "click", "hasPagination", "hasClickablePagination", "initNavEl", "wrapperId", "controls", "addElControls", "handlePointerDown", "handlePointerUp", "handleFocus", "isActive", "isVisible", "sourceCapabilities", "firesTouchEvents", "repeat", "round", "random", "live", "addElLive", "updateNavigation", "updatePagination", "root", "<PERSON><PERSON><PERSON><PERSON>", "paths", "slugify", "text", "get<PERSON>ath<PERSON><PERSON><PERSON>", "urlOverride", "URL", "pathArray", "part", "setHistory", "currentState", "state", "scrollToSlide", "setHistoryPopState", "hashNavigation", "watchState", "onHashChange", "newHash", "setHash", "raf", "timeLeft", "waitForTransition", "disableOnInteraction", "stopOnLastSlide", "reverseDirection", "pauseOnMouseEnter", "autoplayTimeLeft", "wasPaused", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touchStartTimeout", "slideChanged", "pausedByInteraction", "autoplayDelayTotal", "autoplayDelayCurrent", "autoplayStartTime", "calcTimeLeft", "run", "delayForce", "currentSlideDelay", "activeSlideEl", "getSlideDelay", "proceed", "start", "pause", "reset", "onVisibilityChange", "visibilityState", "onPointerEnter", "onPointerLeave", "thumbs", "multipleActiveThumbs", "autoScrollOffset", "slideThumbActiveClass", "thumbsContainerClass", "swiperCreated", "onThumbClick", "thumbsSwiper", "thumbsParams", "SwiperClass", "thumbsSwiperParams", "thumbsToActivate", "thumbActiveClass", "useOffset", "currentThumbsIndex", "newThumbsIndex", "newThumbsSlide", "getThumbsElementAndInit", "thumbsElement", "onThumbsSwiper", "watchForThumbsToAppear", "momentum", "momentumRatio", "momentumBounce", "momentumBounceRatio", "momentumVelocityRatio", "minimumVelocity", "lastMoveEvent", "pop", "velocityEvent", "distance", "momentumDistance", "newPosition", "afterBouncePosition", "doBounce", "bounceAmount", "needsLoopFix", "j", "moveDistance", "currentSlideSize", "slidesNumberEvenToRows", "slidesPerRow", "numFullColumns", "newSlideOrderIndex", "column", "row", "groupIndex", "slideIndexInGroup", "columnsInGroup", "order", "fadeEffect", "crossFade", "tx", "ty", "slideOpacity", "cubeEffect", "shadow", "shadowOffset", "shadowScale", "createSlideShadows", "shadowBefore", "shadowAfter", "cubeShadowEl", "wrapperRotate", "slideAngle", "tz", "shadowAngle", "sin", "scale1", "scale2", "zFactor", "flipEffect", "limitRotation", "rotateY", "rotateX", "zIndex", "coverflowEffect", "stretch", "depth", "modifier", "center", "centerOffset", "offsetMultiplier", "translateZ", "slideTransform", "shadowBeforeEl", "shadowAfterEl", "creativeEffect", "limitProgress", "shadowPerProgress", "progressMultiplier", "getTranslateValue", "isCenteredSlides", "margin", "r", "custom", "translateString", "rotateString", "scaleString", "opacityString", "shadowOpacity", "cardsEffect", "perSlideRotate", "perSlideOffset", "tX", "tY", "tZ", "tXAdd", "isSwipeToNext", "isSwipeToPrev", "subProgress"], "sources": ["../node_modules/ssr-window/ssr-window.esm.js", "../src/shared/utils.js", "../src/shared/get-support.js", "../src/shared/get-device.js", "../src/shared/get-browser.js", "../src/core/events-emitter.js", "../src/core/update/index.js", "../src/core/update/updateSize.js", "../src/core/update/updateSlides.js", "../src/core/update/updateAutoHeight.js", "../src/core/update/updateSlidesOffset.js", "../src/core/update/updateSlidesProgress.js", "../src/core/update/updateProgress.js", "../src/core/update/updateSlidesClasses.js", "../src/core/update/updateActiveIndex.js", "../src/core/update/updateClickedSlide.js", "../src/core/translate/index.js", "../src/core/translate/getTranslate.js", "../src/core/translate/setTranslate.js", "../src/core/translate/minTranslate.js", "../src/core/translate/maxTranslate.js", "../src/core/translate/translateTo.js", "../src/core/transition/transitionEmit.js", "../src/core/slide/index.js", "../src/core/slide/slideTo.js", "../src/core/slide/slideToLoop.js", "../src/core/slide/slideNext.js", "../src/core/slide/slidePrev.js", "../src/core/slide/slideReset.js", "../src/core/slide/slideToClosest.js", "../src/core/slide/slideToClickedSlide.js", "../src/core/loop/index.js", "../src/core/loop/loopCreate.js", "../src/core/loop/loopFix.js", "../src/core/loop/loopDestroy.js", "../src/core/events/onTouchStart.js", "../src/core/events/onTouchMove.js", "../src/core/events/onTouchEnd.js", "../src/core/events/onResize.js", "../src/core/events/onClick.js", "../src/core/events/onScroll.js", "../src/shared/process-lazy-preloader.js", "../src/core/events/onLoad.js", "../src/core/events/index.js", "../src/core/breakpoints/setBreakpoint.js", "../src/core/check-overflow/index.js", "../src/core/defaults.js", "../src/core/moduleExtendParams.js", "../src/core/core.js", "../src/core/transition/index.js", "../src/core/transition/setTransition.js", "../src/core/transition/transitionStart.js", "../src/core/transition/transitionEnd.js", "../src/core/grab-cursor/index.js", "../src/core/grab-cursor/setGrabCursor.js", "../src/core/grab-cursor/unsetGrabCursor.js", "../src/core/breakpoints/index.js", "../src/core/breakpoints/getBreakpoint.js", "../src/core/classes/index.js", "../src/core/classes/addClasses.js", "../src/core/classes/removeClasses.js", "../src/shared/create-element-if-not-defined.js", "../src/shared/classes-to-selector.js", "../src/modules/manipulation/methods/appendSlide.js", "../src/modules/manipulation/methods/prependSlide.js", "../src/modules/manipulation/methods/addSlide.js", "../src/modules/manipulation/methods/removeSlide.js", "../src/modules/manipulation/methods/removeAllSlides.js", "../src/shared/effect-init.js", "../src/shared/effect-target.js", "../src/shared/effect-virtual-transition-end.js", "../src/shared/create-shadow.js", "../src/core/modules/resize/resize.js", "../src/core/modules/observer/observer.js", "../src/swiper.js", "../src/modules/virtual/virtual.js", "../src/modules/keyboard/keyboard.js", "../src/modules/mousewheel/mousewheel.js", "../src/modules/navigation/navigation.js", "../src/modules/pagination/pagination.js", "../src/modules/scrollbar/scrollbar.js", "../src/modules/parallax/parallax.js", "../src/modules/zoom/zoom.js", "../src/modules/controller/controller.js", "../src/modules/a11y/a11y.js", "../src/modules/history/history.js", "../src/modules/hash-navigation/hash-navigation.js", "../src/modules/autoplay/autoplay.js", "../src/modules/thumbs/thumbs.js", "../src/modules/free-mode/free-mode.js", "../src/modules/grid/grid.js", "../src/modules/manipulation/manipulation.js", "../src/modules/effect-fade/effect-fade.js", "../src/modules/effect-cube/effect-cube.js", "../src/modules/effect-flip/effect-flip.js", "../src/modules/effect-coverflow/effect-coverflow.js", "../src/modules/effect-creative/effect-creative.js", "../src/modules/effect-cards/effect-cards.js"], "sourcesContent": ["/**\n * SSR Window 4.0.2\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2021, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: December 13, 2021\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n    return (obj !== null &&\n        typeof obj === 'object' &&\n        'constructor' in obj &&\n        obj.constructor === Object);\n}\nfunction extend(target = {}, src = {}) {\n    Object.keys(src).forEach((key) => {\n        if (typeof target[key] === 'undefined')\n            target[key] = src[key];\n        else if (isObject(src[key]) &&\n            isObject(target[key]) &&\n            Object.keys(src[key]).length > 0) {\n            extend(target[key], src[key]);\n        }\n    });\n}\n\nconst ssrDocument = {\n    body: {},\n    addEventListener() { },\n    removeEventListener() { },\n    activeElement: {\n        blur() { },\n        nodeName: '',\n    },\n    querySelector() {\n        return null;\n    },\n    querySelectorAll() {\n        return [];\n    },\n    getElementById() {\n        return null;\n    },\n    createEvent() {\n        return {\n            initEvent() { },\n        };\n    },\n    createElement() {\n        return {\n            children: [],\n            childNodes: [],\n            style: {},\n            setAttribute() { },\n            getElementsByTagName() {\n                return [];\n            },\n        };\n    },\n    createElementNS() {\n        return {};\n    },\n    importNode() {\n        return null;\n    },\n    location: {\n        hash: '',\n        host: '',\n        hostname: '',\n        href: '',\n        origin: '',\n        pathname: '',\n        protocol: '',\n        search: '',\n    },\n};\nfunction getDocument() {\n    const doc = typeof document !== 'undefined' ? document : {};\n    extend(doc, ssrDocument);\n    return doc;\n}\n\nconst ssrWindow = {\n    document: ssrDocument,\n    navigator: {\n        userAgent: '',\n    },\n    location: {\n        hash: '',\n        host: '',\n        hostname: '',\n        href: '',\n        origin: '',\n        pathname: '',\n        protocol: '',\n        search: '',\n    },\n    history: {\n        replaceState() { },\n        pushState() { },\n        go() { },\n        back() { },\n    },\n    CustomEvent: function CustomEvent() {\n        return this;\n    },\n    addEventListener() { },\n    removeEventListener() { },\n    getComputedStyle() {\n        return {\n            getPropertyValue() {\n                return '';\n            },\n        };\n    },\n    Image() { },\n    Date() { },\n    screen: {},\n    setTimeout() { },\n    clearTimeout() { },\n    matchMedia() {\n        return {};\n    },\n    requestAnimationFrame(callback) {\n        if (typeof setTimeout === 'undefined') {\n            callback();\n            return null;\n        }\n        return setTimeout(callback, 0);\n    },\n    cancelAnimationFrame(id) {\n        if (typeof setTimeout === 'undefined') {\n            return;\n        }\n        clearTimeout(id);\n    },\n};\nfunction getWindow() {\n    const win = typeof window !== 'undefined' ? window : {};\n    extend(win, ssrWindow);\n    return win;\n}\n\nexport { extend, getDocument, getWindow, ssrDocument, ssrWindow };\n", "import { getWindow, getDocument } from 'ssr-window';\n\nfunction deleteProps(obj) {\n  const object = obj;\n  Object.keys(object).forEach((key) => {\n    try {\n      object[key] = null;\n    } catch (e) {\n      // no getter for object\n    }\n    try {\n      delete object[key];\n    } catch (e) {\n      // something got wrong\n    }\n  });\n}\nfunction nextTick(callback, delay = 0) {\n  return setTimeout(callback, delay);\n}\nfunction now() {\n  return Date.now();\n}\nfunction getComputedStyle(el) {\n  const window = getWindow();\n  let style;\n  if (window.getComputedStyle) {\n    style = window.getComputedStyle(el, null);\n  }\n  if (!style && el.currentStyle) {\n    style = el.currentStyle;\n  }\n  if (!style) {\n    style = el.style;\n  }\n\n  return style;\n}\nfunction getTranslate(el, axis = 'x') {\n  const window = getWindow();\n  let matrix;\n  let curTransform;\n  let transformMatrix;\n\n  const curStyle = getComputedStyle(el, null);\n\n  if (window.WebKitCSSMatrix) {\n    curTransform = curStyle.transform || curStyle.webkitTransform;\n    if (curTransform.split(',').length > 6) {\n      curTransform = curTransform\n        .split(', ')\n        .map((a) => a.replace(',', '.'))\n        .join(', ');\n    }\n    // Some old versions of Webkit choke when 'none' is passed; pass\n    // empty string instead in this case\n    transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n  } else {\n    transformMatrix =\n      curStyle.MozTransform ||\n      curStyle.OTransform ||\n      curStyle.MsTransform ||\n      curStyle.msTransform ||\n      curStyle.transform ||\n      curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n    matrix = transformMatrix.toString().split(',');\n  }\n\n  if (axis === 'x') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m41;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[12]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[4]);\n  }\n  if (axis === 'y') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m42;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[13]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[5]);\n  }\n  return curTransform || 0;\n}\nfunction isObject(o) {\n  return (\n    typeof o === 'object' &&\n    o !== null &&\n    o.constructor &&\n    Object.prototype.toString.call(o).slice(8, -1) === 'Object'\n  );\n}\nfunction isNode(node) {\n  // eslint-disable-next-line\n  if (typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined') {\n    return node instanceof HTMLElement;\n  }\n  return node && (node.nodeType === 1 || node.nodeType === 11);\n}\nfunction extend(...args) {\n  const to = Object(args[0]);\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  for (let i = 1; i < args.length; i += 1) {\n    const nextSource = args[i];\n    if (nextSource !== undefined && nextSource !== null && !isNode(nextSource)) {\n      const keysArray = Object.keys(Object(nextSource)).filter((key) => noExtend.indexOf(key) < 0);\n      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n        const nextKey = keysArray[nextIndex];\n        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n        if (desc !== undefined && desc.enumerable) {\n          if (isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else if (!isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            to[nextKey] = {};\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else {\n            to[nextKey] = nextSource[nextKey];\n          }\n        }\n      }\n    }\n  }\n  return to;\n}\n\nfunction setCSSProperty(el, varName, varValue) {\n  el.style.setProperty(varName, varValue);\n}\n\nfunction animateCSSModeScroll({ swiper, targetPosition, side }) {\n  const window = getWindow();\n  const startPosition = -swiper.translate;\n  let startTime = null;\n  let time;\n  const duration = swiper.params.speed;\n\n  swiper.wrapperEl.style.scrollSnapType = 'none';\n  window.cancelAnimationFrame(swiper.cssModeFrameID);\n\n  const dir = targetPosition > startPosition ? 'next' : 'prev';\n\n  const isOutOfBound = (current, target) => {\n    return (dir === 'next' && current >= target) || (dir === 'prev' && current <= target);\n  };\n\n  const animate = () => {\n    time = new Date().getTime();\n    if (startTime === null) {\n      startTime = time;\n    }\n\n    const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n    const easeProgress = 0.5 - Math.cos(progress * Math.PI) / 2;\n    let currentPosition = startPosition + easeProgress * (targetPosition - startPosition);\n\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      currentPosition = targetPosition;\n    }\n    swiper.wrapperEl.scrollTo({\n      [side]: currentPosition,\n    });\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.scrollSnapType = '';\n      setTimeout(() => {\n        swiper.wrapperEl.style.overflow = '';\n        swiper.wrapperEl.scrollTo({\n          [side]: currentPosition,\n        });\n      });\n      window.cancelAnimationFrame(swiper.cssModeFrameID);\n      return;\n    }\n    swiper.cssModeFrameID = window.requestAnimationFrame(animate);\n  };\n  animate();\n}\n\nfunction getSlideTransformEl(slideEl) {\n  return (\n    slideEl.querySelector('.swiper-slide-transform') ||\n    (slideEl.shadowEl && slideEl.shadowEl.querySelector('.swiper-slide-transform')) ||\n    slideEl\n  );\n}\n\nfunction findElementsInElements(elements = [], selector = '') {\n  const found = [];\n  elements.forEach((el) => {\n    found.push(...el.querySelectorAll(selector));\n  });\n  return found;\n}\nfunction elementChildren(element, selector = '') {\n  return [...element.children].filter((el) => el.matches(selector));\n}\n\nfunction createElement(tag, classes = []) {\n  const el = document.createElement(tag);\n  el.classList.add(...(Array.isArray(classes) ? classes : [classes]));\n  return el;\n}\nfunction elementOffset(el) {\n  const window = getWindow();\n  const document = getDocument();\n  const box = el.getBoundingClientRect();\n  const body = document.body;\n  const clientTop = el.clientTop || body.clientTop || 0;\n  const clientLeft = el.clientLeft || body.clientLeft || 0;\n  const scrollTop = el === window ? window.scrollY : el.scrollTop;\n  const scrollLeft = el === window ? window.scrollX : el.scrollLeft;\n  return {\n    top: box.top + scrollTop - clientTop,\n    left: box.left + scrollLeft - clientLeft,\n  };\n}\nfunction elementPrevAll(el, selector) {\n  const prevEls = [];\n  while (el.previousElementSibling) {\n    const prev = el.previousElementSibling; // eslint-disable-line\n    if (selector) {\n      if (prev.matches(selector)) prevEls.push(prev);\n    } else prevEls.push(prev);\n    el = prev;\n  }\n  return prevEls;\n}\nfunction elementNextAll(el, selector) {\n  const nextEls = [];\n  while (el.nextElementSibling) {\n    const next = el.nextElementSibling; // eslint-disable-line\n    if (selector) {\n      if (next.matches(selector)) nextEls.push(next);\n    } else nextEls.push(next);\n    el = next;\n  }\n  return nextEls;\n}\nfunction elementStyle(el, prop) {\n  const window = getWindow();\n  return window.getComputedStyle(el, null).getPropertyValue(prop);\n}\nfunction elementIndex(el) {\n  let child = el;\n  let i;\n  if (child) {\n    i = 0;\n    // eslint-disable-next-line\n    while ((child = child.previousSibling) !== null) {\n      if (child.nodeType === 1) i += 1;\n    }\n    return i;\n  }\n  return undefined;\n}\n\nfunction elementParents(el, selector) {\n  const parents = []; // eslint-disable-line\n  let parent = el.parentElement; // eslint-disable-line\n  while (parent) {\n    if (selector) {\n      if (parent.matches(selector)) parents.push(parent);\n    } else {\n      parents.push(parent);\n    }\n    parent = parent.parentElement;\n  }\n  return parents;\n}\n\nfunction elementTransitionEnd(el, callback) {\n  function fireCallBack(e) {\n    if (e.target !== el) return;\n    callback.call(el, e);\n    el.removeEventListener('transitionend', fireCallBack);\n  }\n  if (callback) {\n    el.addEventListener('transitionend', fireCallBack);\n  }\n}\n\nfunction elementOuterSize(el, size, includeMargins) {\n  const window = getWindow();\n  if (includeMargins) {\n    return (\n      el[size === 'width' ? 'offsetWidth' : 'offsetHeight'] +\n      parseFloat(\n        window\n          .getComputedStyle(el, null)\n          .getPropertyValue(size === 'width' ? 'margin-right' : 'margin-top'),\n      ) +\n      parseFloat(\n        window\n          .getComputedStyle(el, null)\n          .getPropertyValue(size === 'width' ? 'margin-left' : 'margin-bottom'),\n      )\n    );\n  }\n  return el.offsetWidth;\n}\n\nexport {\n  animateCSSModeScroll,\n  deleteProps,\n  nextTick,\n  now,\n  getTranslate,\n  isObject,\n  extend,\n  getComputedStyle,\n  setCSSProperty,\n  getSlideTransformEl,\n  // dom\n  findElementsInElements,\n  createElement,\n  elementChildren,\n  elementOffset,\n  elementPrevAll,\n  elementNextAll,\n  elementStyle,\n  elementIndex,\n  elementParents,\n  elementTransitionEnd,\n  elementOuterSize,\n};\n", "import { getWindow, getDocument } from 'ssr-window';\n\nlet support;\n\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n\n  return {\n    smoothScroll: document.documentElement && 'scrollBehavior' in document.documentElement.style,\n\n    touch: !!(\n      'ontouchstart' in window ||\n      (window.DocumentTouch && document instanceof window.DocumentTouch)\n    ),\n  };\n}\n\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\n\nexport { getSupport };\n", "import { getWindow } from 'ssr-window';\nimport { getSupport } from './get-support.js';\n\nlet deviceCached;\n\nfunction calcDevice({ userAgent } = {}) {\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n\n  const device = {\n    ios: false,\n    android: false,\n  };\n\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = [\n    '1024x1366',\n    '1366x1024',\n    '834x1194',\n    '1194x834',\n    '834x1112',\n    '1112x834',\n    '768x1024',\n    '1024x768',\n    '820x1180',\n    '1180x820',\n    '810x1080',\n    '1080x810',\n  ];\n  if (\n    !ipad &&\n    macos &&\n    support.touch &&\n    iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0\n  ) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\n\nfunction getDevice(overrides = {}) {\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\n\nexport { getDevice };\n", "import { getWindow } from 'ssr-window';\n\nlet browser;\n\nfunction calcBrowser() {\n  const window = getWindow();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua\n        .split('Version/')[1]\n        .split(' ')[0]\n        .split('.')\n        .map((num) => Number(num));\n      needPerspectiveFix = major < 16 || (major === 16 && minor < 2);\n    }\n  }\n  return {\n    isSafari: needPerspectiveFix || isSafari(),\n    needPerspectiveFix,\n    isWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent),\n  };\n}\n\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\n\nexport { getBrowser };\n", "/* eslint-disable no-underscore-dangle */\n\nexport default {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach((event) => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler(...args) {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach((event) => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (\n            eventHandler === handler ||\n            (eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler)\n          ) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n\n  emit(...args) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n\n    eventsArray.forEach((event) => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach((eventHandler) => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler) => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  },\n};\n", "import updateSize from './updateSize.js';\nimport updateSlides from './updateSlides.js';\nimport updateAutoHeight from './updateAutoHeight.js';\nimport updateSlidesOffset from './updateSlidesOffset.js';\nimport updateSlidesProgress from './updateSlidesProgress.js';\nimport updateProgress from './updateProgress.js';\nimport updateSlidesClasses from './updateSlidesClasses.js';\nimport updateActiveIndex from './updateActiveIndex.js';\nimport updateClickedSlide from './updateClickedSlide.js';\n\nexport default {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide,\n};\n", "import { elementStyle } from '../../shared/utils.js';\n\nexport default function updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if ((width === 0 && swiper.isHorizontal()) || (height === 0 && swiper.isVertical())) {\n    return;\n  }\n\n  // Subtract paddings\n  width =\n    width -\n    parseInt(elementStyle(el, 'padding-left') || 0, 10) -\n    parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height =\n    height -\n    parseInt(elementStyle(el, 'padding-top') || 0, 10) -\n    parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height,\n  });\n}\n", "import {\n  elementChildren,\n  elementOuterSize,\n  elementStyle,\n  setCSSProperty,\n} from '../../shared/utils.js';\n\nexport default function updateSlides() {\n  const swiper = this;\n  function getDirectionLabel(property) {\n    if (swiper.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom',\n    }[property];\n  }\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(getDirectionLabel(label)) || 0);\n  }\n\n  const params = swiper.params;\n\n  const { wrapperEl, slidesEl, size: swiperSize, rtlTranslate: rtl, wrongRTL } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = (parseFloat(spaceBetween.replace('%', '')) / 100) * swiperSize;\n  }\n\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach((slideEl) => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slidesLength);\n  }\n\n  // Calc slides\n  let slideSize;\n\n  const shouldResetSlideSize =\n    params.slidesPerView === 'auto' &&\n    params.breakpoints &&\n    Object.keys(params.breakpoints).filter((key) => {\n      return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n    }).length > 0;\n\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slidesLength, getDirectionLabel);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal()\n          ? elementOuterSize(slide, 'width', true)\n          : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const { clientWidth, offsetWidth } = slide;\n          slideSize =\n            width +\n            paddingLeft +\n            paddingRight +\n            marginLeft +\n            marginRight +\n            (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n\n      if (slides[i]) {\n        slides[i].style[getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0)\n        slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (\n        (index - Math.min(swiper.params.slidesPerGroupSkip, index)) %\n          swiper.params.slidesPerGroup ===\n        0\n      )\n        snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n\n    swiper.virtualSize += slideSize + spaceBetween;\n\n    prevSlideSize = slideSize;\n\n    index += 1;\n  }\n\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + params.spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[getDirectionLabel('width')] = `${swiper.virtualSize + params.spaceBetween}px`;\n  }\n\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid, getDirectionLabel);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n\n    if (\n      Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) >\n      1\n    ) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil(\n        (swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup,\n      );\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n\n  if (params.spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : getDirectionLabel('marginRight');\n    slides\n      .filter((_, slideIndex) => {\n        if (!params.cssMode || params.loop) return true;\n        if (slideIndex === slides.length - 1) {\n          return false;\n        }\n        return true;\n      })\n      .forEach((slideEl) => {\n        slideEl.style[key] = `${spaceBetween}px`;\n      });\n  }\n\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach((slideSizeValue) => {\n      allSlidesSize += slideSizeValue + (params.spaceBetween ? params.spaceBetween : 0);\n    });\n    allSlidesSize -= params.spaceBetween;\n    const maxSnap = allSlidesSize - swiperSize;\n    snapGrid = snapGrid.map((snap) => {\n      if (snap < 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach((slideSizeValue) => {\n      allSlidesSize += slideSizeValue + (params.spaceBetween ? params.spaceBetween : 0);\n    });\n    allSlidesSize -= params.spaceBetween;\n    if (allSlidesSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid,\n  });\n\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(\n      wrapperEl,\n      '--swiper-centered-offset-after',\n      `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`,\n    );\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map((v) => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map((v) => v + addToSlidesGrid);\n  }\n\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}\n", "export default function updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n\n  const getSlideByIndex = (index) => {\n    if (isVirtual) {\n      return swiper.slides.filter(\n        (el) => parseInt(el.getAttribute('data-swiper-slide-index'), 10) === index,\n      )[0];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach((slide) => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}\n", "export default function updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement\n    ? swiper.isHorizontal()\n      ? swiper.wrapperEl.offsetLeft\n      : swiper.wrapperEl.offsetTop\n    : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset =\n      (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset;\n  }\n}\n", "export default function updateSlidesProgress(translate = (this && this.translate) || 0) {\n  const swiper = this;\n  const params = swiper.params;\n\n  const { slides, rtlTranslate: rtl, snapGrid } = swiper;\n\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n\n  // Visible Slides\n  slides.forEach((slideEl) => {\n    slideEl.classList.remove(params.slideVisibleClass);\n  });\n\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n\n    const slideProgress =\n      (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) /\n      (slide.swiperSlideSize + params.spaceBetween);\n    const originalSlideProgress =\n      (offsetCenter -\n        snapGrid[0] +\n        (params.centeredSlides ? swiper.minTranslate() : 0) -\n        slideOffset) /\n      (slide.swiperSlideSize + params.spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isVisible =\n      (slideBefore >= 0 && slideBefore < swiper.size - 1) ||\n      (slideAfter > 1 && slideAfter <= swiper.size) ||\n      (slideBefore <= 0 && slideAfter >= swiper.size);\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n      slides[i].classList.add(params.slideVisibleClass);\n    }\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}\n", "export default function updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = (swiper && swiper.translate && swiper.translate * multiplier) || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let { progress, isBeginning, isEnd, progressLoop } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndex(\n      swiper.slides.filter((el) => el.getAttribute('data-swiper-slide-index') === '0')[0],\n    );\n    const lastSlideIndex = swiper.getSlideIndex(\n      swiper.slides.filter(\n        (el) => el.getAttribute('data-swiper-slide-index') * 1 === swiper.slides.length - 1,\n      )[0],\n    );\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd,\n  });\n\n  if (params.watchSlidesProgress || (params.centeredSlides && params.autoHeight))\n    swiper.updateSlidesProgress(translate);\n\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if ((wasBeginning && !isBeginning) || (wasEnd && !isEnd)) {\n    swiper.emit('fromEdge');\n  }\n\n  swiper.emit('progress', progress);\n}\n", "import { elementChildren, elementNextAll, elementPrevAll } from '../../shared/utils.js';\n\nexport default function updateSlidesClasses() {\n  const swiper = this;\n\n  const { slides, params, slidesEl, activeIndex } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n\n  const getFilteredSlide = (selector) => {\n    return elementChildren(\n      slidesEl,\n      `.${params.slideClass}${selector}, swiper-slide${selector}`,\n    )[0];\n  };\n  slides.forEach((slideEl) => {\n    slideEl.classList.remove(params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n  });\n\n  let activeSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    activeSlide = slides[activeIndex];\n  }\n\n  if (activeSlide) {\n    // Active classes\n    activeSlide.classList.add(params.slideActiveClass);\n\n    // Next Slide\n    let nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n    if (params.loop && !nextSlide) {\n      nextSlide = slides[0];\n    }\n    if (nextSlide) {\n      nextSlide.classList.add(params.slideNextClass);\n    }\n    // Prev Slide\n    let prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n    if (params.loop && !prevSlide === 0) {\n      prevSlide = slides[slides.length - 1];\n    }\n    if (prevSlide) {\n      prevSlide.classList.add(params.slidePrevClass);\n    }\n  }\n\n  swiper.emitSlidesClasses();\n}\n", "export function getActiveIndexByTranslate(swiper) {\n  const { slidesGrid, params } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (\n        translate >= slidesGrid[i] &&\n        translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2\n      ) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nexport default function updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex,\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n\n  const getVirtualRealIndex = (aIndex) => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.realIndex = getVirtualRealIndex(activeIndex);\n    }\n    return;\n  }\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (swiper.slides[activeIndex]) {\n    realIndex = parseInt(\n      swiper.slides[activeIndex].getAttribute('data-swiper-slide-index') || activeIndex,\n      10,\n    );\n  } else {\n    realIndex = activeIndex;\n  }\n\n  Object.assign(swiper, {\n    snapIndex,\n    realIndex,\n    previousIndex,\n    activeIndex,\n  });\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (previousRealIndex !== realIndex) {\n    swiper.emit('realIndexChange');\n  }\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    swiper.emit('slideChange');\n  }\n}\n", "export default function updateClickedSlide(e) {\n  const swiper = this;\n  const params = swiper.params;\n  const slide = e.closest(`.${params.slideClass}, swiper-slide`);\n  let slideFound = false;\n  let slideIndex;\n\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (\n    params.slideToClickedSlide &&\n    swiper.clickedIndex !== undefined &&\n    swiper.clickedIndex !== swiper.activeIndex\n  ) {\n    swiper.slideToClickedSlide();\n  }\n}\n", "import getTranslate from './getTranslate.js';\nimport setTranslate from './setTranslate.js';\nimport minTranslate from './minTranslate.js';\nimport maxTranslate from './maxTranslate.js';\nimport translateTo from './translateTo.js';\n\nexport default {\n  getTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo,\n};\n", "import { getTranslate } from '../../shared/utils.js';\n\nexport default function getSwiperTranslate(axis = this.isHorizontal() ? 'x' : 'y') {\n  const swiper = this;\n\n  const { params, rtlTranslate: rtl, translate, wrapperEl } = swiper;\n\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  if (rtl) currentTranslate = -currentTranslate;\n\n  return currentTranslate || 0;\n}\n", "export default function setTranslate(translate, byController) {\n  const swiper = this;\n  const { rtlTranslate: rtl, params, wrapperEl, progress } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n\n  swiper.emit('setTranslate', swiper.translate, byController);\n}\n", "export default function minTranslate() {\n  return -this.snapGrid[0];\n}\n", "export default function maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}\n", "import { animateCSSModeScroll } from '../../shared/utils.js';\n\nexport default function translateTo(\n  translate = 0,\n  speed = this.params.speed,\n  runCallbacks = true,\n  translateBounds = true,\n  internal,\n) {\n  const swiper = this;\n\n  const { params, wrapperEl } = swiper;\n\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;\n  else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;\n  else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({ swiper, targetPosition: -newTranslate, side: isH ? 'left' : 'top' });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth',\n      });\n    }\n    return true;\n  }\n\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener(\n            'transitionend',\n            swiper.onTranslateToWrapperTransitionEnd,\n          );\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n\n  return true;\n}\n", "export default function transitionEmit({ swiper, runCallbacks, direction, step }) {\n  const { activeIndex, previousIndex } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';\n    else if (activeIndex < previousIndex) dir = 'prev';\n    else dir = 'reset';\n  }\n\n  swiper.emit(`transition${step}`);\n\n  if (runCallbacks && activeIndex !== previousIndex) {\n    if (dir === 'reset') {\n      swiper.emit(`slideResetTransition${step}`);\n      return;\n    }\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}\n", "import slideTo from './slideTo.js';\nimport slideToLoop from './slideToLoop.js';\nimport slideNext from './slideNext.js';\nimport slidePrev from './slidePrev.js';\nimport slideReset from './slideReset.js';\nimport slideToClosest from './slideToClosest.js';\nimport slideToClickedSlide from './slideToClickedSlide.js';\n\nexport default {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide,\n};\n", "import { animateCSSModeScroll } from '../../shared/utils.js';\n\nexport default function slideTo(\n  index = 0,\n  speed = this.params.speed,\n  runCallbacks = true,\n  internal,\n  initial,\n) {\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled,\n  } = swiper;\n\n  if (\n    (swiper.animating && params.preventInteractionOnTransition) ||\n    (!enabled && !internal && !initial)\n  ) {\n    return false;\n  }\n\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (\n          normalizedTranslate >= normalizedGrid &&\n          normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2\n        ) {\n          slideIndex = i;\n        } else if (\n          normalizedTranslate >= normalizedGrid &&\n          normalizedTranslate < normalizedGridNext\n        ) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (\n      !swiper.allowSlideNext &&\n      translate < swiper.translate &&\n      translate < swiper.minTranslate()\n    ) {\n      return false;\n    }\n    if (\n      !swiper.allowSlidePrev &&\n      translate > swiper.translate &&\n      translate > swiper.maxTranslate()\n    ) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';\n  else if (slideIndex < activeIndex) direction = 'prev';\n  else direction = 'reset';\n\n  // Update Index\n  if ((rtl && -translate === swiper.translate) || (!rtl && translate === swiper.translate)) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({ swiper, targetPosition: t, side: isH ? 'left' : 'top' });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth',\n      });\n    }\n    return true;\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n\n  return true;\n}\n", "export default function slideToLoop(\n  index = 0,\n  speed = this.params.speed,\n  runCallbacks = true,\n  internal,\n) {\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n\n    index = indexAsNumber;\n  }\n\n  const swiper = this;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      newIndex = swiper.getSlideIndex(\n        swiper.slides.filter(\n          (slideEl) => slideEl.getAttribute('data-swiper-slide-index') * 1 === newIndex,\n        )[0],\n      );\n    }\n  }\n\n  return swiper.slideTo(newIndex, speed, runCallbacks, internal);\n}\n", "/* eslint no-unused-vars: \"off\" */\nexport default function slideNext(speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  const { enabled, params, animating } = swiper;\n  if (!enabled) return swiper;\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({ direction: 'next' });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}\n", "/* eslint no-unused-vars: \"off\" */\nexport default function slidePrev(speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  const { params, snapGrid, slidesGrid, rtlTranslate, enabled, animating } = swiper;\n  if (!enabled) return swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n\n    swiper.loopFix({ direction: 'prev' });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map((val) => normalize(val));\n\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && params.cssMode) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (\n      params.slidesPerView === 'auto' &&\n      params.slidesPerGroup === 1 &&\n      params.slidesPerGroupAuto\n    ) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex =\n      swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual\n        ? swiper.virtual.slides.length - 1\n        : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}\n", "/* eslint no-unused-vars: \"off\" */\nexport default function slideReset(speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}\n", "/* eslint no-unused-vars: \"off\" */\nexport default function slideToClosest(\n  speed = this.params.speed,\n  runCallbacks = true,\n  internal,\n  threshold = 0.5,\n) {\n  const swiper = this;\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}\n", "import { elementChildren, nextTick } from '../../shared/utils.js';\n\nexport default function slideToClickedSlide() {\n  const swiper = this;\n  const { params, slidesEl } = swiper;\n\n  const slidesPerView =\n    params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.clickedIndex;\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      if (\n        slideToIndex < swiper.loopedSlides - slidesPerView / 2 ||\n        slideToIndex > swiper.slides.length - swiper.loopedSlides + slidesPerView / 2\n      ) {\n        swiper.loopFix();\n        slideToIndex = swiper.getSlideIndex(\n          elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0],\n        );\n\n        nextTick(() => {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(\n        elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0],\n      );\n\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}\n", "import loopCreate from './loopCreate.js';\nimport loopFix from './loopFix.js';\nimport loopDestroy from './loopDestroy.js';\n\nexport default {\n  loopCreate,\n  loopFix,\n  loopDestroy,\n};\n", "import { elementChildren } from '../../shared/utils.js';\n\nexport default function loopCreate(slideRealIndex) {\n  const swiper = this;\n  const { params, slidesEl } = swiper;\n  if (!params.loop || (swiper.virtual && swiper.params.virtual.enabled)) return;\n\n  const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n\n  slides.forEach((el, index) => {\n    el.setAttribute('data-swiper-slide-index', index);\n  });\n\n  swiper.loopFix({ slideRealIndex, direction: params.centeredSlides ? undefined : 'next' });\n}\n", "export default function loopFix({\n  slideRealIndex,\n  slideTo = true,\n  direction,\n  setTranslate,\n  activeSlideIndex,\n  byC<PERSON>roller,\n  byMousewheel,\n} = {}) {\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n\n  const { slides, allowSlidePrev, allowSlideNext, slidesEl, params } = swiper;\n\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n\n  const slidesPerView =\n    params.slidesPerView === 'auto'\n      ? swiper.slidesPerViewDynamic()\n      : Math.ceil(parseFloat(params.slidesPerView, 10));\n  let loopedSlides = params.loopedSlides || slidesPerView;\n  if (loopedSlides % params.slidesPerGroup !== 0) {\n    loopedSlides += params.slidesPerGroup - (loopedSlides % params.slidesPerGroup);\n  }\n  swiper.loopedSlides = loopedSlides;\n\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n\n  let activeIndex = swiper.activeIndex;\n\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(\n      swiper.slides.filter((el) => el.classList.contains('swiper-slide-active'))[0],\n    );\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  // prepend last slides before start\n  if (activeSlideIndex < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeSlideIndex, params.slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeSlideIndex; i += 1) {\n      const index = i - Math.floor(i / slides.length) * slides.length;\n      prependSlidesIndexes.push(slides.length - index - 1);\n    }\n  } else if (activeSlideIndex /* + slidesPerView */ > swiper.slides.length - loopedSlides * 2) {\n    slidesAppended = Math.max(\n      activeSlideIndex - (swiper.slides.length - loopedSlides * 2),\n      params.slidesPerGroup,\n    );\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / slides.length) * slides.length;\n      appendSlidesIndexes.push(index);\n    }\n  }\n\n  if (isPrev) {\n    prependSlidesIndexes.forEach((index) => {\n      slidesEl.prepend(swiper.slides[index]);\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach((index) => {\n      slidesEl.append(swiper.slides[index]);\n    });\n  }\n\n  swiper.recalcSlides();\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + slidesPrepended, 0, false, true);\n          if (setTranslate) {\n            swiper.touches[swiper.isHorizontal() ? 'startX' : 'startY'] += diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          swiper.slideToLoop(slideRealIndex, 0, false, true);\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touches[swiper.isHorizontal() ? 'startX' : 'startY'] += diff;\n          }\n        }\n      } else {\n        swiper.slideToLoop(slideRealIndex, 0, false, true);\n      }\n    }\n  }\n\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      slideTo: false,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true,\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach((c) => {\n        if (c.params.loop) c.loopFix(loopParams);\n      });\n    } else if (\n      swiper.controller.control instanceof swiper.constructor &&\n      swiper.controller.control.params.loop\n    ) {\n      swiper.controller.control.loopFix(loopParams);\n    }\n  }\n\n  swiper.emit('loopFix');\n}\n", "export default function loopDestroy() {\n  const swiper = this;\n  const { slides, params, slidesEl } = swiper;\n  if (!params.loop || (swiper.virtual && swiper.params.virtual.enabled)) return;\n  swiper.recalcSlides();\n\n  const newSlidesOrder = [];\n  slides.forEach((slideEl) => {\n    const index =\n      typeof slideEl.swiperSlideIndex === 'undefined'\n        ? slideEl.getAttribute('data-swiper-slide-index') * 1\n        : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  slides.forEach((slideEl) => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach((slideEl) => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}\n", "import { getWindow, getDocument } from 'ssr-window';\nimport { now } from '../../shared/utils.js';\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base = this) {\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\n\nexport default function onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  const window = getWindow();\n\n  const data = swiper.touchEventsData;\n  data.evCache.push(event);\n  const { params, touches, enabled } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetEl = e.target;\n\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!swiper.wrapperEl.contains(targetEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = event.composedPath ? event.composedPath() : event.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n\n  const noSwipingSelector = params.noSwipingSelector\n    ? params.noSwipingSelector\n    : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (\n    params.noSwiping &&\n    (isTargetShadow\n      ? closestElement(noSwipingSelector, targetEl)\n      : targetEl.closest(noSwipingSelector))\n  ) {\n    swiper.allowClick = true;\n    return;\n  }\n\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  const edgeSwipeDetection = params.edgeSwipeDetection || params.iOSEdgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold || params.iOSEdgeSwipeThreshold;\n  if (\n    edgeSwipeDetection &&\n    (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)\n  ) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n    } else {\n      return;\n    }\n  }\n\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined,\n  });\n\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (\n    document.activeElement &&\n    document.activeElement.matches(data.focusableElements) &&\n    document.activeElement !== targetEl\n  ) {\n    document.activeElement.blur();\n  }\n\n  const shouldPreventDefault =\n    preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if (\n    (params.touchStartForcePreventDefault || shouldPreventDefault) &&\n    !targetEl.isContentEditable\n  ) {\n    e.preventDefault();\n  }\n  if (\n    swiper.params.freeMode &&\n    swiper.params.freeMode.enabled &&\n    swiper.freeMode &&\n    swiper.animating &&\n    !params.cssMode\n  ) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}\n", "import { getDocument } from 'ssr-window';\nimport { now } from '../../shared/utils.js';\n\nexport default function onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const { params, touches, rtlTranslate: rtl, enabled } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n\n  const pointerIndex = data.evCache.findIndex((cachedEv) => cachedEv.pointerId === e.pointerId);\n  if (pointerIndex >= 0) data.evCache[pointerIndex] = e;\n  const targetTouch = data.evCache.length > 1 ? data.evCache[0] : e;\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        prevX: swiper.touches.currentX,\n        prevY: swiper.touches.currentY,\n        currentX: pageX,\n        currentY: pageY,\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (\n        (pageY < touches.startY && swiper.translate <= swiper.maxTranslate()) ||\n        (pageY > touches.startY && swiper.translate >= swiper.minTranslate())\n      ) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (\n      (pageX < touches.startX && swiper.translate <= swiper.maxTranslate()) ||\n      (pageX > touches.startX && swiper.translate >= swiper.minTranslate())\n    ) {\n      return;\n    }\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  if (e.targetTouches && e.targetTouches.length > 1) return;\n\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold)\n    return;\n\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (\n      (swiper.isHorizontal() && touches.currentY === touches.startY) ||\n      (swiper.isVertical() && touches.currentX === touches.startX)\n    ) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = (Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180) / Math.PI;\n        data.isScrolling = swiper.isHorizontal()\n          ? touchAngle > params.touchAngle\n          : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (\n    data.isScrolling ||\n    (swiper.zoom && swiper.params.zoom && swiper.params.zoom.enabled && data.evCache.length > 1)\n  ) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal()\n    ? touches.currentX - touches.previousX\n    : touches.currentY - touches.previousY;\n\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n\n  const isLoop = swiper.params.loop && !params.cssMode;\n\n  if (!data.isMoved) {\n    if (isLoop) {\n      swiper.loopFix({ direction: swiper.swipeDirection });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true,\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  if (\n    data.isMoved &&\n    prevTouchesDirection !== swiper.touchesDirection &&\n    isLoop &&\n    Math.abs(diff) >= 1\n  ) {\n    // need another loop fix\n    swiper.loopFix({ direction: swiper.swipeDirection, setTranslate: true });\n    loopFixed = true;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n\n  data.currentTranslate = diff + data.startTranslate;\n\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (\n      isLoop &&\n      !loopFixed &&\n      data.currentTranslate >\n        (params.centeredSlides ? swiper.minTranslate() - swiper.size / 2 : swiper.minTranslate())\n    ) {\n      swiper.loopFix({ direction: 'prev', setTranslate: true, activeSlideIndex: 0 });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate =\n          swiper.minTranslate() -\n          1 +\n          (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (\n      isLoop &&\n      !loopFixed &&\n      data.currentTranslate <\n        (params.centeredSlides ? swiper.maxTranslate() + swiper.size / 2 : swiper.maxTranslate())\n    ) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex:\n          swiper.slides.length -\n          (params.slidesPerView === 'auto'\n            ? swiper.slidesPerViewDynamic()\n            : Math.ceil(parseFloat(params.slidesPerView, 10))),\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate =\n          swiper.maxTranslate() +\n          1 -\n          (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (\n    !swiper.allowSlideNext &&\n    swiper.swipeDirection === 'next' &&\n    data.currentTranslate < data.startTranslate\n  ) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (\n    !swiper.allowSlidePrev &&\n    swiper.swipeDirection === 'prev' &&\n    data.currentTranslate > data.startTranslate\n  ) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal()\n          ? touches.currentX - touches.startX\n          : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (\n    (params.freeMode && params.freeMode.enabled && swiper.freeMode) ||\n    params.watchSlidesProgress\n  ) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (swiper.params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}\n", "import { now, nextTick } from '../../shared/utils.js';\n\nexport default function onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const pointerIndex = data.evCache.findIndex((cachedEv) => cachedEv.pointerId === event.pointerId);\n  if (pointerIndex >= 0) {\n    data.evCache.splice(pointerIndex, 1);\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave'].includes(event.type)) {\n    const proceed =\n      event.type === 'pointercancel' && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n\n  const { params, touches, rtlTranslate: rtl, slidesGrid, enabled } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  // Return Grab Cursor\n  if (\n    params.grabCursor &&\n    data.isMoved &&\n    data.isTouched &&\n    (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)\n  ) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || (e.composedPath && e.composedPath());\n    swiper.updateClickedSlide((pathTree && pathTree[0]) || e.target);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n\n  if (\n    !data.isTouched ||\n    !data.isMoved ||\n    !swiper.swipeDirection ||\n    touches.diff === 0 ||\n    data.currentTranslate === data.startTranslate\n  ) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n\n  if (params.cssMode) {\n    return;\n  }\n\n  if (swiper.params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({ currentPos });\n    return;\n  }\n\n  // Find current slide\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (\n    let i = 0;\n    i < slidesGrid.length;\n    i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup\n  ) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex =\n        swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual\n          ? swiper.virtual.slides.length - 1\n          : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio)\n        swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);\n      else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (\n        rewindLastIndex !== null &&\n        ratio < 0 &&\n        Math.abs(ratio) > params.longSwipesRatio\n      ) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget =\n      swiper.navigation &&\n      (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}\n", "let timeout;\nexport default function onResize() {\n  const swiper = this;\n\n  const { params, el } = swiper;\n\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const { allowSlideNext, allowSlidePrev, snapGrid } = swiper;\n\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n\n  swiper.updateSize();\n  swiper.updateSlides();\n\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if (\n    (params.slidesPerView === 'auto' || params.slidesPerView > 1) &&\n    swiper.isEnd &&\n    !swiper.isBeginning &&\n    !swiper.params.centeredSlides &&\n    !isVirtualLoop\n  ) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}\n", "export default function onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}\n", "export default function onScroll() {\n  const swiper = this;\n  const { wrapperEl, rtlTranslate, enabled } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n\n  swiper.emit('setTranslate', swiper.translate, false);\n}\n", "export const processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => (swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`);\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    const lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (lazyEl) lazyEl.remove();\n  }\n};\n", "import { processLazyPreloader } from '../../shared/process-lazy-preloader.js';\n\nexport default function onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  swiper.update();\n}\n", "import { getDocument } from 'ssr-window';\n\nimport onTouchStart from './onTouchStart.js';\nimport onTouchMove from './onTouchMove.js';\nimport onTouchEnd from './onTouchEnd.js';\nimport onResize from './onResize.js';\nimport onClick from './onClick.js';\nimport onScroll from './onScroll.js';\nimport onLoad from './onLoad.js';\n\nlet dummyEventAttached = false;\nfunction dummyEventListener() {}\n\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const { params, el, wrapperEl, device } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n\n  // Touch Events\n  el[domMethod]('pointerdown', swiper.onTouchStart, { passive: false });\n  document[domMethod]('pointermove', swiper.onTouchMove, { passive: false, capture });\n  document[domMethod]('pointerup', swiper.onTouchEnd, { passive: true });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, { passive: true });\n  document[domMethod]('pointerout', swiper.onTouchEnd, { passive: true });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, { passive: true });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](\n      device.ios || device.android\n        ? 'resize orientationchange observerUpdate'\n        : 'resize observerUpdate',\n      onResize,\n      true,\n    );\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, { capture: true });\n};\n\nfunction attachEvents() {\n  const swiper = this;\n  const document = getDocument();\n  const { params } = swiper;\n\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n\n  if (!dummyEventAttached) {\n    document.addEventListener('touchstart', dummyEventListener);\n    dummyEventAttached = true;\n  }\n\n  events(swiper, 'on');\n}\n\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\n\nexport default {\n  attachEvents,\n  detachEvents,\n};\n", "import { extend } from '../../shared/utils.js';\n\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\n\nexport default function setBreakpoint() {\n  const swiper = this;\n  const { realIndex, initialized, params, el } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || (breakpoints && Object.keys(breakpoints).length === 0)) return;\n\n  // Get breakpoint for window width and update parameters\n  const breakpoint = swiper.getBreakpoint(breakpoints, swiper.params.breakpointsBase, swiper.el);\n\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n\n  const wasEnabled = params.enabled;\n\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(\n      `${params.containerModifierClass}grid`,\n      `${params.containerModifierClass}grid-column`,\n    );\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (\n      (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column') ||\n      (!breakpointParams.grid.fill && params.grid.fill === 'column')\n    ) {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach((prop) => {\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n\n  const directionChanged =\n    breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop =\n    params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n\n  const isEnabled = swiper.params.enabled;\n\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev,\n  });\n\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n\n  swiper.currentBreakpoint = breakpoint;\n\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n\n  if (needsReLoop && initialized) {\n    swiper.loopDestroy();\n    swiper.loopCreate(realIndex);\n    swiper.updateSlides();\n  }\n\n  swiper.emit('breakpoint', breakpointParams);\n}\n", "function checkOverflow() {\n  const swiper = this;\n  const { isLocked: wasLocked, params } = swiper;\n  const { slidesOffsetBefore } = params;\n\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge =\n      swiper.slidesGrid[lastSlideIndex] +\n      swiper.slidesSizesGrid[lastSlideIndex] +\n      slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\n\nexport default { checkOverflow };\n", "export default {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n\n  // Overrides\n  width: null,\n  height: null,\n\n  //\n  preventInteractionOnTransition: false,\n\n  // ssr\n  userAgent: null,\n  url: null,\n\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n\n  // Autoheight\n  autoHeight: false,\n\n  // Set wrapper width\n  setWrapperSize: false,\n\n  // Virtual Translate\n  virtualTranslate: false,\n\n  // Effects\n  effect: 'slide', // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0, // in px\n  slidesOffsetAfter: 0, // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n\n  // Round length\n  roundLengths: false,\n\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n\n  // Progress\n  watchSlidesProgress: false,\n\n  // Cursor\n  grabCursor: false,\n\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n\n  // loop\n  loop: false,\n  loopedSlides: null,\n  loopPreventsSliding: true,\n\n  // rewind\n  rewind: false,\n\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null, // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n\n  // Passive Listeners\n  passiveListeners: true,\n\n  maxBackfaceHiddenSlides: 10,\n\n  // NS\n  containerModifierClass: 'swiper-', // NEW\n  slideClass: 'swiper-slide',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n\n  // Callbacks\n  runCallbacksOnInit: true,\n\n  // Internals\n  _emitClasses: false,\n};\n", "import { extend } from '../shared/utils.js';\n\nexport default function moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj = {}) {\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (\n      ['navigation', 'pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 &&\n      params[moduleParamName] === true\n    ) {\n      params[moduleParamName] = { auto: true };\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = { enabled: true };\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = { enabled: false };\n    extend(allModulesParams, obj);\n  };\n}\n", "/* eslint no-param-reassign: \"off\" */\nimport { getDocument } from 'ssr-window';\nimport {\n  extend,\n  now,\n  deleteProps,\n  createElement,\n  elementChildren,\n  elementStyle,\n  elementIndex,\n} from '../shared/utils.js';\nimport { getSupport } from '../shared/get-support.js';\nimport { getDevice } from '../shared/get-device.js';\nimport { getBrowser } from '../shared/get-browser.js';\n\nimport Resize from './modules/resize/resize.js';\nimport Observer from './modules/observer/observer.js';\n\nimport eventsEmitter from './events-emitter.js';\n\nimport update from './update/index.js';\nimport translate from './translate/index.js';\nimport transition from './transition/index.js';\nimport slide from './slide/index.js';\nimport loop from './loop/index.js';\nimport grabCursor from './grab-cursor/index.js';\nimport events from './events/index.js';\nimport breakpoints from './breakpoints/index.js';\nimport classes from './classes/index.js';\nimport checkOverflow from './check-overflow/index.js';\n\nimport defaults from './defaults.js';\nimport moduleExtendParams from './moduleExtendParams.js';\nimport { processLazyPreloader } from '../shared/process-lazy-preloader.js';\n\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events,\n  breakpoints,\n  checkOverflow,\n  classes,\n};\n\nconst extendedDefaults = {};\n\nclass Swiper {\n  constructor(...args) {\n    let el;\n    let params;\n    if (\n      args.length === 1 &&\n      args[0].constructor &&\n      Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object'\n    ) {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n\n    const document = getDocument();\n\n    if (\n      params.el &&\n      typeof params.el === 'string' &&\n      document.querySelectorAll(params.el).length > 1\n    ) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach((containerEl) => {\n        const newParams = extend({}, params, { el: containerEl });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({ userAgent: params.userAgent });\n    swiper.browser = getBrowser();\n\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n\n    const allModulesParams = {};\n    swiper.modules.forEach((mod) => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper),\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach((eventName) => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n\n      // Classes\n      classNames: [],\n\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n\n      //\n      isBeginning: true,\n      isEnd: false,\n\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: now(),\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        evCache: [],\n      },\n\n      // Clicks\n      allowClick: true,\n\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0,\n      },\n\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0,\n    });\n\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n\n  getSlideIndex(slideEl) {\n    const { slidesEl, params } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n\n  recalcSlides() {\n    const swiper = this;\n    const { slidesEl, params } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter((className) => {\n      return (\n        className.indexOf('swiper') === 0 ||\n        className.indexOf(swiper.params.containerModifierClass) === 0\n      );\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n\n    return slideEl.className\n      .split(' ')\n      .filter((className) => {\n        return (\n          className.indexOf('swiper-slide') === 0 ||\n          className.indexOf(swiper.params.slideClass) === 0\n        );\n      })\n      .join(' ');\n  }\n\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach((slideEl) => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({ slideEl, classNames });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n\n  slidesPerViewDynamic(view = 'current', exact = false) {\n    const swiper = this;\n    const { params, slides, slidesGrid, slidesSizesGrid, size: swiperSize, activeIndex } = swiper;\n    let spv = 1;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex].swiperSlideSize;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact\n            ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize\n            : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const { snapGrid, params } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach((imageEl) => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(\n        Math.max(translateValue, swiper.maxTranslate()),\n        swiper.minTranslate(),\n      );\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (swiper.params.freeMode && swiper.params.freeMode.enabled) {\n      setTranslate();\n      if (swiper.params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if (\n        (swiper.params.slidesPerView === 'auto' || swiper.params.slidesPerView > 1) &&\n        swiper.isEnd &&\n        !swiper.params.centeredSlides\n      ) {\n        translated = swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n\n  changeDirection(newDirection, needUpdate = true) {\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (\n      newDirection === currentDirection ||\n      (newDirection !== 'horizontal' && newDirection !== 'vertical')\n    ) {\n      return swiper;\n    }\n\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n\n    swiper.params.direction = newDirection;\n\n    swiper.slides.forEach((slideEl) => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n\n    return swiper;\n  }\n\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if ((swiper.rtl && direction === 'rtl') || (!swiper.rtl && direction === 'ltr')) return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n\n    el.swiper = swiper;\n    if (el.shadowEl) {\n      swiper.isElement = true;\n    }\n\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach((slideEl) => {\n        wrapperEl.append(slideEl);\n      });\n    }\n\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement ? el : wrapperEl,\n      mounted: true,\n\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate:\n        swiper.params.direction === 'horizontal' &&\n        (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box',\n    });\n\n    return true;\n  }\n\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(\n        swiper.params.initialSlide + swiper.virtual.slidesBefore,\n        0,\n        swiper.params.runCallbacksOnInit,\n        false,\n        true,\n      );\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate();\n    }\n\n    // Attach events\n    swiper.attachEvents();\n\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach((imageEl) => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', (e) => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n\n    // Init Flag\n    swiper.initialized = true;\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n\n    return swiper;\n  }\n\n  destroy(deleteInstance = true, cleanStyles = true) {\n    const swiper = this;\n    const { params, el, wrapperEl, slides } = swiper;\n\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      el.removeAttribute('style');\n      wrapperEl.removeAttribute('style');\n      if (slides && slides.length) {\n        slides.forEach((slideEl) => {\n          slideEl.classList.remove(\n            params.slideVisibleClass,\n            params.slideActiveClass,\n            params.slideNextClass,\n            params.slidePrevClass,\n          );\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach((eventName) => {\n      swiper.off(eventName);\n    });\n\n    if (deleteInstance !== false) {\n      swiper.el.swiper = null;\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n\n    return null;\n  }\n\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n\n  static get defaults() {\n    return defaults;\n  }\n\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach((m) => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\n\nObject.keys(prototypes).forEach((prototypeGroup) => {\n  Object.keys(prototypes[prototypeGroup]).forEach((protoMethod) => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\n\nSwiper.use([Resize, Observer]);\n\nexport default Swiper;\n", "import setTransition from './setTransition.js';\nimport transitionStart from './transitionStart.js';\nimport transitionEnd from './transitionEnd.js';\n\nexport default {\n  setTransition,\n  transitionStart,\n  transitionEnd,\n};\n", "export default function setTransition(duration, byController) {\n  const swiper = this;\n\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n  }\n\n  swiper.emit('setTransition', duration, byController);\n}\n", "import transitionEmit from './transitionEmit.js';\n\nexport default function transitionStart(runCallbacks = true, direction) {\n  const swiper = this;\n  const { params } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n\n  transitionEmit({ swiper, runCallbacks, direction, step: 'Start' });\n}\n", "import transitionEmit from './transitionEmit.js';\n\nexport default function transitionEnd(runCallbacks = true, direction) {\n  const swiper = this;\n  const { params } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n\n  transitionEmit({ swiper, runCallbacks, direction, step: 'End' });\n}\n", "import setGrabCursor from './setGrabCursor.js';\nimport unsetGrabCursor from './unsetGrabCursor.js';\n\nexport default {\n  setGrabCursor,\n  unsetGrabCursor,\n};\n", "export default function setGrabCursor(moving) {\n  const swiper = this;\n  if (\n    !swiper.params.simulateTouch ||\n    (swiper.params.watchOverflow && swiper.isLocked) ||\n    swiper.params.cssMode\n  )\n    return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n", "export default function unsetGrabCursor() {\n  const swiper = this;\n  if ((swiper.params.watchOverflow && swiper.isLocked) || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n", "import setBreakpoint from './setBreakpoint.js';\nimport getBreakpoint from './getBreakpoint.js';\n\nexport default { setBreakpoint, getBreakpoint };\n", "import { getWindow } from 'ssr-window';\n\nexport default function getBreakpoint(breakpoints, base = 'window', containerEl) {\n  if (!breakpoints || (base === 'container' && !containerEl)) return undefined;\n  let breakpoint = false;\n\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n\n  const points = Object.keys(breakpoints).map((point) => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return { value, point };\n    }\n    return { value: point, point };\n  });\n\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const { point, value } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}\n", "import addClasses from './addClasses.js';\nimport removeClasses from './removeClasses.js';\n\nexport default { addClasses, removeClasses };\n", "function prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach((item) => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach((classNames) => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\n\nexport default function addClasses() {\n  const swiper = this;\n  const { classNames, params, rtl, el, device } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses([\n    'initialized',\n    params.direction,\n    { 'free-mode': swiper.params.freeMode && params.freeMode.enabled },\n    { 'autoheight': params.autoHeight },\n    { 'rtl': rtl },\n    { 'grid': params.grid && params.grid.rows > 1 },\n    { 'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column' },\n    { 'android': device.android },\n    { 'ios': device.ios },\n    { 'css-mode': params.cssMode },\n    { 'centered': params.cssMode && params.centeredSlides },\n    { 'watch-progress': params.watchSlidesProgress },\n  ], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}\n", "export default function removeClasses() {\n  const swiper = this;\n  const { el, classNames } = swiper;\n\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}\n", "import { createElement, elementChildren } from './utils.js';\n\nexport default function createElementIfNotDefined(swiper, originalParams, params, checkProps) {\n  if (swiper.params.createElements) {\n    Object.keys(checkProps).forEach((key) => {\n      if (!params[key] && params.auto === true) {\n        let element = elementChildren(swiper.el, `.${checkProps[key]}`)[0];\n        if (!element) {\n          element = createElement('div', checkProps[key]);\n          element.className = checkProps[key];\n          swiper.el.append(element);\n        }\n        params[key] = element;\n        originalParams[key] = element;\n      }\n    });\n  }\n  return params;\n}\n", "export default function classesToSelector(classes = '') {\n  return `.${classes\n    .trim()\n    .replace(/([\\.:!\\/])/g, '\\\\$1') // eslint-disable-line\n    .replace(/ /g, '.')}`;\n}\n", "export default function appendSlide(slides) {\n  const swiper = this;\n  const { params, slidesEl } = swiper;\n\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n\n  const appendElement = (slideEl) => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      tempDOM.innerHTML = slideEl;\n      slidesEl.append(tempDOM.children[0]);\n      tempDOM.innerHTML = '';\n    } else {\n      slidesEl.append(slideEl);\n    }\n  };\n\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) appendElement(slides[i]);\n    }\n  } else {\n    appendElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n}\n", "export default function prependSlide(slides) {\n  const swiper = this;\n  const { params, activeIndex, slidesEl } = swiper;\n\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndex + 1;\n  const prependElement = (slideEl) => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      tempDOM.innerHTML = slideEl;\n      slidesEl.prepend(tempDOM.children[0]);\n      tempDOM.innerHTML = '';\n    } else {\n      slidesEl.prepend(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) prependElement(slides[i]);\n    }\n    newActiveIndex = activeIndex + slides.length;\n  } else {\n    prependElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  swiper.slideTo(newActiveIndex, 0, false);\n}\n", "export default function addSlide(index, slides) {\n  const swiper = this;\n  const { params, activeIndex, slidesEl } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n    swiper.recalcSlides();\n  }\n  const baseLength = swiper.slides.length;\n  if (index <= 0) {\n    swiper.prependSlide(slides);\n    return;\n  }\n  if (index >= baseLength) {\n    swiper.appendSlide(slides);\n    return;\n  }\n  let newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + 1 : activeIndexBuffer;\n\n  const slidesBuffer = [];\n  for (let i = baseLength - 1; i >= index; i -= 1) {\n    const currentSlide = swiper.slides[i];\n    currentSlide.remove();\n    slidesBuffer.unshift(currentSlide);\n  }\n\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) slidesEl.append(slides[i]);\n    }\n    newActiveIndex =\n      activeIndexBuffer > index ? activeIndexBuffer + slides.length : activeIndexBuffer;\n  } else {\n    slidesEl.append(slides);\n  }\n\n  for (let i = 0; i < slidesBuffer.length; i += 1) {\n    slidesEl.append(slidesBuffer[i]);\n  }\n\n  swiper.recalcSlides();\n\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\n", "export default function removeSlide(slidesIndexes) {\n  const swiper = this;\n  const { params, activeIndex } = swiper;\n\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndexBuffer;\n  let indexToRemove;\n\n  if (typeof slidesIndexes === 'object' && 'length' in slidesIndexes) {\n    for (let i = 0; i < slidesIndexes.length; i += 1) {\n      indexToRemove = slidesIndexes[i];\n      if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n      if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    }\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  } else {\n    indexToRemove = slidesIndexes;\n    if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n    if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  }\n\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\n", "export default function removeAllSlides() {\n  const swiper = this;\n\n  const slidesIndexes = [];\n  for (let i = 0; i < swiper.slides.length; i += 1) {\n    slidesIndexes.push(i);\n  }\n  swiper.removeSlide(slidesIndexes);\n}\n", "export default function effectInit(params) {\n  const {\n    effect,\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams,\n    perspective,\n    recreateShadows,\n    getEffectParams,\n  } = params;\n\n  on('beforeInit', () => {\n    if (swiper.params.effect !== effect) return;\n    swiper.classNames.push(`${swiper.params.containerModifierClass}${effect}`);\n    if (perspective && perspective()) {\n      swiper.classNames.push(`${swiper.params.containerModifierClass}3d`);\n    }\n\n    const overwriteParamsResult = overwriteParams ? overwriteParams() : {};\n\n    Object.assign(swiper.params, overwriteParamsResult);\n    Object.assign(swiper.originalParams, overwriteParamsResult);\n  });\n  on('setTranslate', () => {\n    if (swiper.params.effect !== effect) return;\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    if (swiper.params.effect !== effect) return;\n    setTransition(duration);\n  });\n\n  on('transitionEnd', () => {\n    if (swiper.params.effect !== effect) return;\n    if (recreateShadows) {\n      if (!getEffectParams || !getEffectParams().slideShadows) return;\n      // remove shadows\n      swiper.slides.forEach((slideEl) => {\n        slideEl\n          .querySelectorAll(\n            '.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left',\n          )\n          .forEach((shadowEl) => shadowEl.remove());\n      });\n      // create new one\n      recreateShadows();\n    }\n  });\n\n  let requireUpdateOnVirtual;\n  on('virtualUpdate', () => {\n    if (swiper.params.effect !== effect) return;\n    if (!swiper.slides.length) {\n      requireUpdateOnVirtual = true;\n    }\n    requestAnimationFrame(() => {\n      if (requireUpdateOnVirtual && swiper.slides && swiper.slides.length) {\n        setTranslate();\n        requireUpdateOnVirtual = false;\n      }\n    });\n  });\n}\n", "import { getSlideTransformEl } from './utils.js';\n\nexport default function effectTarget(effectParams, slideEl) {\n  const transformEl = getSlideTransformEl(slideEl);\n  if (transformEl !== slideEl) {\n    transformEl.style.backfaceVisibility = 'hidden';\n    transformEl.style['-webkit-backface-visibility'] = 'hidden';\n  }\n  return transformEl;\n}\n", "import { elementTransitionEnd } from './utils.js';\n\nexport default function effectVirtualTransitionEnd({\n  swiper,\n  duration,\n  transformElements,\n  allSlides,\n}) {\n  const { activeIndex } = swiper;\n  const getSlide = (el) => {\n    if (!el.parentElement) {\n      // assume shadow root\n      const slide = swiper.slides.filter(\n        (slideEl) => slideEl.shadowEl && slideEl.shadowEl === el.parentNode,\n      )[0];\n      return slide;\n    }\n    return el.parentElement;\n  };\n  if (swiper.params.virtualTranslate && duration !== 0) {\n    let eventTriggered = false;\n    let transitionEndTarget;\n    if (allSlides) {\n      transitionEndTarget = transformElements;\n    } else {\n      transitionEndTarget = transformElements.filter((transformEl) => {\n        const el = transformEl.classList.contains('swiper-slide-transform')\n          ? getSlide(transformEl)\n          : transformEl;\n        return swiper.getSlideIndex(el) === activeIndex;\n      });\n    }\n    transitionEndTarget.forEach((el) => {\n      elementTransitionEnd(el, () => {\n        if (eventTriggered) return;\n        if (!swiper || swiper.destroyed) return;\n        eventTriggered = true;\n        swiper.animating = false;\n        const evt = new window.CustomEvent('transitionend', {\n          bubbles: true,\n          cancelable: true,\n        });\n        swiper.wrapperEl.dispatchEvent(evt);\n      });\n    });\n  }\n}\n", "import { createElement, getSlideTransformEl } from './utils.js';\n\nexport default function createShadow(params, slideEl, side) {\n  const shadowClass = `swiper-slide-shadow${side ? `-${side}` : ''}`;\n  const shadowContainer = getSlideTransformEl(slideEl);\n  let shadowEl = shadowContainer.querySelector(`.${shadowClass}`);\n\n  if (!shadowEl) {\n    shadowEl = createElement('div', `swiper-slide-shadow${side ? `-${side}` : ''}`);\n    shadowContainer.append(shadowEl);\n  }\n  return shadowEl;\n}\n", "import { getWindow } from 'ssr-window';\n\nexport default function Resize({ swiper, on, emit }) {\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver((entries) => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const { width, height } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(({ contentBoxSize, contentRect, target }) => {\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect\n            ? contentRect.width\n            : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect\n            ? contentRect.height\n            : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}\n", "import { getWindow } from 'ssr-window';\nimport { elementParents } from '../../../shared/utils.js';\n\nexport default function Observer({ swiper, extendParams, on, emit }) {\n  const observers = [];\n  const window = getWindow();\n  const attach = (target, options = {}) => {\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc((mutations) => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: typeof options.childList === 'undefined' ? true : options.childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData,\n    });\n\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.el);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.el, {\n      childList: swiper.params.observeSlideChildren,\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, { attributes: false });\n  };\n  const destroy = () => {\n    observers.forEach((observer) => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false,\n  });\n  on('init', init);\n  on('destroy', destroy);\n}\n", "// Swiper Class\nimport Swiper from './core/core.js';\n\n//IMPORT_MODULES\n\nconst modules = [\n  //INSTALL_MODULES\n];\n\nSwiper.use(modules);\n\n//EXPORT\n", "import { getDocument } from 'ssr-window';\nimport { createElement, elementChildren, setCSSProperty } from '../../shared/utils.js';\n\nexport default function Virtual({ swiper, extendParams, on, emit }) {\n  extendParams({\n    virtual: {\n      enabled: false,\n      slides: [],\n      cache: true,\n      renderSlide: null,\n      renderExternal: null,\n      renderExternalUpdate: true,\n      addSlidesBefore: 0,\n      addSlidesAfter: 0,\n    },\n  });\n\n  let cssModeTimeout;\n  const document = getDocument();\n\n  swiper.virtual = {\n    cache: {},\n    from: undefined,\n    to: undefined,\n    slides: [],\n    offset: 0,\n    slidesGrid: [],\n  };\n\n  const tempDOM = document.createElement('div');\n\n  function renderSlide(slide, index) {\n    const params = swiper.params.virtual;\n    if (params.cache && swiper.virtual.cache[index]) {\n      return swiper.virtual.cache[index];\n    }\n    // eslint-disable-next-line\n    let slideEl;\n    if (params.renderSlide) {\n      slideEl = params.renderSlide.call(swiper, slide, index);\n      if (typeof slideEl === 'string') {\n        tempDOM.innerHTML = slideEl;\n        slideEl = tempDOM.children[0];\n      }\n    } else if (swiper.isElement) {\n      slideEl = createElement('swiper-slide');\n    } else {\n      slideEl = createElement('div', swiper.params.slideClass);\n    }\n    slideEl.setAttribute('data-swiper-slide-index', index);\n    if (!params.renderSlide) {\n      slideEl.innerHTML = slide;\n    }\n\n    if (params.cache) swiper.virtual.cache[index] = slideEl;\n    return slideEl;\n  }\n\n  function update(force) {\n    const { slidesPerView, slidesPerGroup, centeredSlides, loop: isLoop } = swiper.params;\n    const { addSlidesBefore, addSlidesAfter } = swiper.params.virtual;\n    const {\n      from: previousFrom,\n      to: previousTo,\n      slides,\n      slidesGrid: previousSlidesGrid,\n      offset: previousOffset,\n    } = swiper.virtual;\n    if (!swiper.params.cssMode) {\n      swiper.updateActiveIndex();\n    }\n\n    const activeIndex = swiper.activeIndex || 0;\n\n    let offsetProp;\n    if (swiper.rtlTranslate) offsetProp = 'right';\n    else offsetProp = swiper.isHorizontal() ? 'left' : 'top';\n\n    let slidesAfter;\n    let slidesBefore;\n    if (centeredSlides) {\n      slidesAfter = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesAfter;\n      slidesBefore = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesBefore;\n    } else {\n      slidesAfter = slidesPerView + (slidesPerGroup - 1) + addSlidesAfter;\n      slidesBefore = (isLoop ? slidesPerView : slidesPerGroup) + addSlidesBefore;\n    }\n    let from = activeIndex - slidesBefore;\n    let to = activeIndex + slidesAfter;\n    if (!isLoop) {\n      from = Math.max(from, 0);\n      to = Math.min(to, slides.length - 1);\n    }\n    let offset = (swiper.slidesGrid[from] || 0) - (swiper.slidesGrid[0] || 0);\n    if (isLoop && activeIndex >= slidesBefore) {\n      from -= slidesBefore;\n      if (!centeredSlides) offset += swiper.slidesGrid[0];\n    } else if (isLoop && activeIndex < slidesBefore) {\n      from = -slidesBefore;\n      if (centeredSlides) offset += swiper.slidesGrid[0];\n    }\n\n    Object.assign(swiper.virtual, {\n      from,\n      to,\n      offset,\n      slidesGrid: swiper.slidesGrid,\n      slidesBefore,\n      slidesAfter,\n    });\n\n    function onRendered() {\n      swiper.updateSlides();\n      swiper.updateProgress();\n      swiper.updateSlidesClasses();\n      emit('virtualUpdate');\n    }\n\n    if (previousFrom === from && previousTo === to && !force) {\n      if (swiper.slidesGrid !== previousSlidesGrid && offset !== previousOffset) {\n        swiper.slides.forEach((slideEl) => {\n          slideEl.style[offsetProp] = `${offset}px`;\n        });\n      }\n      swiper.updateProgress();\n      emit('virtualUpdate');\n      return;\n    }\n    if (swiper.params.virtual.renderExternal) {\n      swiper.params.virtual.renderExternal.call(swiper, {\n        offset,\n        from,\n        to,\n        slides: (function getSlides() {\n          const slidesToRender = [];\n          for (let i = from; i <= to; i += 1) {\n            slidesToRender.push(slides[i]);\n          }\n          return slidesToRender;\n        })(),\n      });\n      if (swiper.params.virtual.renderExternalUpdate) {\n        onRendered();\n      } else {\n        emit('virtualUpdate');\n      }\n      return;\n    }\n    const prependIndexes = [];\n    const appendIndexes = [];\n\n    const getSlideIndex = (index) => {\n      let slideIndex = index;\n      if (index < 0) {\n        slideIndex = slides.length + index;\n      } else if (slideIndex >= slides.length) {\n        // eslint-disable-next-line\n        slideIndex = slideIndex - slides.length;\n      }\n      return slideIndex;\n    };\n\n    if (force) {\n      swiper.slidesEl\n        .querySelectorAll(`.${swiper.params.slideClass}, swiper-slide`)\n        .forEach((slideEl) => {\n          slideEl.remove();\n        });\n    } else {\n      for (let i = previousFrom; i <= previousTo; i += 1) {\n        if (i < from || i > to) {\n          const slideIndex = getSlideIndex(i);\n          swiper.slidesEl\n            .querySelectorAll(\n              `.${swiper.params.slideClass}[data-swiper-slide-index=\"${slideIndex}\"], swiper-slide[data-swiper-slide-index=\"${slideIndex}\"]`,\n            )\n            .forEach((slideEl) => {\n              slideEl.remove();\n            });\n        }\n      }\n    }\n\n    const loopFrom = isLoop ? -slides.length : 0;\n    const loopTo = isLoop ? slides.length * 2 : slides.length;\n    for (let i = loopFrom; i < loopTo; i += 1) {\n      if (i >= from && i <= to) {\n        const slideIndex = getSlideIndex(i);\n        if (typeof previousTo === 'undefined' || force) {\n          appendIndexes.push(slideIndex);\n        } else {\n          if (i > previousTo) appendIndexes.push(slideIndex);\n          if (i < previousFrom) prependIndexes.push(slideIndex);\n        }\n      }\n    }\n    appendIndexes.forEach((index) => {\n      swiper.slidesEl.append(renderSlide(slides[index], index));\n    });\n    if (isLoop) {\n      for (let i = prependIndexes.length - 1; i >= 0; i -= 1) {\n        const index = prependIndexes[i];\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      }\n    } else {\n      prependIndexes.sort((a, b) => b - a);\n      prependIndexes.forEach((index) => {\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      });\n    }\n    elementChildren(swiper.slidesEl, '.swiper-slide, swiper-slide').forEach((slideEl) => {\n      slideEl.style[offsetProp] = `${offset}px`;\n    });\n    onRendered();\n  }\n\n  function appendSlide(slides) {\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.push(slides[i]);\n      }\n    } else {\n      swiper.virtual.slides.push(slides);\n    }\n    update(true);\n  }\n  function prependSlide(slides) {\n    const activeIndex = swiper.activeIndex;\n    let newActiveIndex = activeIndex + 1;\n    let numberOfNewSlides = 1;\n\n    if (Array.isArray(slides)) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.unshift(slides[i]);\n      }\n      newActiveIndex = activeIndex + slides.length;\n      numberOfNewSlides = slides.length;\n    } else {\n      swiper.virtual.slides.unshift(slides);\n    }\n    if (swiper.params.virtual.cache) {\n      const cache = swiper.virtual.cache;\n      const newCache = {};\n      Object.keys(cache).forEach((cachedIndex) => {\n        const cachedEl = cache[cachedIndex];\n        const cachedElIndex = cachedEl.getAttribute('data-swiper-slide-index');\n        if (cachedElIndex) {\n          cachedEl.setAttribute(\n            'data-swiper-slide-index',\n            parseInt(cachedElIndex, 10) + numberOfNewSlides,\n          );\n        }\n        newCache[parseInt(cachedIndex, 10) + numberOfNewSlides] = cachedEl;\n      });\n      swiper.virtual.cache = newCache;\n    }\n    update(true);\n    swiper.slideTo(newActiveIndex, 0);\n  }\n  function removeSlide(slidesIndexes) {\n    if (typeof slidesIndexes === 'undefined' || slidesIndexes === null) return;\n    let activeIndex = swiper.activeIndex;\n    if (Array.isArray(slidesIndexes)) {\n      for (let i = slidesIndexes.length - 1; i >= 0; i -= 1) {\n        swiper.virtual.slides.splice(slidesIndexes[i], 1);\n        if (swiper.params.virtual.cache) {\n          delete swiper.virtual.cache[slidesIndexes[i]];\n        }\n        if (slidesIndexes[i] < activeIndex) activeIndex -= 1;\n        activeIndex = Math.max(activeIndex, 0);\n      }\n    } else {\n      swiper.virtual.slides.splice(slidesIndexes, 1);\n      if (swiper.params.virtual.cache) {\n        delete swiper.virtual.cache[slidesIndexes];\n      }\n      if (slidesIndexes < activeIndex) activeIndex -= 1;\n      activeIndex = Math.max(activeIndex, 0);\n    }\n    update(true);\n    swiper.slideTo(activeIndex, 0);\n  }\n  function removeAllSlides() {\n    swiper.virtual.slides = [];\n    if (swiper.params.virtual.cache) {\n      swiper.virtual.cache = {};\n    }\n    update(true);\n    swiper.slideTo(0, 0);\n  }\n\n  on('beforeInit', () => {\n    if (!swiper.params.virtual.enabled) return;\n    let domSlidesAssigned;\n    if (typeof swiper.passedParams.virtual.slides === 'undefined') {\n      const slides = [...swiper.slidesEl.children].filter((el) =>\n        el.matches(`.${swiper.params.slideClass}, swiper-slide`),\n      );\n      if (slides && slides.length) {\n        swiper.virtual.slides = [...slides];\n        domSlidesAssigned = true;\n        slides.forEach((slideEl, slideIndex) => {\n          slideEl.setAttribute('data-swiper-slide-index', slideIndex);\n          swiper.virtual.cache[slideIndex] = slideEl;\n          slideEl.remove();\n        });\n      }\n    }\n    if (!domSlidesAssigned) {\n      swiper.virtual.slides = swiper.params.virtual.slides;\n    }\n\n    swiper.classNames.push(`${swiper.params.containerModifierClass}virtual`);\n\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n\n    if (!swiper.params.initialSlide) {\n      update();\n    }\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode && !swiper._immediateVirtual) {\n      clearTimeout(cssModeTimeout);\n      cssModeTimeout = setTimeout(() => {\n        update();\n      }, 100);\n    } else {\n      update();\n    }\n  });\n  on('init update resize', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode) {\n      setCSSProperty(swiper.wrapperEl, '--swiper-virtual-size', `${swiper.virtualSize}px`);\n    }\n  });\n\n  Object.assign(swiper.virtual, {\n    appendSlide,\n    prependSlide,\n    removeSlide,\n    removeAllSlides,\n    update,\n  });\n}\n", "/* eslint-disable consistent-return */\nimport { getWindow, getDocument } from 'ssr-window';\nimport { elementOffset, elementParents } from '../../shared/utils.js';\n\nexport default function Keyboard({ swiper, extendParams, on, emit }) {\n  const document = getDocument();\n  const window = getWindow();\n  swiper.keyboard = {\n    enabled: false,\n  };\n  extendParams({\n    keyboard: {\n      enabled: false,\n      onlyInViewport: true,\n      pageUpDown: true,\n    },\n  });\n\n  function handle(event) {\n    if (!swiper.enabled) return;\n\n    const { rtlTranslate: rtl } = swiper;\n    let e = event;\n    if (e.originalEvent) e = e.originalEvent; // jquery fix\n    const kc = e.keyCode || e.charCode;\n    const pageUpDown = swiper.params.keyboard.pageUpDown;\n    const isPageUp = pageUpDown && kc === 33;\n    const isPageDown = pageUpDown && kc === 34;\n    const isArrowLeft = kc === 37;\n    const isArrowRight = kc === 39;\n    const isArrowUp = kc === 38;\n    const isArrowDown = kc === 40;\n    // Directions locks\n    if (\n      !swiper.allowSlideNext &&\n      ((swiper.isHorizontal() && isArrowRight) ||\n        (swiper.isVertical() && isArrowDown) ||\n        isPageDown)\n    ) {\n      return false;\n    }\n    if (\n      !swiper.allowSlidePrev &&\n      ((swiper.isHorizontal() && isArrowLeft) || (swiper.isVertical() && isArrowUp) || isPageUp)\n    ) {\n      return false;\n    }\n    if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) {\n      return undefined;\n    }\n    if (\n      document.activeElement &&\n      document.activeElement.nodeName &&\n      (document.activeElement.nodeName.toLowerCase() === 'input' ||\n        document.activeElement.nodeName.toLowerCase() === 'textarea')\n    ) {\n      return undefined;\n    }\n    if (\n      swiper.params.keyboard.onlyInViewport &&\n      (isPageUp || isPageDown || isArrowLeft || isArrowRight || isArrowUp || isArrowDown)\n    ) {\n      let inView = false;\n      // Check that swiper should be inside of visible area of window\n      if (\n        elementParents(swiper.el, `.${swiper.params.slideClass}, swiper-slide`).length > 0 &&\n        elementParents(swiper.el, `.${swiper.params.slideActiveClass}`).length === 0\n      ) {\n        return undefined;\n      }\n\n      const el = swiper.el;\n      const swiperWidth = el.clientWidth;\n      const swiperHeight = el.clientHeight;\n      const windowWidth = window.innerWidth;\n      const windowHeight = window.innerHeight;\n      const swiperOffset = elementOffset(el);\n      if (rtl) swiperOffset.left -= el.scrollLeft;\n      const swiperCoord = [\n        [swiperOffset.left, swiperOffset.top],\n        [swiperOffset.left + swiperWidth, swiperOffset.top],\n        [swiperOffset.left, swiperOffset.top + swiperHeight],\n        [swiperOffset.left + swiperWidth, swiperOffset.top + swiperHeight],\n      ];\n      for (let i = 0; i < swiperCoord.length; i += 1) {\n        const point = swiperCoord[i];\n        if (point[0] >= 0 && point[0] <= windowWidth && point[1] >= 0 && point[1] <= windowHeight) {\n          if (point[0] === 0 && point[1] === 0) continue; // eslint-disable-line\n          inView = true;\n        }\n      }\n      if (!inView) return undefined;\n    }\n    if (swiper.isHorizontal()) {\n      if (isPageUp || isPageDown || isArrowLeft || isArrowRight) {\n        if (e.preventDefault) e.preventDefault();\n        else e.returnValue = false;\n      }\n      if (((isPageDown || isArrowRight) && !rtl) || ((isPageUp || isArrowLeft) && rtl))\n        swiper.slideNext();\n      if (((isPageUp || isArrowLeft) && !rtl) || ((isPageDown || isArrowRight) && rtl))\n        swiper.slidePrev();\n    } else {\n      if (isPageUp || isPageDown || isArrowUp || isArrowDown) {\n        if (e.preventDefault) e.preventDefault();\n        else e.returnValue = false;\n      }\n      if (isPageDown || isArrowDown) swiper.slideNext();\n      if (isPageUp || isArrowUp) swiper.slidePrev();\n    }\n    emit('keyPress', kc);\n    return undefined;\n  }\n  function enable() {\n    if (swiper.keyboard.enabled) return;\n    document.addEventListener('keydown', handle);\n    swiper.keyboard.enabled = true;\n  }\n  function disable() {\n    if (!swiper.keyboard.enabled) return;\n    document.removeEventListener('keydown', handle);\n    swiper.keyboard.enabled = false;\n  }\n\n  on('init', () => {\n    if (swiper.params.keyboard.enabled) {\n      enable();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.keyboard.enabled) {\n      disable();\n    }\n  });\n\n  Object.assign(swiper.keyboard, {\n    enable,\n    disable,\n  });\n}\n", "/* eslint-disable consistent-return */\nimport { getWindow } from 'ssr-window';\nimport { now, nextTick } from '../../shared/utils.js';\n\nexport default function Mousewheel({ swiper, extendParams, on, emit }) {\n  const window = getWindow();\n\n  extendParams({\n    mousewheel: {\n      enabled: false,\n      releaseOnEdges: false,\n      invert: false,\n      forceToAxis: false,\n      sensitivity: 1,\n      eventsTarget: 'container',\n      thresholdDelta: null,\n      thresholdTime: null,\n    },\n  });\n\n  swiper.mousewheel = {\n    enabled: false,\n  };\n\n  let timeout;\n  let lastScrollTime = now();\n  let lastEventBeforeSnap;\n  const recentWheelEvents = [];\n\n  function normalize(e) {\n    // Reasonable defaults\n    const PIXEL_STEP = 10;\n    const LINE_HEIGHT = 40;\n    const PAGE_HEIGHT = 800;\n\n    let sX = 0;\n    let sY = 0; // spinX, spinY\n    let pX = 0;\n    let pY = 0; // pixelX, pixelY\n\n    // Legacy\n    if ('detail' in e) {\n      sY = e.detail;\n    }\n    if ('wheelDelta' in e) {\n      sY = -e.wheelDelta / 120;\n    }\n    if ('wheelDeltaY' in e) {\n      sY = -e.wheelDeltaY / 120;\n    }\n    if ('wheelDeltaX' in e) {\n      sX = -e.wheelDeltaX / 120;\n    }\n\n    // side scrolling on FF with DOMMouseScroll\n    if ('axis' in e && e.axis === e.HORIZONTAL_AXIS) {\n      sX = sY;\n      sY = 0;\n    }\n\n    pX = sX * PIXEL_STEP;\n    pY = sY * PIXEL_STEP;\n\n    if ('deltaY' in e) {\n      pY = e.deltaY;\n    }\n    if ('deltaX' in e) {\n      pX = e.deltaX;\n    }\n\n    if (e.shiftKey && !pX) {\n      // if user scrolls with shift he wants horizontal scroll\n      pX = pY;\n      pY = 0;\n    }\n\n    if ((pX || pY) && e.deltaMode) {\n      if (e.deltaMode === 1) {\n        // delta in LINE units\n        pX *= LINE_HEIGHT;\n        pY *= LINE_HEIGHT;\n      } else {\n        // delta in PAGE units\n        pX *= PAGE_HEIGHT;\n        pY *= PAGE_HEIGHT;\n      }\n    }\n\n    // Fall-back if spin cannot be determined\n    if (pX && !sX) {\n      sX = pX < 1 ? -1 : 1;\n    }\n    if (pY && !sY) {\n      sY = pY < 1 ? -1 : 1;\n    }\n\n    return {\n      spinX: sX,\n      spinY: sY,\n      pixelX: pX,\n      pixelY: pY,\n    };\n  }\n  function handleMouseEnter() {\n    if (!swiper.enabled) return;\n    swiper.mouseEntered = true;\n  }\n  function handleMouseLeave() {\n    if (!swiper.enabled) return;\n    swiper.mouseEntered = false;\n  }\n  function animateSlider(newEvent) {\n    if (\n      swiper.params.mousewheel.thresholdDelta &&\n      newEvent.delta < swiper.params.mousewheel.thresholdDelta\n    ) {\n      // Prevent if delta of wheel scroll delta is below configured threshold\n      return false;\n    }\n\n    if (\n      swiper.params.mousewheel.thresholdTime &&\n      now() - lastScrollTime < swiper.params.mousewheel.thresholdTime\n    ) {\n      // Prevent if time between scrolls is below configured threshold\n      return false;\n    }\n\n    // If the movement is NOT big enough and\n    // if the last time the user scrolled was too close to the current one (avoid continuously triggering the slider):\n    //   Don't go any further (avoid insignificant scroll movement).\n    if (newEvent.delta >= 6 && now() - lastScrollTime < 60) {\n      // Return false as a default\n      return true;\n    }\n    // If user is scrolling towards the end:\n    //   If the slider hasn't hit the latest slide or\n    //   if the slider is a loop and\n    //   if the slider isn't moving right now:\n    //     Go to next slide and\n    //     emit a scroll event.\n    // Else (the user is scrolling towards the beginning) and\n    // if the slider hasn't hit the first slide or\n    // if the slider is a loop and\n    // if the slider isn't moving right now:\n    //   Go to prev slide and\n    //   emit a scroll event.\n    if (newEvent.direction < 0) {\n      if ((!swiper.isEnd || swiper.params.loop) && !swiper.animating) {\n        swiper.slideNext();\n        emit('scroll', newEvent.raw);\n      }\n    } else if ((!swiper.isBeginning || swiper.params.loop) && !swiper.animating) {\n      swiper.slidePrev();\n      emit('scroll', newEvent.raw);\n    }\n    // If you got here is because an animation has been triggered so store the current time\n    lastScrollTime = new window.Date().getTime();\n    // Return false as a default\n    return false;\n  }\n  function releaseScroll(newEvent) {\n    const params = swiper.params.mousewheel;\n    if (newEvent.direction < 0) {\n      if (swiper.isEnd && !swiper.params.loop && params.releaseOnEdges) {\n        // Return true to animate scroll on edges\n        return true;\n      }\n    } else if (swiper.isBeginning && !swiper.params.loop && params.releaseOnEdges) {\n      // Return true to animate scroll on edges\n      return true;\n    }\n    return false;\n  }\n  function handle(event) {\n    let e = event;\n    let disableParentSwiper = true;\n    if (!swiper.enabled) return;\n    const params = swiper.params.mousewheel;\n\n    if (swiper.params.cssMode) {\n      e.preventDefault();\n    }\n\n    let targetEl = swiper.el;\n    if (swiper.params.mousewheel.eventsTarget !== 'container') {\n      targetEl = document.querySelector(swiper.params.mousewheel.eventsTarget);\n    }\n    const targetElContainsTarget = targetEl && targetEl.contains(e.target);\n    if (!swiper.mouseEntered && !targetElContainsTarget && !params.releaseOnEdges) return true;\n\n    if (e.originalEvent) e = e.originalEvent; // jquery fix\n    let delta = 0;\n    const rtlFactor = swiper.rtlTranslate ? -1 : 1;\n\n    const data = normalize(e);\n\n    if (params.forceToAxis) {\n      if (swiper.isHorizontal()) {\n        if (Math.abs(data.pixelX) > Math.abs(data.pixelY)) delta = -data.pixelX * rtlFactor;\n        else return true;\n      } else if (Math.abs(data.pixelY) > Math.abs(data.pixelX)) delta = -data.pixelY;\n      else return true;\n    } else {\n      delta =\n        Math.abs(data.pixelX) > Math.abs(data.pixelY) ? -data.pixelX * rtlFactor : -data.pixelY;\n    }\n\n    if (delta === 0) return true;\n\n    if (params.invert) delta = -delta;\n\n    // Get the scroll positions\n    let positions = swiper.getTranslate() + delta * params.sensitivity;\n\n    if (positions >= swiper.minTranslate()) positions = swiper.minTranslate();\n    if (positions <= swiper.maxTranslate()) positions = swiper.maxTranslate();\n\n    // When loop is true:\n    //     the disableParentSwiper will be true.\n    // When loop is false:\n    //     if the scroll positions is not on edge,\n    //     then the disableParentSwiper will be true.\n    //     if the scroll on edge positions,\n    //     then the disableParentSwiper will be false.\n    disableParentSwiper = swiper.params.loop\n      ? true\n      : !(positions === swiper.minTranslate() || positions === swiper.maxTranslate());\n\n    if (disableParentSwiper && swiper.params.nested) e.stopPropagation();\n\n    if (!swiper.params.freeMode || !swiper.params.freeMode.enabled) {\n      // Register the new event in a variable which stores the relevant data\n      const newEvent = {\n        time: now(),\n        delta: Math.abs(delta),\n        direction: Math.sign(delta),\n        raw: event,\n      };\n\n      // Keep the most recent events\n      if (recentWheelEvents.length >= 2) {\n        recentWheelEvents.shift(); // only store the last N events\n      }\n      const prevEvent = recentWheelEvents.length\n        ? recentWheelEvents[recentWheelEvents.length - 1]\n        : undefined;\n      recentWheelEvents.push(newEvent);\n\n      // If there is at least one previous recorded event:\n      //   If direction has changed or\n      //   if the scroll is quicker than the previous one:\n      //     Animate the slider.\n      // Else (this is the first time the wheel is moved):\n      //     Animate the slider.\n      if (prevEvent) {\n        if (\n          newEvent.direction !== prevEvent.direction ||\n          newEvent.delta > prevEvent.delta ||\n          newEvent.time > prevEvent.time + 150\n        ) {\n          animateSlider(newEvent);\n        }\n      } else {\n        animateSlider(newEvent);\n      }\n\n      // If it's time to release the scroll:\n      //   Return now so you don't hit the preventDefault.\n      if (releaseScroll(newEvent)) {\n        return true;\n      }\n    } else {\n      // Freemode or scrollContainer:\n\n      // If we recently snapped after a momentum scroll, then ignore wheel events\n      // to give time for the deceleration to finish. Stop ignoring after 500 msecs\n      // or if it's a new scroll (larger delta or inverse sign as last event before\n      // an end-of-momentum snap).\n      const newEvent = {\n        time: now(),\n        delta: Math.abs(delta),\n        direction: Math.sign(delta),\n      };\n\n      const ignoreWheelEvents =\n        lastEventBeforeSnap &&\n        newEvent.time < lastEventBeforeSnap.time + 500 &&\n        newEvent.delta <= lastEventBeforeSnap.delta &&\n        newEvent.direction === lastEventBeforeSnap.direction;\n      if (!ignoreWheelEvents) {\n        lastEventBeforeSnap = undefined;\n\n        let position = swiper.getTranslate() + delta * params.sensitivity;\n        const wasBeginning = swiper.isBeginning;\n        const wasEnd = swiper.isEnd;\n\n        if (position >= swiper.minTranslate()) position = swiper.minTranslate();\n        if (position <= swiper.maxTranslate()) position = swiper.maxTranslate();\n\n        swiper.setTransition(0);\n        swiper.setTranslate(position);\n        swiper.updateProgress();\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n\n        if ((!wasBeginning && swiper.isBeginning) || (!wasEnd && swiper.isEnd)) {\n          swiper.updateSlidesClasses();\n        }\n        if (swiper.params.loop) {\n          swiper.loopFix({\n            direction: newEvent.direction < 0 ? 'next' : 'prev',\n            byMousewheel: true,\n          });\n        }\n\n        if (swiper.params.freeMode.sticky) {\n          // When wheel scrolling starts with sticky (aka snap) enabled, then detect\n          // the end of a momentum scroll by storing recent (N=15?) wheel events.\n          // 1. do all N events have decreasing or same (absolute value) delta?\n          // 2. did all N events arrive in the last M (M=500?) msecs?\n          // 3. does the earliest event have an (absolute value) delta that's\n          //    at least P (P=1?) larger than the most recent event's delta?\n          // 4. does the latest event have a delta that's smaller than Q (Q=6?) pixels?\n          // If 1-4 are \"yes\" then we're near the end of a momentum scroll deceleration.\n          // Snap immediately and ignore remaining wheel events in this scroll.\n          // See comment above for \"remaining wheel events in this scroll\" determination.\n          // If 1-4 aren't satisfied, then wait to snap until 500ms after the last event.\n          clearTimeout(timeout);\n          timeout = undefined;\n          if (recentWheelEvents.length >= 15) {\n            recentWheelEvents.shift(); // only store the last N events\n          }\n          const prevEvent = recentWheelEvents.length\n            ? recentWheelEvents[recentWheelEvents.length - 1]\n            : undefined;\n          const firstEvent = recentWheelEvents[0];\n          recentWheelEvents.push(newEvent);\n          if (\n            prevEvent &&\n            (newEvent.delta > prevEvent.delta || newEvent.direction !== prevEvent.direction)\n          ) {\n            // Increasing or reverse-sign delta means the user started scrolling again. Clear the wheel event log.\n            recentWheelEvents.splice(0);\n          } else if (\n            recentWheelEvents.length >= 15 &&\n            newEvent.time - firstEvent.time < 500 &&\n            firstEvent.delta - newEvent.delta >= 1 &&\n            newEvent.delta <= 6\n          ) {\n            // We're at the end of the deceleration of a momentum scroll, so there's no need\n            // to wait for more events. Snap ASAP on the next tick.\n            // Also, because there's some remaining momentum we'll bias the snap in the\n            // direction of the ongoing scroll because it's better UX for the scroll to snap\n            // in the same direction as the scroll instead of reversing to snap.  Therefore,\n            // if it's already scrolled more than 20% in the current direction, keep going.\n            const snapToThreshold = delta > 0 ? 0.8 : 0.2;\n            lastEventBeforeSnap = newEvent;\n            recentWheelEvents.splice(0);\n            timeout = nextTick(() => {\n              swiper.slideToClosest(swiper.params.speed, true, undefined, snapToThreshold);\n            }, 0); // no delay; move on next tick\n          }\n          if (!timeout) {\n            // if we get here, then we haven't detected the end of a momentum scroll, so\n            // we'll consider a scroll \"complete\" when there haven't been any wheel events\n            // for 500ms.\n            timeout = nextTick(() => {\n              const snapToThreshold = 0.5;\n              lastEventBeforeSnap = newEvent;\n              recentWheelEvents.splice(0);\n              swiper.slideToClosest(swiper.params.speed, true, undefined, snapToThreshold);\n            }, 500);\n          }\n        }\n\n        // Emit event\n        if (!ignoreWheelEvents) emit('scroll', e);\n\n        // Stop autoplay\n        if (swiper.params.autoplay && swiper.params.autoplayDisableOnInteraction)\n          swiper.autoplay.stop();\n        // Return page scroll on edge positions\n        if (position === swiper.minTranslate() || position === swiper.maxTranslate()) return true;\n      }\n    }\n\n    if (e.preventDefault) e.preventDefault();\n    else e.returnValue = false;\n    return false;\n  }\n\n  function events(method) {\n    let targetEl = swiper.el;\n    if (swiper.params.mousewheel.eventsTarget !== 'container') {\n      targetEl = document.querySelector(swiper.params.mousewheel.eventsTarget);\n    }\n    targetEl[method]('mouseenter', handleMouseEnter);\n    targetEl[method]('mouseleave', handleMouseLeave);\n    targetEl[method]('wheel', handle);\n  }\n\n  function enable() {\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.removeEventListener('wheel', handle);\n      return true;\n    }\n    if (swiper.mousewheel.enabled) return false;\n    events('addEventListener');\n    swiper.mousewheel.enabled = true;\n    return true;\n  }\n  function disable() {\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.addEventListener(event, handle);\n      return true;\n    }\n    if (!swiper.mousewheel.enabled) return false;\n    events('removeEventListener');\n    swiper.mousewheel.enabled = false;\n    return true;\n  }\n\n  on('init', () => {\n    if (!swiper.params.mousewheel.enabled && swiper.params.cssMode) {\n      disable();\n    }\n    if (swiper.params.mousewheel.enabled) enable();\n  });\n  on('destroy', () => {\n    if (swiper.params.cssMode) {\n      enable();\n    }\n    if (swiper.mousewheel.enabled) disable();\n  });\n\n  Object.assign(swiper.mousewheel, {\n    enable,\n    disable,\n  });\n}\n", "import createElementIfNotDefined from '../../shared/create-element-if-not-defined.js';\n\nexport default function Navigation({ swiper, extendParams, on, emit }) {\n  extendParams({\n    navigation: {\n      nextEl: null,\n      prevEl: null,\n\n      hideOnClick: false,\n      disabledClass: 'swiper-button-disabled',\n      hiddenClass: 'swiper-button-hidden',\n      lockClass: 'swiper-button-lock',\n      navigationDisabledClass: 'swiper-navigation-disabled',\n    },\n  });\n\n  swiper.navigation = {\n    nextEl: null,\n    prevEl: null,\n  };\n\n  const makeElementsArray = (el) => {\n    if (!Array.isArray(el)) el = [el].filter((e) => !!e);\n    return el;\n  };\n\n  function getEl(el) {\n    let res;\n    if (el && typeof el === 'string' && swiper.isElement) {\n      res = swiper.el.shadowRoot.querySelector(el);\n      if (res) return res;\n    }\n    if (el) {\n      if (typeof el === 'string') res = [...document.querySelectorAll(el)];\n      if (\n        swiper.params.uniqueNavElements &&\n        typeof el === 'string' &&\n        res.length > 1 &&\n        swiper.el.querySelectorAll(el).length === 1\n      ) {\n        res = swiper.el.querySelector(el);\n      }\n    }\n    if (el && !res) return el;\n    // if (Array.isArray(res) && res.length === 1) res = res[0];\n    return res;\n  }\n\n  function toggleEl(el, disabled) {\n    const params = swiper.params.navigation;\n    el = makeElementsArray(el);\n    el.forEach((subEl) => {\n      if (subEl) {\n        subEl.classList[disabled ? 'add' : 'remove'](...params.disabledClass.split(' '));\n        if (subEl.tagName === 'BUTTON') subEl.disabled = disabled;\n        if (swiper.params.watchOverflow && swiper.enabled) {\n          subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n        }\n      }\n    });\n  }\n  function update() {\n    // Update Navigation Buttons\n    const { nextEl, prevEl } = swiper.navigation;\n    if (swiper.params.loop) {\n      toggleEl(prevEl, false);\n      toggleEl(nextEl, false);\n      return;\n    }\n\n    toggleEl(prevEl, swiper.isBeginning && !swiper.params.rewind);\n    toggleEl(nextEl, swiper.isEnd && !swiper.params.rewind);\n  }\n  function onPrevClick(e) {\n    e.preventDefault();\n    if (swiper.isBeginning && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slidePrev();\n    emit('navigationPrev');\n  }\n  function onNextClick(e) {\n    e.preventDefault();\n    if (swiper.isEnd && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slideNext();\n    emit('navigationNext');\n  }\n  function init() {\n    const params = swiper.params.navigation;\n\n    swiper.params.navigation = createElementIfNotDefined(\n      swiper,\n      swiper.originalParams.navigation,\n      swiper.params.navigation,\n      {\n        nextEl: 'swiper-button-next',\n        prevEl: 'swiper-button-prev',\n      },\n    );\n    if (!(params.nextEl || params.prevEl)) return;\n\n    let nextEl = getEl(params.nextEl);\n    let prevEl = getEl(params.prevEl);\n\n    Object.assign(swiper.navigation, {\n      nextEl,\n      prevEl,\n    });\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n\n    const initButton = (el, dir) => {\n      if (el) {\n        el.addEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      }\n      if (!swiper.enabled && el) {\n        el.classList.add(...params.lockClass.split(' '));\n      }\n    };\n\n    nextEl.forEach((el) => initButton(el, 'next'));\n    prevEl.forEach((el) => initButton(el, 'prev'));\n  }\n  function destroy() {\n    let { nextEl, prevEl } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const destroyButton = (el, dir) => {\n      el.removeEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      el.classList.remove(...swiper.params.navigation.disabledClass.split(' '));\n    };\n    nextEl.forEach((el) => destroyButton(el, 'next'));\n    prevEl.forEach((el) => destroyButton(el, 'prev'));\n  }\n\n  on('init', () => {\n    if (swiper.params.navigation.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      update();\n    }\n  });\n  on('toEdge fromEdge lock unlock', () => {\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let { nextEl, prevEl } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    [...nextEl, ...prevEl]\n      .filter((el) => !!el)\n      .forEach((el) =>\n        el.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.navigation.lockClass),\n      );\n  });\n  on('click', (_s, e) => {\n    let { nextEl, prevEl } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const targetEl = e.target;\n    if (\n      swiper.params.navigation.hideOnClick &&\n      !prevEl.includes(targetEl) &&\n      !nextEl.includes(targetEl)\n    ) {\n      if (\n        swiper.pagination &&\n        swiper.params.pagination &&\n        swiper.params.pagination.clickable &&\n        (swiper.pagination.el === targetEl || swiper.pagination.el.contains(targetEl))\n      )\n        return;\n      let isHidden;\n      if (nextEl.length) {\n        isHidden = nextEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      } else if (prevEl.length) {\n        isHidden = prevEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      }\n      if (isHidden === true) {\n        emit('navigationShow');\n      } else {\n        emit('navigationHide');\n      }\n      [...nextEl, ...prevEl]\n        .filter((el) => !!el)\n        .forEach((el) => el.classList.toggle(swiper.params.navigation.hiddenClass));\n    }\n  });\n\n  const enable = () => {\n    swiper.el.classList.remove(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    init();\n    update();\n  };\n\n  const disable = () => {\n    swiper.el.classList.add(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    destroy();\n  };\n\n  Object.assign(swiper.navigation, {\n    enable,\n    disable,\n    update,\n    init,\n    destroy,\n  });\n}\n", "import classesToSelector from '../../shared/classes-to-selector.js';\nimport createElementIfNotDefined from '../../shared/create-element-if-not-defined.js';\nimport { elementIndex, elementOuterSize, elementParents } from '../../shared/utils.js';\n\nexport default function Pagination({ swiper, extendParams, on, emit }) {\n  const pfx = 'swiper-pagination';\n  extendParams({\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets', // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: (number) => number,\n      formatFractionTotal: (number) => number,\n      bulletClass: `${pfx}-bullet`,\n      bulletActiveClass: `${pfx}-bullet-active`,\n      modifierClass: `${pfx}-`,\n      currentClass: `${pfx}-current`,\n      totalClass: `${pfx}-total`,\n      hiddenClass: `${pfx}-hidden`,\n      progressbarFillClass: `${pfx}-progressbar-fill`,\n      progressbarOppositeClass: `${pfx}-progressbar-opposite`,\n      clickableClass: `${pfx}-clickable`,\n      lockClass: `${pfx}-lock`,\n      horizontalClass: `${pfx}-horizontal`,\n      verticalClass: `${pfx}-vertical`,\n      paginationDisabledClass: `${pfx}-disabled`,\n    },\n  });\n\n  swiper.pagination = {\n    el: null,\n    bullets: [],\n  };\n\n  let bulletSize;\n  let dynamicBulletIndex = 0;\n\n  const makeElementsArray = (el) => {\n    if (!Array.isArray(el)) el = [el].filter((e) => !!e);\n    return el;\n  };\n\n  function isPaginationDisabled() {\n    return (\n      !swiper.params.pagination.el ||\n      !swiper.pagination.el ||\n      (Array.isArray(swiper.pagination.el) && swiper.pagination.el.length === 0)\n    );\n  }\n\n  function setSideBullets(bulletEl, position) {\n    const { bulletActiveClass } = swiper.params.pagination;\n    if (!bulletEl) return;\n    bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n    if (bulletEl) {\n      bulletEl.classList.add(`${bulletActiveClass}-${position}`);\n      bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n      if (bulletEl) {\n        bulletEl.classList.add(`${bulletActiveClass}-${position}-${position}`);\n      }\n    }\n  }\n\n  function onBulletClick(e) {\n    const bulletEl = e.target.closest(classesToSelector(swiper.params.pagination.bulletClass));\n    if (!bulletEl) {\n      return;\n    }\n    e.preventDefault();\n    const index = elementIndex(bulletEl) * swiper.params.slidesPerGroup;\n    if (swiper.params.loop) {\n      if (swiper.realIndex === index) return;\n      if (index < swiper.loopedSlides || index > swiper.slides.length - swiper.loopedSlides) {\n        swiper.loopFix({\n          direction: index < swiper.loopedSlides ? 'prev' : 'next',\n          activeSlideIndex: index,\n          slideTo: false,\n        });\n      }\n\n      swiper.slideToLoop(index);\n    } else {\n      swiper.slideTo(index);\n    }\n  }\n\n  function update() {\n    // Render || Update Pagination bullets/items\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    // Current/Total\n    let current;\n    const slidesLength =\n      swiper.virtual && swiper.params.virtual.enabled\n        ? swiper.virtual.slides.length\n        : swiper.slides.length;\n    const total = swiper.params.loop\n      ? Math.ceil(slidesLength / swiper.params.slidesPerGroup)\n      : swiper.snapGrid.length;\n    if (swiper.params.loop) {\n      current =\n        swiper.params.slidesPerGroup > 1\n          ? Math.floor(swiper.realIndex / swiper.params.slidesPerGroup)\n          : swiper.realIndex;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n    } else {\n      current = swiper.activeIndex || 0;\n    }\n    // Types\n    if (\n      params.type === 'bullets' &&\n      swiper.pagination.bullets &&\n      swiper.pagination.bullets.length > 0\n    ) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n      if (params.dynamicBullets) {\n        bulletSize = elementOuterSize(bullets[0], swiper.isHorizontal() ? 'width' : 'height', true);\n        el.forEach((subEl) => {\n          subEl.style[swiper.isHorizontal() ? 'width' : 'height'] = `${\n            bulletSize * (params.dynamicMainBullets + 4)\n          }px`;\n        });\n\n        if (params.dynamicMainBullets > 1 && swiper.previousIndex !== undefined) {\n          dynamicBulletIndex += current - (swiper.previousIndex || 0);\n          if (dynamicBulletIndex > params.dynamicMainBullets - 1) {\n            dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (dynamicBulletIndex < 0) {\n            dynamicBulletIndex = 0;\n          }\n        }\n        firstIndex = Math.max(current - dynamicBulletIndex, 0);\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n      bullets.forEach((bulletEl) => {\n        bulletEl.classList.remove(\n          ...['', '-next', '-next-next', '-prev', '-prev-prev', '-main'].map(\n            (suffix) => `${params.bulletActiveClass}${suffix}`,\n          ),\n        );\n      });\n\n      if (el.length > 1) {\n        bullets.forEach((bullet) => {\n          const bulletIndex = elementIndex(bullet);\n          if (bulletIndex === current) {\n            bullet.classList.add(params.bulletActiveClass);\n          }\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              bullet.classList.add(`${params.bulletActiveClass}-main`);\n            }\n            if (bulletIndex === firstIndex) {\n              setSideBullets(bullet, 'prev');\n            }\n            if (bulletIndex === lastIndex) {\n              setSideBullets(bullet, 'next');\n            }\n          }\n        });\n      } else {\n        const bullet = bullets[current];\n        if (bullet) {\n          bullet.classList.add(params.bulletActiveClass);\n        }\n\n        if (params.dynamicBullets) {\n          const firstDisplayedBullet = bullets[firstIndex];\n          const lastDisplayedBullet = bullets[lastIndex];\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            if (bullets[i]) {\n              bullets[i].classList.add(`${params.bulletActiveClass}-main`);\n            }\n          }\n\n          setSideBullets(firstDisplayedBullet, 'prev');\n          setSideBullets(lastDisplayedBullet, 'next');\n        }\n      }\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset =\n          (bulletSize * dynamicBulletsLength - bulletSize) / 2 - midIndex * bulletSize;\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.forEach((bullet) => {\n          bullet.style[swiper.isHorizontal() ? offsetProp : 'top'] = `${bulletsOffset}px`;\n        });\n      }\n    }\n    el.forEach((subEl, subElIndex) => {\n      if (params.type === 'fraction') {\n        subEl.querySelectorAll(classesToSelector(params.currentClass)).forEach((fractionEl) => {\n          fractionEl.textContent = params.formatFractionCurrent(current + 1);\n        });\n        subEl.querySelectorAll(classesToSelector(params.totalClass)).forEach((totalEl) => {\n          totalEl.textContent = params.formatFractionTotal(total);\n        });\n      }\n      if (params.type === 'progressbar') {\n        let progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        const scale = (current + 1) / total;\n        let scaleX = 1;\n        let scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        subEl\n          .querySelectorAll(classesToSelector(params.progressbarFillClass))\n          .forEach((progressEl) => {\n            progressEl.style.transform = `translate3d(0,0,0) scaleX(${scaleX}) scaleY(${scaleY})`;\n            progressEl.style.transitionDuration = `${swiper.params.speed}ms`;\n          });\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        subEl.innerHTML = params.renderCustom(swiper, current + 1, total);\n        if (subElIndex === 0) emit('paginationRender', subEl);\n      } else {\n        if (subElIndex === 0) emit('paginationRender', subEl);\n        emit('paginationUpdate', subEl);\n      }\n      if (swiper.params.watchOverflow && swiper.enabled) {\n        subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n      }\n    });\n  }\n  function render() {\n    // Render Container\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength =\n      swiper.virtual && swiper.params.virtual.enabled\n        ? swiper.virtual.slides.length\n        : swiper.slides.length;\n\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    let paginationHTML = '';\n    if (params.type === 'bullets') {\n      let numberOfBullets = swiper.params.loop\n        ? Math.ceil(slidesLength / swiper.params.slidesPerGroup)\n        : swiper.snapGrid.length;\n      if (\n        swiper.params.freeMode &&\n        swiper.params.freeMode.enabled &&\n        numberOfBullets > slidesLength\n      ) {\n        numberOfBullets = slidesLength;\n      }\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          paginationHTML += `<${params.bulletElement} class=\"${params.bulletClass}\"></${params.bulletElement}>`;\n        }\n      }\n    }\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML =\n          `<span class=\"${params.currentClass}\"></span>` +\n          ' / ' +\n          `<span class=\"${params.totalClass}\"></span>`;\n      }\n    }\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = `<span class=\"${params.progressbarFillClass}\"></span>`;\n      }\n    }\n\n    el.forEach((subEl) => {\n      if (params.type !== 'custom') {\n        subEl.innerHTML = paginationHTML || '';\n      }\n      if (params.type === 'bullets') {\n        swiper.pagination.bullets = [\n          ...subEl.querySelectorAll(classesToSelector(params.bulletClass)),\n        ];\n      }\n    });\n    if (params.type !== 'custom') {\n      emit('paginationRender', el[0]);\n    }\n  }\n  function init() {\n    swiper.params.pagination = createElementIfNotDefined(\n      swiper,\n      swiper.originalParams.pagination,\n      swiper.params.pagination,\n      { el: 'swiper-pagination' },\n    );\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.shadowRoot.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = [...document.querySelectorAll(params.el)];\n    }\n    if (!el) {\n      el = params.el;\n    }\n    if (!el || el.length === 0) return;\n\n    if (\n      swiper.params.uniqueNavElements &&\n      typeof params.el === 'string' &&\n      Array.isArray(el) &&\n      el.length > 1\n    ) {\n      el = [...swiper.el.querySelectorAll(params.el)];\n      // check if it belongs to another nested Swiper\n      if (el.length > 1) {\n        el = el.filter((subEl) => {\n          if (elementParents(subEl, '.swiper')[0] !== swiper.el) return false;\n          return true;\n        })[0];\n      }\n    }\n    if (Array.isArray(el) && el.length === 1) el = el[0];\n\n    Object.assign(swiper.pagination, {\n      el,\n    });\n\n    el = makeElementsArray(el);\n    el.forEach((subEl) => {\n      if (params.type === 'bullets' && params.clickable) {\n        subEl.classList.add(params.clickableClass);\n      }\n\n      subEl.classList.add(params.modifierClass + params.type);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        subEl.classList.add(`${params.modifierClass}${params.type}-dynamic`);\n        dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        subEl.classList.add(params.progressbarOppositeClass);\n      }\n\n      if (params.clickable) {\n        subEl.addEventListener('click', onBulletClick);\n      }\n\n      if (!swiper.enabled) {\n        subEl.classList.add(params.lockClass);\n      }\n    });\n  }\n\n  function destroy() {\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach((subEl) => {\n        subEl.classList.remove(params.hiddenClass);\n        subEl.classList.remove(params.modifierClass + params.type);\n        subEl.classList.remove(\n          swiper.isHorizontal() ? params.horizontalClass : params.verticalClass,\n        );\n        if (params.clickable) {\n          subEl.removeEventListener('click', onBulletClick);\n        }\n      });\n    }\n\n    if (swiper.pagination.bullets)\n      swiper.pagination.bullets.forEach((subEl) =>\n        subEl.classList.remove(params.bulletActiveClass),\n      );\n  }\n\n  on('init', () => {\n    if (swiper.params.pagination.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      render();\n      update();\n    }\n  });\n  on('activeIndexChange', () => {\n    if (typeof swiper.snapIndex === 'undefined') {\n      update();\n    }\n  });\n  on('snapIndexChange', () => {\n    update();\n  });\n  on('snapGridLengthChange', () => {\n    render();\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let { el } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach((subEl) =>\n        subEl.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.pagination.lockClass),\n      );\n    }\n  });\n  on('lock unlock', () => {\n    update();\n  });\n  on('click', (_s, e) => {\n    const targetEl = e.target;\n    let { el } = swiper.pagination;\n    if (!Array.isArray(el)) el = [el].filter((element) => !!element);\n    if (\n      swiper.params.pagination.el &&\n      swiper.params.pagination.hideOnClick &&\n      el &&\n      el.length > 0 &&\n      !targetEl.classList.contains(swiper.params.pagination.bulletClass)\n    ) {\n      if (\n        swiper.navigation &&\n        ((swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl) ||\n          (swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl))\n      )\n        return;\n      const isHidden = el[0].classList.contains(swiper.params.pagination.hiddenClass);\n      if (isHidden === true) {\n        emit('paginationShow');\n      } else {\n        emit('paginationHide');\n      }\n      el.forEach((subEl) => subEl.classList.toggle(swiper.params.pagination.hiddenClass));\n    }\n  });\n\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.pagination.paginationDisabledClass);\n    let { el } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach((subEl) =>\n        subEl.classList.remove(swiper.params.pagination.paginationDisabledClass),\n      );\n    }\n    init();\n    render();\n    update();\n  };\n\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.pagination.paginationDisabledClass);\n    let { el } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach((subEl) => subEl.classList.add(swiper.params.pagination.paginationDisabledClass));\n    }\n    destroy();\n  };\n\n  Object.assign(swiper.pagination, {\n    enable,\n    disable,\n    render,\n    update,\n    init,\n    destroy,\n  });\n}\n", "import { getDocument } from 'ssr-window';\nimport { createElement, elementOffset, nextTick } from '../../shared/utils.js';\nimport createElementIfNotDefined from '../../shared/create-element-if-not-defined.js';\n\nexport default function Scrollbar({ swiper, extendParams, on, emit }) {\n  const document = getDocument();\n\n  let isTouched = false;\n  let timeout = null;\n  let dragTimeout = null;\n  let dragStartPos;\n  let dragSize;\n  let trackSize;\n  let divider;\n\n  extendParams({\n    scrollbar: {\n      el: null,\n      dragSize: 'auto',\n      hide: false,\n      draggable: false,\n      snapOnRelease: true,\n      lockClass: 'swiper-scrollbar-lock',\n      dragClass: 'swiper-scrollbar-drag',\n      scrollbarDisabledClass: 'swiper-scrollbar-disabled',\n      horizontalClass: `swiper-scrollbar-horizontal`,\n      verticalClass: `swiper-scrollbar-vertical`,\n    },\n  });\n\n  swiper.scrollbar = {\n    el: null,\n    dragEl: null,\n  };\n\n  function setTranslate() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    const { scrollbar, rtlTranslate: rtl } = swiper;\n    const { dragEl, el } = scrollbar;\n    const params = swiper.params.scrollbar;\n    const progress = swiper.params.loop ? swiper.progressLoop : swiper.progress;\n\n    let newSize = dragSize;\n    let newPos = (trackSize - dragSize) * progress;\n    if (rtl) {\n      newPos = -newPos;\n      if (newPos > 0) {\n        newSize = dragSize - newPos;\n        newPos = 0;\n      } else if (-newPos + dragSize > trackSize) {\n        newSize = trackSize + newPos;\n      }\n    } else if (newPos < 0) {\n      newSize = dragSize + newPos;\n      newPos = 0;\n    } else if (newPos + dragSize > trackSize) {\n      newSize = trackSize - newPos;\n    }\n    if (swiper.isHorizontal()) {\n      dragEl.style.transform = `translate3d(${newPos}px, 0, 0)`;\n      dragEl.style.width = `${newSize}px`;\n    } else {\n      dragEl.style.transform = `translate3d(0px, ${newPos}px, 0)`;\n      dragEl.style.height = `${newSize}px`;\n    }\n    if (params.hide) {\n      clearTimeout(timeout);\n      el.style.opacity = 1;\n      timeout = setTimeout(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n  }\n  function setTransition(duration) {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    swiper.scrollbar.dragEl.style.transitionDuration = `${duration}ms`;\n  }\n  function updateSize() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n\n    const { scrollbar } = swiper;\n    const { dragEl, el } = scrollbar;\n\n    dragEl.style.width = '';\n    dragEl.style.height = '';\n    trackSize = swiper.isHorizontal() ? el.offsetWidth : el.offsetHeight;\n\n    divider =\n      swiper.size /\n      (swiper.virtualSize +\n        swiper.params.slidesOffsetBefore -\n        (swiper.params.centeredSlides ? swiper.snapGrid[0] : 0));\n    if (swiper.params.scrollbar.dragSize === 'auto') {\n      dragSize = trackSize * divider;\n    } else {\n      dragSize = parseInt(swiper.params.scrollbar.dragSize, 10);\n    }\n\n    if (swiper.isHorizontal()) {\n      dragEl.style.width = `${dragSize}px`;\n    } else {\n      dragEl.style.height = `${dragSize}px`;\n    }\n\n    if (divider >= 1) {\n      el.style.display = 'none';\n    } else {\n      el.style.display = '';\n    }\n    if (swiper.params.scrollbar.hide) {\n      el.style.opacity = 0;\n    }\n\n    if (swiper.params.watchOverflow && swiper.enabled) {\n      scrollbar.el.classList[swiper.isLocked ? 'add' : 'remove'](swiper.params.scrollbar.lockClass);\n    }\n  }\n  function getPointerPosition(e) {\n    return swiper.isHorizontal() ? e.clientX : e.clientY;\n  }\n  function setDragPosition(e) {\n    const { scrollbar, rtlTranslate: rtl } = swiper;\n    const { el } = scrollbar;\n\n    let positionRatio;\n    positionRatio =\n      (getPointerPosition(e) -\n        elementOffset(el)[swiper.isHorizontal() ? 'left' : 'top'] -\n        (dragStartPos !== null ? dragStartPos : dragSize / 2)) /\n      (trackSize - dragSize);\n    positionRatio = Math.max(Math.min(positionRatio, 1), 0);\n    if (rtl) {\n      positionRatio = 1 - positionRatio;\n    }\n\n    const position =\n      swiper.minTranslate() + (swiper.maxTranslate() - swiper.minTranslate()) * positionRatio;\n\n    swiper.updateProgress(position);\n    swiper.setTranslate(position);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  function onDragStart(e) {\n    const params = swiper.params.scrollbar;\n    const { scrollbar, wrapperEl } = swiper;\n    const { el, dragEl } = scrollbar;\n    isTouched = true;\n    dragStartPos =\n      e.target === dragEl\n        ? getPointerPosition(e) -\n          e.target.getBoundingClientRect()[swiper.isHorizontal() ? 'left' : 'top']\n        : null;\n    e.preventDefault();\n    e.stopPropagation();\n\n    wrapperEl.style.transitionDuration = '100ms';\n    dragEl.style.transitionDuration = '100ms';\n    setDragPosition(e);\n\n    clearTimeout(dragTimeout);\n\n    el.style.transitionDuration = '0ms';\n    if (params.hide) {\n      el.style.opacity = 1;\n    }\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = 'none';\n    }\n    emit('scrollbarDragStart', e);\n  }\n  function onDragMove(e) {\n    const { scrollbar, wrapperEl } = swiper;\n    const { el, dragEl } = scrollbar;\n\n    if (!isTouched) return;\n    if (e.preventDefault) e.preventDefault();\n    else e.returnValue = false;\n    setDragPosition(e);\n    wrapperEl.style.transitionDuration = '0ms';\n    el.style.transitionDuration = '0ms';\n    dragEl.style.transitionDuration = '0ms';\n    emit('scrollbarDragMove', e);\n  }\n  function onDragEnd(e) {\n    const params = swiper.params.scrollbar;\n    const { scrollbar, wrapperEl } = swiper;\n    const { el } = scrollbar;\n\n    if (!isTouched) return;\n    isTouched = false;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = '';\n      wrapperEl.style.transitionDuration = '';\n    }\n    if (params.hide) {\n      clearTimeout(dragTimeout);\n      dragTimeout = nextTick(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n    emit('scrollbarDragEnd', e);\n    if (params.snapOnRelease) {\n      swiper.slideToClosest();\n    }\n  }\n\n  function events(method) {\n    const { scrollbar, params } = swiper;\n    const el = scrollbar.el;\n    if (!el) return;\n    const target = el;\n    const activeListener = params.passiveListeners ? { passive: false, capture: false } : false;\n    const passiveListener = params.passiveListeners ? { passive: true, capture: false } : false;\n    if (!target) return;\n    const eventMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n    target[eventMethod]('pointerdown', onDragStart, activeListener);\n    document[eventMethod]('pointermove', onDragMove, activeListener);\n    document[eventMethod]('pointerup', onDragEnd, passiveListener);\n  }\n\n  function enableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('on');\n  }\n  function disableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('off');\n  }\n  function init() {\n    const { scrollbar, el: swiperEl } = swiper;\n    swiper.params.scrollbar = createElementIfNotDefined(\n      swiper,\n      swiper.originalParams.scrollbar,\n      swiper.params.scrollbar,\n      { el: 'swiper-scrollbar' },\n    );\n    const params = swiper.params.scrollbar;\n    if (!params.el) return;\n\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.shadowRoot.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = document.querySelectorAll(params.el);\n    } else if (!el) {\n      el = params.el;\n    }\n\n    if (\n      swiper.params.uniqueNavElements &&\n      typeof params.el === 'string' &&\n      el.length > 1 &&\n      swiperEl.querySelectorAll(params.el).length === 1\n    ) {\n      el = swiperEl.querySelector(params.el);\n    }\n    if (el.length > 0) el = el[0];\n\n    el.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n\n    let dragEl;\n    if (el) {\n      dragEl = el.querySelector(`.${swiper.params.scrollbar.dragClass}`);\n      if (!dragEl) {\n        dragEl = createElement('div', swiper.params.scrollbar.dragClass);\n        el.append(dragEl);\n      }\n    }\n\n    Object.assign(scrollbar, {\n      el,\n      dragEl,\n    });\n\n    if (params.draggable) {\n      enableDraggable();\n    }\n\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.scrollbar.lockClass);\n    }\n  }\n  function destroy() {\n    const params = swiper.params.scrollbar;\n    const el = swiper.scrollbar.el;\n    if (el) {\n      el.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    }\n\n    disableDraggable();\n  }\n\n  on('init', () => {\n    if (swiper.params.scrollbar.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      updateSize();\n      setTranslate();\n    }\n  });\n  on('update resize observerUpdate lock unlock', () => {\n    updateSize();\n  });\n  on('setTranslate', () => {\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    setTransition(duration);\n  });\n  on('enable disable', () => {\n    const { el } = swiper.scrollbar;\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.scrollbar.lockClass);\n    }\n  });\n  on('destroy', () => {\n    destroy();\n  });\n\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.scrollbar.scrollbarDisabledClass);\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.remove(swiper.params.scrollbar.scrollbarDisabledClass);\n    }\n    init();\n    updateSize();\n    setTranslate();\n  };\n\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.scrollbar.scrollbarDisabledClass);\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.add(swiper.params.scrollbar.scrollbarDisabledClass);\n    }\n    destroy();\n  };\n\n  Object.assign(swiper.scrollbar, {\n    enable,\n    disable,\n    updateSize,\n    setTranslate,\n    init,\n    destroy,\n  });\n}\n", "import { elementChildren } from '../../shared/utils.js';\n\nexport default function Parallax({ swiper, extendParams, on }) {\n  extendParams({\n    parallax: {\n      enabled: false,\n    },\n  });\n\n  const setTransform = (el, progress) => {\n    const { rtl } = swiper;\n\n    const rtlFactor = rtl ? -1 : 1;\n\n    const p = el.getAttribute('data-swiper-parallax') || '0';\n    let x = el.getAttribute('data-swiper-parallax-x');\n    let y = el.getAttribute('data-swiper-parallax-y');\n    const scale = el.getAttribute('data-swiper-parallax-scale');\n    const opacity = el.getAttribute('data-swiper-parallax-opacity');\n    const rotate = el.getAttribute('data-swiper-parallax-rotate');\n\n    if (x || y) {\n      x = x || '0';\n      y = y || '0';\n    } else if (swiper.isHorizontal()) {\n      x = p;\n      y = '0';\n    } else {\n      y = p;\n      x = '0';\n    }\n\n    if (x.indexOf('%') >= 0) {\n      x = `${parseInt(x, 10) * progress * rtlFactor}%`;\n    } else {\n      x = `${x * progress * rtlFactor}px`;\n    }\n    if (y.indexOf('%') >= 0) {\n      y = `${parseInt(y, 10) * progress}%`;\n    } else {\n      y = `${y * progress}px`;\n    }\n\n    if (typeof opacity !== 'undefined' && opacity !== null) {\n      const currentOpacity = opacity - (opacity - 1) * (1 - Math.abs(progress));\n      el.style.opacity = currentOpacity;\n    }\n    let transform = `translate3d(${x}, ${y}, 0px)`;\n    if (typeof scale !== 'undefined' && scale !== null) {\n      const currentScale = scale - (scale - 1) * (1 - Math.abs(progress));\n      transform += ` scale(${currentScale})`;\n    }\n    if (rotate && typeof rotate !== 'undefined' && rotate !== null) {\n      const currentRotate = rotate * progress * -1;\n      transform += ` rotate(${currentRotate}deg)`;\n    }\n    el.style.transform = transform;\n  };\n\n  const setTranslate = () => {\n    const { el, slides, progress, snapGrid } = swiper;\n    elementChildren(\n      el,\n      '[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]',\n    ).forEach((subEl) => {\n      setTransform(subEl, progress);\n    });\n\n    slides.forEach((slideEl, slideIndex) => {\n      let slideProgress = slideEl.progress;\n      if (swiper.params.slidesPerGroup > 1 && swiper.params.slidesPerView !== 'auto') {\n        slideProgress += Math.ceil(slideIndex / 2) - progress * (snapGrid.length - 1);\n      }\n      slideProgress = Math.min(Math.max(slideProgress, -1), 1);\n      slideEl\n        .querySelectorAll(\n          '[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale], [data-swiper-parallax-rotate]',\n        )\n        .forEach((subEl) => {\n          setTransform(subEl, slideProgress);\n        });\n    });\n  };\n\n  const setTransition = (duration = swiper.params.speed) => {\n    const { el } = swiper;\n    el.querySelectorAll(\n      '[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]',\n    ).forEach((parallaxEl) => {\n      let parallaxDuration =\n        parseInt(parallaxEl.getAttribute('data-swiper-parallax-duration'), 10) || duration;\n      if (duration === 0) parallaxDuration = 0;\n      parallaxEl.style.transitionDuration = `${parallaxDuration}ms`;\n    });\n  };\n\n  on('beforeInit', () => {\n    if (!swiper.params.parallax.enabled) return;\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n  });\n  on('init', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTransition', (_swiper, duration) => {\n    if (!swiper.params.parallax.enabled) return;\n    setTransition(duration);\n  });\n}\n", "import { getWindow } from 'ssr-window';\nimport {\n  elementChildren,\n  elementOffset,\n  elementParents,\n  getTranslate,\n} from '../../shared/utils.js';\n\nexport default function Zoom({ swiper, extendParams, on, emit }) {\n  const window = getWindow();\n  extendParams({\n    zoom: {\n      enabled: false,\n      maxRatio: 3,\n      minRatio: 1,\n      toggle: true,\n      containerClass: 'swiper-zoom-container',\n      zoomedSlideClass: 'swiper-slide-zoomed',\n    },\n  });\n\n  swiper.zoom = {\n    enabled: false,\n  };\n\n  let currentScale = 1;\n  let isScaling = false;\n  let fakeGestureTouched;\n  let fakeGestureMoved;\n  const evCache = [];\n  const gesture = {\n    slideEl: undefined,\n    slideWidth: undefined,\n    slideHeight: undefined,\n    imageEl: undefined,\n    imageWrapEl: undefined,\n    maxRatio: 3,\n  };\n  const image = {\n    isTouched: undefined,\n    isMoved: undefined,\n    currentX: undefined,\n    currentY: undefined,\n    minX: undefined,\n    minY: undefined,\n    maxX: undefined,\n    maxY: undefined,\n    width: undefined,\n    height: undefined,\n    startX: undefined,\n    startY: undefined,\n    touchesStart: {},\n    touchesCurrent: {},\n  };\n  const velocity = {\n    x: undefined,\n    y: undefined,\n    prevPositionX: undefined,\n    prevPositionY: undefined,\n    prevTime: undefined,\n  };\n\n  let scale = 1;\n  Object.defineProperty(swiper.zoom, 'scale', {\n    get() {\n      return scale;\n    },\n    set(value) {\n      if (scale !== value) {\n        const imageEl = gesture.imageEl;\n        const slideEl = gesture.slideEl;\n        emit('zoomChange', value, imageEl, slideEl);\n      }\n      scale = value;\n    },\n  });\n\n  function getDistanceBetweenTouches() {\n    if (evCache.length < 2) return 1;\n    const x1 = evCache[0].pageX;\n    const y1 = evCache[0].pageY;\n    const x2 = evCache[1].pageX;\n    const y2 = evCache[1].pageY;\n    const distance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);\n    return distance;\n  }\n\n  function getScaleOrigin() {\n    if (evCache.length < 2) return { x: null, y: null };\n    const box = gesture.imageEl.getBoundingClientRect();\n    return [\n      (evCache[0].pageX + (evCache[1].pageX - evCache[0].pageX) / 2 - box.x) / currentScale,\n\n      (evCache[0].pageY + (evCache[1].pageY - evCache[0].pageY) / 2 - box.y) / currentScale,\n    ];\n  }\n\n  function getSlideSelector() {\n    return swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  }\n\n  function eventWithinSlide(e) {\n    const slideSelector = getSlideSelector();\n    if (e.target.matches(slideSelector)) return true;\n    if (swiper.slides.filter((slideEl) => slideEl.contains(e.target)).length > 0) return true;\n    return false;\n  }\n\n  function eventWithinZoomContainer(e) {\n    const selector = `.${swiper.params.zoom.containerClass}`;\n    if (e.target.matches(selector)) return true;\n    if (\n      [...swiper.el.querySelectorAll(selector)].filter((containerEl) =>\n        containerEl.contains(e.target),\n      ).length > 0\n    )\n      return true;\n    return false;\n  }\n\n  // Events\n  function onGestureStart(e) {\n    if (e.pointerType === 'mouse') {\n      evCache.splice(0, evCache.length);\n    }\n    if (!eventWithinSlide(e)) return;\n    const params = swiper.params.zoom;\n    fakeGestureTouched = false;\n    fakeGestureMoved = false;\n    evCache.push(e);\n    if (evCache.length < 2) {\n      return;\n    }\n    fakeGestureTouched = true;\n    gesture.scaleStart = getDistanceBetweenTouches();\n\n    if (!gesture.slideEl) {\n      gesture.slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n      if (!gesture.slideEl) gesture.slideEl = swiper.slides[swiper.activeIndex];\n\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n\n      if (!gesture.imageWrapEl) {\n        gesture.imageEl = undefined;\n        return;\n      }\n\n      gesture.maxRatio = gesture.imageWrapEl.getAttribute('data-swiper-zoom') || params.maxRatio;\n    }\n    if (gesture.imageEl) {\n      const [originX, originY] = getScaleOrigin();\n      gesture.imageEl.style.transformOrigin = `${originX}px ${originY}px`;\n      gesture.imageEl.style.transitionDuration = '0ms';\n    }\n    isScaling = true;\n  }\n  function onGestureChange(e) {\n    if (!eventWithinSlide(e)) return;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const pointerIndex = evCache.findIndex((cachedEv) => cachedEv.pointerId === e.pointerId);\n    if (pointerIndex >= 0) evCache[pointerIndex] = e;\n\n    if (evCache.length < 2) {\n      return;\n    }\n    fakeGestureMoved = true;\n    gesture.scaleMove = getDistanceBetweenTouches();\n\n    if (!gesture.imageEl) {\n      return;\n    }\n\n    zoom.scale = (gesture.scaleMove / gesture.scaleStart) * currentScale;\n    if (zoom.scale > gesture.maxRatio) {\n      zoom.scale = gesture.maxRatio - 1 + (zoom.scale - gesture.maxRatio + 1) ** 0.5;\n    }\n    if (zoom.scale < params.minRatio) {\n      zoom.scale = params.minRatio + 1 - (params.minRatio - zoom.scale + 1) ** 0.5;\n    }\n\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n  }\n  function onGestureEnd(e) {\n    if (!eventWithinSlide(e)) return;\n    if (e.pointerType === 'mouse' && e.type === 'pointerout') return;\n\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const pointerIndex = evCache.findIndex((cachedEv) => cachedEv.pointerId === e.pointerId);\n    if (pointerIndex >= 0) evCache.splice(pointerIndex, 1);\n\n    if (!fakeGestureTouched || !fakeGestureMoved) {\n      return;\n    }\n\n    fakeGestureTouched = false;\n    fakeGestureMoved = false;\n    if (!gesture.imageEl) return;\n\n    zoom.scale = Math.max(Math.min(zoom.scale, gesture.maxRatio), params.minRatio);\n    gesture.imageEl.style.transitionDuration = `${swiper.params.speed}ms`;\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n    currentScale = zoom.scale;\n    isScaling = false;\n    if (zoom.scale === 1) gesture.slideEl = undefined;\n  }\n  function onTouchStart(e) {\n    const device = swiper.device;\n    if (!gesture.imageEl) return;\n    if (image.isTouched) return;\n    if (device.android && e.cancelable) e.preventDefault();\n    image.isTouched = true;\n    image.touchesStart.x = e.pageX;\n    image.touchesStart.y = e.pageY;\n  }\n  function onTouchMove(e) {\n    if (!eventWithinSlide(e) || !eventWithinZoomContainer(e)) return;\n    const zoom = swiper.zoom;\n    if (!gesture.imageEl) return;\n    swiper.allowClick = false;\n    if (!image.isTouched || !gesture.slideEl) return;\n\n    if (!image.isMoved) {\n      image.width = gesture.imageEl.offsetWidth;\n      image.height = gesture.imageEl.offsetHeight;\n      image.startX = getTranslate(gesture.imageWrapEl, 'x') || 0;\n      image.startY = getTranslate(gesture.imageWrapEl, 'y') || 0;\n      gesture.slideWidth = gesture.slideEl.offsetWidth;\n      gesture.slideHeight = gesture.slideEl.offsetHeight;\n      gesture.imageWrapEl.style.transitionDuration = '0ms';\n    }\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n\n    if (scaledWidth < gesture.slideWidth && scaledHeight < gesture.slideHeight) return;\n\n    image.minX = Math.min(gesture.slideWidth / 2 - scaledWidth / 2, 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(gesture.slideHeight / 2 - scaledHeight / 2, 0);\n    image.maxY = -image.minY;\n\n    image.touchesCurrent.x = evCache.length > 0 ? evCache[0].pageX : e.pageX;\n    image.touchesCurrent.y = evCache.length > 0 ? evCache[0].pageY : e.pageY;\n\n    if (!image.isMoved && !isScaling) {\n      if (\n        swiper.isHorizontal() &&\n        ((Math.floor(image.minX) === Math.floor(image.startX) &&\n          image.touchesCurrent.x < image.touchesStart.x) ||\n          (Math.floor(image.maxX) === Math.floor(image.startX) &&\n            image.touchesCurrent.x > image.touchesStart.x))\n      ) {\n        image.isTouched = false;\n        return;\n      }\n      if (\n        !swiper.isHorizontal() &&\n        ((Math.floor(image.minY) === Math.floor(image.startY) &&\n          image.touchesCurrent.y < image.touchesStart.y) ||\n          (Math.floor(image.maxY) === Math.floor(image.startY) &&\n            image.touchesCurrent.y > image.touchesStart.y))\n      ) {\n        image.isTouched = false;\n        return;\n      }\n    }\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n    e.stopPropagation();\n\n    image.isMoved = true;\n    image.currentX = image.touchesCurrent.x - image.touchesStart.x + image.startX;\n    image.currentY = image.touchesCurrent.y - image.touchesStart.y + image.startY;\n\n    if (image.currentX < image.minX) {\n      image.currentX = image.minX + 1 - (image.minX - image.currentX + 1) ** 0.8;\n    }\n    if (image.currentX > image.maxX) {\n      image.currentX = image.maxX - 1 + (image.currentX - image.maxX + 1) ** 0.8;\n    }\n\n    if (image.currentY < image.minY) {\n      image.currentY = image.minY + 1 - (image.minY - image.currentY + 1) ** 0.8;\n    }\n    if (image.currentY > image.maxY) {\n      image.currentY = image.maxY - 1 + (image.currentY - image.maxY + 1) ** 0.8;\n    }\n\n    // Velocity\n    if (!velocity.prevPositionX) velocity.prevPositionX = image.touchesCurrent.x;\n    if (!velocity.prevPositionY) velocity.prevPositionY = image.touchesCurrent.y;\n    if (!velocity.prevTime) velocity.prevTime = Date.now();\n    velocity.x =\n      (image.touchesCurrent.x - velocity.prevPositionX) / (Date.now() - velocity.prevTime) / 2;\n    velocity.y =\n      (image.touchesCurrent.y - velocity.prevPositionY) / (Date.now() - velocity.prevTime) / 2;\n    if (Math.abs(image.touchesCurrent.x - velocity.prevPositionX) < 2) velocity.x = 0;\n    if (Math.abs(image.touchesCurrent.y - velocity.prevPositionY) < 2) velocity.y = 0;\n    velocity.prevPositionX = image.touchesCurrent.x;\n    velocity.prevPositionY = image.touchesCurrent.y;\n    velocity.prevTime = Date.now();\n\n    gesture.imageWrapEl.style.transform = `translate3d(${image.currentX}px, ${image.currentY}px,0)`;\n  }\n  function onTouchEnd() {\n    const zoom = swiper.zoom;\n    if (!gesture.imageEl) return;\n    if (!image.isTouched || !image.isMoved) {\n      image.isTouched = false;\n      image.isMoved = false;\n      return;\n    }\n    image.isTouched = false;\n    image.isMoved = false;\n    let momentumDurationX = 300;\n    let momentumDurationY = 300;\n    const momentumDistanceX = velocity.x * momentumDurationX;\n    const newPositionX = image.currentX + momentumDistanceX;\n    const momentumDistanceY = velocity.y * momentumDurationY;\n    const newPositionY = image.currentY + momentumDistanceY;\n\n    // Fix duration\n    if (velocity.x !== 0)\n      momentumDurationX = Math.abs((newPositionX - image.currentX) / velocity.x);\n    if (velocity.y !== 0)\n      momentumDurationY = Math.abs((newPositionY - image.currentY) / velocity.y);\n    const momentumDuration = Math.max(momentumDurationX, momentumDurationY);\n\n    image.currentX = newPositionX;\n    image.currentY = newPositionY;\n\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n    image.minX = Math.min(gesture.slideWidth / 2 - scaledWidth / 2, 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(gesture.slideHeight / 2 - scaledHeight / 2, 0);\n    image.maxY = -image.minY;\n    image.currentX = Math.max(Math.min(image.currentX, image.maxX), image.minX);\n    image.currentY = Math.max(Math.min(image.currentY, image.maxY), image.minY);\n\n    gesture.imageWrapEl.style.transitionDuration = `${momentumDuration}ms`;\n    gesture.imageWrapEl.style.transform = `translate3d(${image.currentX}px, ${image.currentY}px,0)`;\n  }\n  function onTransitionEnd() {\n    const zoom = swiper.zoom;\n    if (gesture.slideEl && swiper.previousIndex !== swiper.activeIndex) {\n      if (gesture.imageEl) {\n        gesture.imageEl.style.transform = 'translate3d(0,0,0) scale(1)';\n      }\n      if (gesture.imageWrapEl) {\n        gesture.imageWrapEl.style.transform = 'translate3d(0,0,0)';\n      }\n\n      zoom.scale = 1;\n      currentScale = 1;\n      gesture.slideEl = undefined;\n      gesture.imageEl = undefined;\n      gesture.imageWrapEl = undefined;\n    }\n  }\n\n  function zoomIn(e) {\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n\n    if (!gesture.slideEl) {\n      if (e && e.target) {\n        gesture.slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n      }\n      if (!gesture.slideEl) {\n        if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n          gesture.slideEl = elementChildren(\n            swiper.slidesEl,\n            `.${swiper.params.slideActiveClass}`,\n          )[0];\n        } else {\n          gesture.slideEl = swiper.slides[swiper.activeIndex];\n        }\n      }\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n    }\n    if (!gesture.imageEl || !gesture.imageWrapEl) return;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.touchAction = 'none';\n    }\n\n    gesture.slideEl.classList.add(`${params.zoomedSlideClass}`);\n\n    let touchX;\n    let touchY;\n    let offsetX;\n    let offsetY;\n    let diffX;\n    let diffY;\n    let translateX;\n    let translateY;\n    let imageWidth;\n    let imageHeight;\n    let scaledWidth;\n    let scaledHeight;\n    let translateMinX;\n    let translateMinY;\n    let translateMaxX;\n    let translateMaxY;\n    let slideWidth;\n    let slideHeight;\n\n    if (typeof image.touchesStart.x === 'undefined' && e) {\n      touchX = e.pageX;\n      touchY = e.pageY;\n    } else {\n      touchX = image.touchesStart.x;\n      touchY = image.touchesStart.y;\n    }\n\n    const forceZoomRatio = typeof e === 'number' ? e : null;\n    if (currentScale === 1 && forceZoomRatio) {\n      touchX = undefined;\n      touchY = undefined;\n    }\n\n    zoom.scale =\n      forceZoomRatio || gesture.imageWrapEl.getAttribute('data-swiper-zoom') || params.maxRatio;\n    currentScale =\n      forceZoomRatio || gesture.imageWrapEl.getAttribute('data-swiper-zoom') || params.maxRatio;\n    if (e && !(currentScale === 1 && forceZoomRatio)) {\n      slideWidth = gesture.slideEl.offsetWidth;\n      slideHeight = gesture.slideEl.offsetHeight;\n      offsetX = elementOffset(gesture.slideEl).left + window.scrollX;\n      offsetY = elementOffset(gesture.slideEl).top + window.scrollY;\n      diffX = offsetX + slideWidth / 2 - touchX;\n      diffY = offsetY + slideHeight / 2 - touchY;\n\n      imageWidth = gesture.imageEl.offsetWidth;\n      imageHeight = gesture.imageEl.offsetHeight;\n      scaledWidth = imageWidth * zoom.scale;\n      scaledHeight = imageHeight * zoom.scale;\n\n      translateMinX = Math.min(slideWidth / 2 - scaledWidth / 2, 0);\n      translateMinY = Math.min(slideHeight / 2 - scaledHeight / 2, 0);\n      translateMaxX = -translateMinX;\n      translateMaxY = -translateMinY;\n\n      translateX = diffX * zoom.scale;\n      translateY = diffY * zoom.scale;\n\n      if (translateX < translateMinX) {\n        translateX = translateMinX;\n      }\n      if (translateX > translateMaxX) {\n        translateX = translateMaxX;\n      }\n\n      if (translateY < translateMinY) {\n        translateY = translateMinY;\n      }\n      if (translateY > translateMaxY) {\n        translateY = translateMaxY;\n      }\n    } else {\n      translateX = 0;\n      translateY = 0;\n    }\n    gesture.imageWrapEl.style.transitionDuration = '300ms';\n    gesture.imageWrapEl.style.transform = `translate3d(${translateX}px, ${translateY}px,0)`;\n    gesture.imageEl.style.transitionDuration = '300ms';\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n  }\n  function zoomOut() {\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n\n    if (!gesture.slideEl) {\n      if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n        gesture.slideEl = elementChildren(swiper.slidesEl, `.${swiper.params.slideActiveClass}`)[0];\n      } else {\n        gesture.slideEl = swiper.slides[swiper.activeIndex];\n      }\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n    }\n    if (!gesture.imageEl || !gesture.imageWrapEl) return;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style.overflow = '';\n      swiper.wrapperEl.style.touchAction = '';\n    }\n    zoom.scale = 1;\n    currentScale = 1;\n    gesture.imageWrapEl.style.transitionDuration = '300ms';\n    gesture.imageWrapEl.style.transform = 'translate3d(0,0,0)';\n    gesture.imageEl.style.transitionDuration = '300ms';\n    gesture.imageEl.style.transform = 'translate3d(0,0,0) scale(1)';\n\n    gesture.slideEl.classList.remove(`${params.zoomedSlideClass}`);\n    gesture.slideEl = undefined;\n  }\n\n  // Toggle Zoom\n  function zoomToggle(e) {\n    const zoom = swiper.zoom;\n\n    if (zoom.scale && zoom.scale !== 1) {\n      // Zoom Out\n      zoomOut();\n    } else {\n      // Zoom In\n      zoomIn(e);\n    }\n  }\n\n  function getListeners() {\n    const passiveListener = swiper.params.passiveListeners\n      ? { passive: true, capture: false }\n      : false;\n    const activeListenerWithCapture = swiper.params.passiveListeners\n      ? { passive: false, capture: true }\n      : true;\n    return { passiveListener, activeListenerWithCapture };\n  }\n\n  // Attach/Detach Events\n  function enable() {\n    const zoom = swiper.zoom;\n    if (zoom.enabled) return;\n    zoom.enabled = true;\n    const { passiveListener, activeListenerWithCapture } = getListeners();\n\n    // Scale image\n\n    swiper.wrapperEl.addEventListener('pointerdown', onGestureStart, passiveListener);\n    swiper.wrapperEl.addEventListener('pointermove', onGestureChange, activeListenerWithCapture);\n    ['pointerup', 'pointercancel', 'pointerout'].forEach((eventName) => {\n      swiper.wrapperEl.addEventListener(eventName, onGestureEnd, passiveListener);\n    });\n\n    // Move image\n    swiper.wrapperEl.addEventListener('pointermove', onTouchMove, activeListenerWithCapture);\n  }\n  function disable() {\n    const zoom = swiper.zoom;\n    if (!zoom.enabled) return;\n    zoom.enabled = false;\n\n    const { passiveListener, activeListenerWithCapture } = getListeners();\n\n    // Scale image\n    swiper.wrapperEl.removeEventListener('pointerdown', onGestureStart, passiveListener);\n    swiper.wrapperEl.removeEventListener('pointermove', onGestureChange, activeListenerWithCapture);\n    ['pointerup', 'pointercancel', 'pointerout'].forEach((eventName) => {\n      swiper.wrapperEl.removeEventListener(eventName, onGestureEnd, passiveListener);\n    });\n\n    // Move image\n    swiper.wrapperEl.removeEventListener('pointermove', onTouchMove, activeListenerWithCapture);\n  }\n\n  on('init', () => {\n    if (swiper.params.zoom.enabled) {\n      enable();\n    }\n  });\n  on('destroy', () => {\n    disable();\n  });\n  on('touchStart', (_s, e) => {\n    if (!swiper.zoom.enabled) return;\n    onTouchStart(e);\n  });\n  on('touchEnd', (_s, e) => {\n    if (!swiper.zoom.enabled) return;\n    onTouchEnd(e);\n  });\n  on('doubleTap', (_s, e) => {\n    if (\n      !swiper.animating &&\n      swiper.params.zoom.enabled &&\n      swiper.zoom.enabled &&\n      swiper.params.zoom.toggle\n    ) {\n      zoomToggle(e);\n    }\n  });\n  on('transitionEnd', () => {\n    if (swiper.zoom.enabled && swiper.params.zoom.enabled) {\n      onTransitionEnd();\n    }\n  });\n  on('slideChange', () => {\n    if (swiper.zoom.enabled && swiper.params.zoom.enabled && swiper.params.cssMode) {\n      onTransitionEnd();\n    }\n  });\n\n  Object.assign(swiper.zoom, {\n    enable,\n    disable,\n    in: zoomIn,\n    out: zoomOut,\n    toggle: zoomToggle,\n  });\n}\n", "/* eslint no-bitwise: [\"error\", { \"allow\": [\">>\"] }] */\nimport { elementTransitionEnd, nextTick } from '../../shared/utils.js';\n\nexport default function Controller({ swiper, extendParams, on }) {\n  extendParams({\n    controller: {\n      control: undefined,\n      inverse: false,\n      by: 'slide', // or 'container'\n    },\n  });\n\n  swiper.controller = {\n    control: undefined,\n  };\n\n  function LinearSpline(x, y) {\n    const binarySearch = (function search() {\n      let maxIndex;\n      let minIndex;\n      let guess;\n      return (array, val) => {\n        minIndex = -1;\n        maxIndex = array.length;\n        while (maxIndex - minIndex > 1) {\n          guess = (maxIndex + minIndex) >> 1;\n          if (array[guess] <= val) {\n            minIndex = guess;\n          } else {\n            maxIndex = guess;\n          }\n        }\n        return maxIndex;\n      };\n    })();\n    this.x = x;\n    this.y = y;\n    this.lastIndex = x.length - 1;\n    // Given an x value (x2), return the expected y2 value:\n    // (x1,y1) is the known point before given value,\n    // (x3,y3) is the known point after given value.\n    let i1;\n    let i3;\n\n    this.interpolate = function interpolate(x2) {\n      if (!x2) return 0;\n\n      // Get the indexes of x1 and x3 (the array indexes before and after given x2):\n      i3 = binarySearch(this.x, x2);\n      i1 = i3 - 1;\n\n      // We have our indexes i1 & i3, so we can calculate already:\n      // y2 := ((x2−x1) × (y3−y1)) ÷ (x3−x1) + y1\n      return (\n        ((x2 - this.x[i1]) * (this.y[i3] - this.y[i1])) / (this.x[i3] - this.x[i1]) + this.y[i1]\n      );\n    };\n    return this;\n  }\n  // xxx: for now i will just save one spline function to to\n  function getInterpolateFunction(c) {\n    if (!swiper.controller.spline) {\n      swiper.controller.spline = swiper.params.loop\n        ? new LinearSpline(swiper.slidesGrid, c.slidesGrid)\n        : new LinearSpline(swiper.snapGrid, c.snapGrid);\n    }\n  }\n  function setTranslate(_t, byController) {\n    const controlled = swiper.controller.control;\n    let multiplier;\n    let controlledTranslate;\n    const Swiper = swiper.constructor;\n    function setControlledTranslate(c) {\n      // this will create an Interpolate function based on the snapGrids\n      // x is the Grid of the scrolled scroller and y will be the controlled scroller\n      // it makes sense to create this only once and recall it for the interpolation\n      // the function does a lot of value caching for performance\n      const translate = swiper.rtlTranslate ? -swiper.translate : swiper.translate;\n      if (swiper.params.controller.by === 'slide') {\n        getInterpolateFunction(c);\n        // i am not sure why the values have to be multiplicated this way, tried to invert the snapGrid\n        // but it did not work out\n        controlledTranslate = -swiper.controller.spline.interpolate(-translate);\n      }\n\n      if (!controlledTranslate || swiper.params.controller.by === 'container') {\n        multiplier =\n          (c.maxTranslate() - c.minTranslate()) / (swiper.maxTranslate() - swiper.minTranslate());\n        controlledTranslate = (translate - swiper.minTranslate()) * multiplier + c.minTranslate();\n      }\n\n      if (swiper.params.controller.inverse) {\n        controlledTranslate = c.maxTranslate() - controlledTranslate;\n      }\n      c.updateProgress(controlledTranslate);\n      c.setTranslate(controlledTranslate, swiper);\n      c.updateActiveIndex();\n      c.updateSlidesClasses();\n    }\n    if (Array.isArray(controlled)) {\n      for (let i = 0; i < controlled.length; i += 1) {\n        if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n          setControlledTranslate(controlled[i]);\n        }\n      }\n    } else if (controlled instanceof Swiper && byController !== controlled) {\n      setControlledTranslate(controlled);\n    }\n  }\n  function setTransition(duration, byController) {\n    const Swiper = swiper.constructor;\n    const controlled = swiper.controller.control;\n    let i;\n    function setControlledTransition(c) {\n      c.setTransition(duration, swiper);\n      if (duration !== 0) {\n        c.transitionStart();\n        if (c.params.autoHeight) {\n          nextTick(() => {\n            c.updateAutoHeight();\n          });\n        }\n        elementTransitionEnd(c.wrapperEl, () => {\n          if (!controlled) return;\n          c.transitionEnd();\n        });\n      }\n    }\n    if (Array.isArray(controlled)) {\n      for (i = 0; i < controlled.length; i += 1) {\n        if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n          setControlledTransition(controlled[i]);\n        }\n      }\n    } else if (controlled instanceof Swiper && byController !== controlled) {\n      setControlledTransition(controlled);\n    }\n  }\n\n  function removeSpline() {\n    if (!swiper.controller.control) return;\n    if (swiper.controller.spline) {\n      swiper.controller.spline = undefined;\n      delete swiper.controller.spline;\n    }\n  }\n  on('beforeInit', () => {\n    if (\n      typeof window !== 'undefined' && // eslint-disable-line\n      (typeof swiper.params.controller.control === 'string' ||\n        swiper.params.controller.control instanceof HTMLElement)\n    ) {\n      const controlElement = document.querySelector(swiper.params.controller.control);\n      if (controlElement && controlElement.swiper) {\n        swiper.controller.control = controlElement.swiper;\n      } else if (controlElement) {\n        const onControllerSwiper = (e) => {\n          swiper.controller.control = e.detail[0];\n          swiper.update();\n          controlElement.removeEventListener('init', onControllerSwiper);\n        };\n        controlElement.addEventListener('init', onControllerSwiper);\n      }\n      return;\n    }\n    swiper.controller.control = swiper.params.controller.control;\n  });\n  on('update', () => {\n    removeSpline();\n  });\n  on('resize', () => {\n    removeSpline();\n  });\n  on('observerUpdate', () => {\n    removeSpline();\n  });\n  on('setTranslate', (_s, translate, byController) => {\n    if (!swiper.controller.control) return;\n    swiper.controller.setTranslate(translate, byController);\n  });\n  on('setTransition', (_s, duration, byController) => {\n    if (!swiper.controller.control) return;\n    swiper.controller.setTransition(duration, byController);\n  });\n\n  Object.assign(swiper.controller, {\n    setTranslate,\n    setTransition,\n  });\n}\n", "import classesToSelector from '../../shared/classes-to-selector.js';\nimport { createElement, elementIndex } from '../../shared/utils.js';\n\nexport default function A11y({ swiper, extendParams, on }) {\n  extendParams({\n    a11y: {\n      enabled: true,\n      notificationClass: 'swiper-notification',\n      prevSlideMessage: 'Previous slide',\n      nextSlideMessage: 'Next slide',\n      firstSlideMessage: 'This is the first slide',\n      lastSlideMessage: 'This is the last slide',\n      paginationBulletMessage: 'Go to slide {{index}}',\n      slideLabelMessage: '{{index}} / {{slidesLength}}',\n      containerMessage: null,\n      containerRoleDescriptionMessage: null,\n      itemRoleDescriptionMessage: null,\n      slideRole: 'group',\n      id: null,\n    },\n  });\n\n  swiper.a11y = {\n    clicked: false,\n  };\n\n  let liveRegion = null;\n\n  function notify(message) {\n    const notification = liveRegion;\n    if (notification.length === 0) return;\n    notification.innerHTML = '';\n    notification.innerHTML = message;\n  }\n\n  const makeElementsArray = (el) => {\n    if (!Array.isArray(el)) el = [el].filter((e) => !!e);\n    return el;\n  };\n\n  function getRandomNumber(size = 16) {\n    const randomChar = () => Math.round(16 * Math.random()).toString(16);\n    return 'x'.repeat(size).replace(/x/g, randomChar);\n  }\n  function makeElFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach((subEl) => {\n      subEl.setAttribute('tabIndex', '0');\n    });\n  }\n  function makeElNotFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach((subEl) => {\n      subEl.setAttribute('tabIndex', '-1');\n    });\n  }\n  function addElRole(el, role) {\n    el = makeElementsArray(el);\n    el.forEach((subEl) => {\n      subEl.setAttribute('role', role);\n    });\n  }\n  function addElRoleDescription(el, description) {\n    el = makeElementsArray(el);\n    el.forEach((subEl) => {\n      subEl.setAttribute('aria-roledescription', description);\n    });\n  }\n  function addElControls(el, controls) {\n    el = makeElementsArray(el);\n    el.forEach((subEl) => {\n      subEl.setAttribute('aria-controls', controls);\n    });\n  }\n  function addElLabel(el, label) {\n    el = makeElementsArray(el);\n    el.forEach((subEl) => {\n      subEl.setAttribute('aria-label', label);\n    });\n  }\n  function addElId(el, id) {\n    el = makeElementsArray(el);\n    el.forEach((subEl) => {\n      subEl.setAttribute('id', id);\n    });\n  }\n  function addElLive(el, live) {\n    el = makeElementsArray(el);\n    el.forEach((subEl) => {\n      subEl.setAttribute('aria-live', live);\n    });\n  }\n  function disableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach((subEl) => {\n      subEl.setAttribute('aria-disabled', true);\n    });\n  }\n  function enableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach((subEl) => {\n      subEl.setAttribute('aria-disabled', false);\n    });\n  }\n\n  function onEnterOrSpaceKey(e) {\n    if (e.keyCode !== 13 && e.keyCode !== 32) return;\n    const params = swiper.params.a11y;\n    const targetEl = e.target;\n    if (\n      swiper.pagination &&\n      swiper.pagination.el &&\n      (targetEl === swiper.pagination.el || swiper.pagination.el.contains(e.target))\n    ) {\n      if (!e.target.matches(classesToSelector(swiper.params.pagination.bulletClass))) return;\n    }\n    if (swiper.navigation && swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl) {\n      if (!(swiper.isEnd && !swiper.params.loop)) {\n        swiper.slideNext();\n      }\n      if (swiper.isEnd) {\n        notify(params.lastSlideMessage);\n      } else {\n        notify(params.nextSlideMessage);\n      }\n    }\n    if (swiper.navigation && swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl) {\n      if (!(swiper.isBeginning && !swiper.params.loop)) {\n        swiper.slidePrev();\n      }\n      if (swiper.isBeginning) {\n        notify(params.firstSlideMessage);\n      } else {\n        notify(params.prevSlideMessage);\n      }\n    }\n\n    if (\n      swiper.pagination &&\n      targetEl.matches(classesToSelector(swiper.params.pagination.bulletClass))\n    ) {\n      targetEl.click();\n    }\n  }\n\n  function updateNavigation() {\n    if (swiper.params.loop || swiper.params.rewind || !swiper.navigation) return;\n    const { nextEl, prevEl } = swiper.navigation;\n\n    if (prevEl) {\n      if (swiper.isBeginning) {\n        disableEl(prevEl);\n        makeElNotFocusable(prevEl);\n      } else {\n        enableEl(prevEl);\n        makeElFocusable(prevEl);\n      }\n    }\n    if (nextEl) {\n      if (swiper.isEnd) {\n        disableEl(nextEl);\n        makeElNotFocusable(nextEl);\n      } else {\n        enableEl(nextEl);\n        makeElFocusable(nextEl);\n      }\n    }\n  }\n\n  function hasPagination() {\n    return swiper.pagination && swiper.pagination.bullets && swiper.pagination.bullets.length;\n  }\n\n  function hasClickablePagination() {\n    return hasPagination() && swiper.params.pagination.clickable;\n  }\n\n  function updatePagination() {\n    const params = swiper.params.a11y;\n    if (!hasPagination()) return;\n    swiper.pagination.bullets.forEach((bulletEl) => {\n      if (swiper.params.pagination.clickable) {\n        makeElFocusable(bulletEl);\n        if (!swiper.params.pagination.renderBullet) {\n          addElRole(bulletEl, 'button');\n          addElLabel(\n            bulletEl,\n            params.paginationBulletMessage.replace(/\\{\\{index\\}\\}/, elementIndex(bulletEl) + 1),\n          );\n        }\n      }\n      if (bulletEl.matches(`.${swiper.params.pagination.bulletActiveClass}`)) {\n        bulletEl.setAttribute('aria-current', 'true');\n      } else {\n        bulletEl.removeAttribute('aria-current');\n      }\n    });\n  }\n\n  const initNavEl = (el, wrapperId, message) => {\n    makeElFocusable(el);\n    if (el.tagName !== 'BUTTON') {\n      addElRole(el, 'button');\n      el.addEventListener('keydown', onEnterOrSpaceKey);\n    }\n    addElLabel(el, message);\n    addElControls(el, wrapperId);\n  };\n  const handlePointerDown = () => {\n    swiper.a11y.clicked = true;\n  };\n  const handlePointerUp = () => {\n    requestAnimationFrame(() => {\n      requestAnimationFrame(() => {\n        if (!swiper.destroyed) {\n          swiper.a11y.clicked = false;\n        }\n      });\n    });\n  };\n\n  const handleFocus = (e) => {\n    if (swiper.a11y.clicked) return;\n    const slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n    if (!slideEl || !swiper.slides.includes(slideEl)) return;\n    const isActive = swiper.slides.indexOf(slideEl) === swiper.activeIndex;\n    const isVisible =\n      swiper.params.watchSlidesProgress &&\n      swiper.visibleSlides &&\n      swiper.visibleSlides.includes(slideEl);\n    if (isActive || isVisible) return;\n    if (e.sourceCapabilities && e.sourceCapabilities.firesTouchEvents) return;\n    if (swiper.isHorizontal()) {\n      swiper.el.scrollLeft = 0;\n    } else {\n      swiper.el.scrollTop = 0;\n    }\n    swiper.slideTo(swiper.slides.indexOf(slideEl), 0);\n  };\n\n  const initSlides = () => {\n    const params = swiper.params.a11y;\n    if (params.itemRoleDescriptionMessage) {\n      addElRoleDescription(swiper.slides, params.itemRoleDescriptionMessage);\n    }\n    if (params.slideRole) {\n      addElRole(swiper.slides, params.slideRole);\n    }\n\n    const slidesLength = swiper.slides.length;\n    if (params.slideLabelMessage) {\n      swiper.slides.forEach((slideEl, index) => {\n        const slideIndex = swiper.params.loop\n          ? parseInt(slideEl.getAttribute('data-swiper-slide-index'), 10)\n          : index;\n        const ariaLabelMessage = params.slideLabelMessage\n          .replace(/\\{\\{index\\}\\}/, slideIndex + 1)\n          .replace(/\\{\\{slidesLength\\}\\}/, slidesLength);\n        addElLabel(slideEl, ariaLabelMessage);\n      });\n    }\n  };\n\n  const init = () => {\n    const params = swiper.params.a11y;\n\n    swiper.el.append(liveRegion);\n\n    // Container\n    const containerEl = swiper.el;\n    if (params.containerRoleDescriptionMessage) {\n      addElRoleDescription(containerEl, params.containerRoleDescriptionMessage);\n    }\n    if (params.containerMessage) {\n      addElLabel(containerEl, params.containerMessage);\n    }\n\n    // Wrapper\n    const wrapperEl = swiper.wrapperEl;\n    const wrapperId =\n      params.id || wrapperEl.getAttribute('id') || `swiper-wrapper-${getRandomNumber(16)}`;\n    const live = swiper.params.autoplay && swiper.params.autoplay.enabled ? 'off' : 'polite';\n    addElId(wrapperEl, wrapperId);\n    addElLive(wrapperEl, live);\n\n    // Slide\n    initSlides();\n\n    // Navigation\n    let { nextEl, prevEl } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n\n    if (nextEl) {\n      nextEl.forEach((el) => initNavEl(el, wrapperId, params.nextSlideMessage));\n    }\n    if (prevEl) {\n      prevEl.forEach((el) => initNavEl(el, wrapperId, params.prevSlideMessage));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = Array.isArray(swiper.pagination.el)\n        ? swiper.pagination.el\n        : [swiper.pagination.el];\n      paginationEl.forEach((el) => {\n        el.addEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n\n    // Tab focus\n    swiper.el.addEventListener('focus', handleFocus, true);\n    swiper.el.addEventListener('pointerdown', handlePointerDown, true);\n    swiper.el.addEventListener('pointerup', handlePointerUp, true);\n  };\n  function destroy() {\n    if (liveRegion && liveRegion.length > 0) liveRegion.remove();\n    let { nextEl, prevEl } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach((el) => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n    if (prevEl) {\n      prevEl.forEach((el) => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = Array.isArray(swiper.pagination.el)\n        ? swiper.pagination.el\n        : [swiper.pagination.el];\n      paginationEl.forEach((el) => {\n        el.removeEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n\n    // Tab focus\n    swiper.el.removeEventListener('focus', handleFocus, true);\n    swiper.el.removeEventListener('pointerdown', handlePointerDown, true);\n    swiper.el.removeEventListener('pointerup', handlePointerUp, true);\n  }\n\n  on('beforeInit', () => {\n    liveRegion = createElement('span', swiper.params.a11y.notificationClass);\n    liveRegion.setAttribute('aria-live', 'assertive');\n    liveRegion.setAttribute('aria-atomic', 'true');\n    if (swiper.isElement) {\n      liveRegion.setAttribute('slot', 'container-end');\n    }\n  });\n\n  on('afterInit', () => {\n    if (!swiper.params.a11y.enabled) return;\n    init();\n  });\n  on('slidesLengthChange snapGridLengthChange slidesGridLengthChange', () => {\n    if (!swiper.params.a11y.enabled) return;\n    initSlides();\n  });\n  on('fromEdge toEdge afterInit lock unlock', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updateNavigation();\n  });\n  on('paginationUpdate', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updatePagination();\n  });\n  on('destroy', () => {\n    if (!swiper.params.a11y.enabled) return;\n    destroy();\n  });\n}\n", "import { getWindow } from 'ssr-window';\n\nexport default function History({ swiper, extendParams, on }) {\n  extendParams({\n    history: {\n      enabled: false,\n      root: '',\n      replaceState: false,\n      key: 'slides',\n      keepQuery: false,\n    },\n  });\n\n  let initialized = false;\n  let paths = {};\n\n  const slugify = (text) => {\n    return text\n      .toString()\n      .replace(/\\s+/g, '-')\n      .replace(/[^\\w-]+/g, '')\n      .replace(/--+/g, '-')\n      .replace(/^-+/, '')\n      .replace(/-+$/, '');\n  };\n\n  const getPathValues = (urlOverride) => {\n    const window = getWindow();\n    let location;\n    if (urlOverride) {\n      location = new URL(urlOverride);\n    } else {\n      location = window.location;\n    }\n    const pathArray = location.pathname\n      .slice(1)\n      .split('/')\n      .filter((part) => part !== '');\n    const total = pathArray.length;\n    const key = pathArray[total - 2];\n    const value = pathArray[total - 1];\n    return { key, value };\n  };\n  const setHistory = (key, index) => {\n    const window = getWindow();\n    if (!initialized || !swiper.params.history.enabled) return;\n    let location;\n    if (swiper.params.url) {\n      location = new URL(swiper.params.url);\n    } else {\n      location = window.location;\n    }\n    const slide = swiper.slides[index];\n    let value = slugify(slide.getAttribute('data-history'));\n    if (swiper.params.history.root.length > 0) {\n      let root = swiper.params.history.root;\n      if (root[root.length - 1] === '/') root = root.slice(0, root.length - 1);\n      value = `${root}/${key ? `${key}/` : ''}${value}`;\n    } else if (!location.pathname.includes(key)) {\n      value = `${key ? `${key}/` : ''}${value}`;\n    }\n    if (swiper.params.history.keepQuery) {\n      value += location.search;\n    }\n    const currentState = window.history.state;\n    if (currentState && currentState.value === value) {\n      return;\n    }\n    if (swiper.params.history.replaceState) {\n      window.history.replaceState({ value }, null, value);\n    } else {\n      window.history.pushState({ value }, null, value);\n    }\n  };\n\n  const scrollToSlide = (speed, value, runCallbacks) => {\n    if (value) {\n      for (let i = 0, length = swiper.slides.length; i < length; i += 1) {\n        const slide = swiper.slides[i];\n        const slideHistory = slugify(slide.getAttribute('data-history'));\n        if (slideHistory === value) {\n          const index = swiper.getSlideIndex(slide);\n          swiper.slideTo(index, speed, runCallbacks);\n        }\n      }\n    } else {\n      swiper.slideTo(0, speed, runCallbacks);\n    }\n  };\n\n  const setHistoryPopState = () => {\n    paths = getPathValues(swiper.params.url);\n    scrollToSlide(swiper.params.speed, paths.value, false);\n  };\n\n  const init = () => {\n    const window = getWindow();\n    if (!swiper.params.history) return;\n    if (!window.history || !window.history.pushState) {\n      swiper.params.history.enabled = false;\n      swiper.params.hashNavigation.enabled = true;\n      return;\n    }\n    initialized = true;\n    paths = getPathValues(swiper.params.url);\n    if (!paths.key && !paths.value) {\n      if (!swiper.params.history.replaceState) {\n        window.addEventListener('popstate', setHistoryPopState);\n      }\n      return;\n    }\n    scrollToSlide(0, paths.value, swiper.params.runCallbacksOnInit);\n    if (!swiper.params.history.replaceState) {\n      window.addEventListener('popstate', setHistoryPopState);\n    }\n  };\n  const destroy = () => {\n    const window = getWindow();\n    if (!swiper.params.history.replaceState) {\n      window.removeEventListener('popstate', setHistoryPopState);\n    }\n  };\n\n  on('init', () => {\n    if (swiper.params.history.enabled) {\n      init();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.params.history.enabled) {\n      destroy();\n    }\n  });\n  on('transitionEnd _freeModeNoMomentumRelease', () => {\n    if (initialized) {\n      setHistory(swiper.params.history.key, swiper.activeIndex);\n    }\n  });\n  on('slideChange', () => {\n    if (initialized && swiper.params.cssMode) {\n      setHistory(swiper.params.history.key, swiper.activeIndex);\n    }\n  });\n}\n", "import { getWindow, getDocument } from 'ssr-window';\nimport { elementChildren } from '../../shared/utils.js';\n\nexport default function HashNavigation({ swiper, extendParams, emit, on }) {\n  let initialized = false;\n  const document = getDocument();\n  const window = getWindow();\n  extendParams({\n    hashNavigation: {\n      enabled: false,\n      replaceState: false,\n      watchState: false,\n    },\n  });\n  const onHashChange = () => {\n    emit('hashChange');\n    const newHash = document.location.hash.replace('#', '');\n    const activeSlideHash = swiper.slides[swiper.activeIndex].getAttribute('data-hash');\n    if (newHash !== activeSlideHash) {\n      const newIndex = swiper.getSlideIndex(\n        elementChildren(\n          swiper.slidesEl,\n          `.${swiper.params.slideClass}[data-hash=\"${newHash}\"], swiper-slide[data-hash=\"${newHash}\"]`,\n        )[0],\n      );\n      if (typeof newIndex === 'undefined') return;\n      swiper.slideTo(newIndex);\n    }\n  };\n  const setHash = () => {\n    if (!initialized || !swiper.params.hashNavigation.enabled) return;\n    if (\n      swiper.params.hashNavigation.replaceState &&\n      window.history &&\n      window.history.replaceState\n    ) {\n      window.history.replaceState(\n        null,\n        null,\n        `#${swiper.slides[swiper.activeIndex].getAttribute('data-hash')}` || '',\n      );\n      emit('hashSet');\n    } else {\n      const slide = swiper.slides[swiper.activeIndex];\n      const hash = slide.getAttribute('data-hash') || slide.getAttribute('data-history');\n      document.location.hash = hash || '';\n      emit('hashSet');\n    }\n  };\n  const init = () => {\n    if (\n      !swiper.params.hashNavigation.enabled ||\n      (swiper.params.history && swiper.params.history.enabled)\n    )\n      return;\n    initialized = true;\n    const hash = document.location.hash.replace('#', '');\n    if (hash) {\n      const speed = 0;\n      for (let i = 0, length = swiper.slides.length; i < length; i += 1) {\n        const slide = swiper.slides[i];\n        const slideHash = slide.getAttribute('data-hash') || slide.getAttribute('data-history');\n        if (slideHash === hash) {\n          const index = swiper.getSlideIndex(slide);\n          swiper.slideTo(index, speed, swiper.params.runCallbacksOnInit, true);\n        }\n      }\n    }\n    if (swiper.params.hashNavigation.watchState) {\n      window.addEventListener('hashchange', onHashChange);\n    }\n  };\n  const destroy = () => {\n    if (swiper.params.hashNavigation.watchState) {\n      window.removeEventListener('hashchange', onHashChange);\n    }\n  };\n\n  on('init', () => {\n    if (swiper.params.hashNavigation.enabled) {\n      init();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.params.hashNavigation.enabled) {\n      destroy();\n    }\n  });\n  on('transitionEnd _freeModeNoMomentumRelease', () => {\n    if (initialized) {\n      setHash();\n    }\n  });\n  on('slideChange', () => {\n    if (initialized && swiper.params.cssMode) {\n      setHash();\n    }\n  });\n}\n", "/* eslint no-underscore-dangle: \"off\" */\n/* eslint no-use-before-define: \"off\" */\nimport { getDocument } from 'ssr-window';\n\nexport default function Autoplay({ swiper, extendParams, on, emit, params }) {\n  swiper.autoplay = {\n    running: false,\n    paused: false,\n    timeLeft: 0,\n  };\n\n  extendParams({\n    autoplay: {\n      enabled: false,\n      delay: 3000,\n      waitForTransition: true,\n      disableOnInteraction: true,\n      stopOnLastSlide: false,\n      reverseDirection: false,\n      pauseOnMouseEnter: false,\n    },\n  });\n  let timeout;\n  let raf;\n  let autoplayDelayTotal = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayDelayCurrent = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayTimeLeft;\n  let autoplayStartTime = new Date().getTime;\n  let wasPaused;\n  let isTouched;\n  let pausedByTouch;\n  let touchStartTimeout;\n  let slideChanged;\n  let pausedByInteraction;\n\n  function onTransitionEnd(e) {\n    if (!swiper || swiper.destroyed || !swiper.wrapperEl) return;\n    if (e.target !== swiper.wrapperEl) return;\n    swiper.wrapperEl.removeEventListener('transitionend', onTransitionEnd);\n    resume();\n  }\n\n  const calcTimeLeft = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.autoplay.paused) {\n      wasPaused = true;\n    } else if (wasPaused) {\n      autoplayDelayCurrent = autoplayTimeLeft;\n      wasPaused = false;\n    }\n    const timeLeft = swiper.autoplay.paused\n      ? autoplayTimeLeft\n      : autoplayStartTime + autoplayDelayCurrent - new Date().getTime();\n    swiper.autoplay.timeLeft = timeLeft;\n    emit('autoplayTimeLeft', timeLeft, timeLeft / autoplayDelayTotal);\n    raf = requestAnimationFrame(() => {\n      calcTimeLeft();\n    });\n  };\n\n  const getSlideDelay = () => {\n    let activeSlideEl;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      activeSlideEl = swiper.slides.filter((slideEl) =>\n        slideEl.classList.contains('swiper-slide-active'),\n      )[0];\n    } else {\n      activeSlideEl = swiper.slides[swiper.activeIndex];\n    }\n    if (!activeSlideEl) return undefined;\n    const currentSlideDelay = parseInt(activeSlideEl.getAttribute('data-swiper-autoplay'), 10);\n    return currentSlideDelay;\n  };\n\n  const run = (delayForce) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    cancelAnimationFrame(raf);\n    calcTimeLeft();\n\n    let delay = typeof delayForce === 'undefined' ? swiper.params.autoplay.delay : delayForce;\n    autoplayDelayTotal = swiper.params.autoplay.delay;\n    autoplayDelayCurrent = swiper.params.autoplay.delay;\n    const currentSlideDelay = getSlideDelay();\n    if (\n      !Number.isNaN(currentSlideDelay) &&\n      currentSlideDelay > 0 &&\n      typeof delayForce === 'undefined'\n    ) {\n      delay = currentSlideDelay;\n      autoplayDelayTotal = currentSlideDelay;\n      autoplayDelayCurrent = currentSlideDelay;\n    }\n    autoplayTimeLeft = delay;\n    const speed = swiper.params.speed;\n    const proceed = () => {\n      if (!swiper || swiper.destroyed) return;\n      if (swiper.params.autoplay.reverseDirection) {\n        if (!swiper.isBeginning || swiper.params.loop || swiper.params.rewind) {\n          swiper.slidePrev(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(swiper.slides.length - 1, speed, true, true);\n          emit('autoplay');\n        }\n      } else {\n        if (!swiper.isEnd || swiper.params.loop || swiper.params.rewind) {\n          swiper.slideNext(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, speed, true, true);\n          emit('autoplay');\n        }\n      }\n      if (swiper.params.cssMode) {\n        autoplayStartTime = new Date().getTime();\n        requestAnimationFrame(() => {\n          run();\n        });\n      }\n    };\n    if (delay > 0) {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        proceed();\n      }, delay);\n    } else {\n      requestAnimationFrame(() => {\n        proceed();\n      });\n    }\n\n    // eslint-disable-next-line\n    return delay;\n  };\n\n  const start = () => {\n    swiper.autoplay.running = true;\n    run();\n    emit('autoplayStart');\n  };\n\n  const stop = () => {\n    swiper.autoplay.running = false;\n    clearTimeout(timeout);\n    cancelAnimationFrame(raf);\n    emit('autoplayStop');\n  };\n  const pause = (internal, reset) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    clearTimeout(timeout);\n    if (!internal) {\n      pausedByInteraction = true;\n    }\n\n    const proceed = () => {\n      emit('autoplayPause');\n      if (swiper.params.autoplay.waitForTransition) {\n        swiper.wrapperEl.addEventListener('transitionend', onTransitionEnd);\n      } else {\n        resume();\n      }\n    };\n\n    swiper.autoplay.paused = true;\n    if (reset) {\n      if (slideChanged) {\n        autoplayTimeLeft = swiper.params.autoplay.delay;\n      }\n      slideChanged = false;\n      proceed();\n      return;\n    }\n    const delay = autoplayTimeLeft || swiper.params.autoplay.delay;\n    autoplayTimeLeft = delay - (new Date().getTime() - autoplayStartTime);\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop) return;\n    if (autoplayTimeLeft < 0) autoplayTimeLeft = 0;\n    proceed();\n  };\n\n  const resume = () => {\n    if (\n      (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop) ||\n      swiper.destroyed ||\n      !swiper.autoplay.running\n    )\n      return;\n    autoplayStartTime = new Date().getTime();\n    if (pausedByInteraction) {\n      pausedByInteraction = false;\n      run(autoplayTimeLeft);\n    } else {\n      run();\n    }\n    swiper.autoplay.paused = false;\n    emit('autoplayResume');\n  };\n\n  const onVisibilityChange = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    const document = getDocument();\n    if (document.visibilityState === 'hidden') {\n      pausedByInteraction = true;\n      pause(true);\n    }\n    if (document.visibilityState === 'visible') {\n      resume();\n    }\n  };\n\n  const onPointerEnter = (e) => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByInteraction = true;\n    pause(true);\n  };\n\n  const onPointerLeave = (e) => {\n    if (e.pointerType !== 'mouse') return;\n    if (swiper.autoplay.paused) {\n      resume();\n    }\n  };\n\n  const attachMouseEvents = () => {\n    if (swiper.params.autoplay.pauseOnMouseEnter) {\n      swiper.el.addEventListener('pointerenter', onPointerEnter);\n      swiper.el.addEventListener('pointerleave', onPointerLeave);\n    }\n  };\n\n  const detachMouseEvents = () => {\n    swiper.el.removeEventListener('pointerenter', onPointerEnter);\n    swiper.el.removeEventListener('pointerleave', onPointerLeave);\n  };\n\n  const attachDocumentEvents = () => {\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n  };\n\n  const detachDocumentEvents = () => {\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n  };\n\n  on('init', () => {\n    if (swiper.params.autoplay.enabled) {\n      attachMouseEvents();\n      attachDocumentEvents();\n      autoplayStartTime = new Date().getTime();\n      start();\n    }\n  });\n\n  on('destroy', () => {\n    detachMouseEvents();\n    detachDocumentEvents();\n    if (swiper.autoplay.running) {\n      stop();\n    }\n  });\n\n  on('beforeTransitionStart', (_s, speed, internal) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (internal || !swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n\n  on('sliderFirstMove', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n\n    if (swiper.params.autoplay.disableOnInteraction) {\n      stop();\n      return;\n    }\n    isTouched = true;\n    pausedByTouch = false;\n    pausedByInteraction = false;\n    touchStartTimeout = setTimeout(() => {\n      pausedByInteraction = true;\n      pausedByTouch = true;\n      pause(true);\n    }, 200);\n  });\n\n  on('touchEnd', () => {\n    if (swiper.destroyed || !swiper.autoplay.running || !isTouched) return;\n    clearTimeout(touchStartTimeout);\n    clearTimeout(timeout);\n\n    if (swiper.params.autoplay.disableOnInteraction) {\n      pausedByTouch = false;\n      isTouched = false;\n      return;\n    }\n\n    if (pausedByTouch && swiper.params.cssMode) resume();\n    pausedByTouch = false;\n    isTouched = false;\n  });\n\n  on('slideChange', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    slideChanged = true;\n  });\n\n  Object.assign(swiper.autoplay, {\n    start,\n    stop,\n    pause,\n    resume,\n  });\n}\n", "import { getDocument } from 'ssr-window';\nimport { elementChildren, isObject } from '../../shared/utils.js';\n\nexport default function Thumb({ swiper, extendParams, on }) {\n  extendParams({\n    thumbs: {\n      swiper: null,\n      multipleActiveThumbs: true,\n      autoScrollOffset: 0,\n      slideThumbActiveClass: 'swiper-slide-thumb-active',\n      thumbsContainerClass: 'swiper-thumbs',\n    },\n  });\n\n  let initialized = false;\n  let swiperCreated = false;\n\n  swiper.thumbs = {\n    swiper: null,\n  };\n\n  function onThumbClick() {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n\n    const clickedIndex = thumbsSwiper.clickedIndex;\n    const clickedSlide = thumbsSwiper.clickedSlide;\n    if (clickedSlide && clickedSlide.classList.contains(swiper.params.thumbs.slideThumbActiveClass))\n      return;\n    if (typeof clickedIndex === 'undefined' || clickedIndex === null) return;\n    let slideToIndex;\n    if (thumbsSwiper.params.loop) {\n      slideToIndex = parseInt(\n        thumbsSwiper.clickedSlide.getAttribute('data-swiper-slide-index'),\n        10,\n      );\n    } else {\n      slideToIndex = clickedIndex;\n    }\n    if (swiper.params.loop) {\n      swiper.slideToLoop(slideToIndex);\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  }\n\n  function init() {\n    const { thumbs: thumbsParams } = swiper.params;\n    if (initialized) return false;\n    initialized = true;\n    const SwiperClass = swiper.constructor;\n    if (thumbsParams.swiper instanceof SwiperClass) {\n      swiper.thumbs.swiper = thumbsParams.swiper;\n      Object.assign(swiper.thumbs.swiper.originalParams, {\n        watchSlidesProgress: true,\n        slideToClickedSlide: false,\n      });\n      Object.assign(swiper.thumbs.swiper.params, {\n        watchSlidesProgress: true,\n        slideToClickedSlide: false,\n      });\n      swiper.thumbs.swiper.update();\n    } else if (isObject(thumbsParams.swiper)) {\n      const thumbsSwiperParams = Object.assign({}, thumbsParams.swiper);\n      Object.assign(thumbsSwiperParams, {\n        watchSlidesProgress: true,\n        slideToClickedSlide: false,\n      });\n      swiper.thumbs.swiper = new SwiperClass(thumbsSwiperParams);\n      swiperCreated = true;\n    }\n    swiper.thumbs.swiper.el.classList.add(swiper.params.thumbs.thumbsContainerClass);\n    swiper.thumbs.swiper.on('tap', onThumbClick);\n    return true;\n  }\n\n  function update(initial) {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n\n    const slidesPerView =\n      thumbsSwiper.params.slidesPerView === 'auto'\n        ? thumbsSwiper.slidesPerViewDynamic()\n        : thumbsSwiper.params.slidesPerView;\n\n    // Activate thumbs\n    let thumbsToActivate = 1;\n    const thumbActiveClass = swiper.params.thumbs.slideThumbActiveClass;\n\n    if (swiper.params.slidesPerView > 1 && !swiper.params.centeredSlides) {\n      thumbsToActivate = swiper.params.slidesPerView;\n    }\n\n    if (!swiper.params.thumbs.multipleActiveThumbs) {\n      thumbsToActivate = 1;\n    }\n\n    thumbsToActivate = Math.floor(thumbsToActivate);\n\n    thumbsSwiper.slides.forEach((slideEl) => slideEl.classList.remove(thumbActiveClass));\n    if (\n      thumbsSwiper.params.loop ||\n      (thumbsSwiper.params.virtual && thumbsSwiper.params.virtual.enabled)\n    ) {\n      for (let i = 0; i < thumbsToActivate; i += 1) {\n        elementChildren(\n          thumbsSwiper.slidesEl,\n          `[data-swiper-slide-index=\"${swiper.realIndex + i}\"]`,\n        ).forEach((slideEl) => {\n          slideEl.classList.add(thumbActiveClass);\n        });\n      }\n    } else {\n      for (let i = 0; i < thumbsToActivate; i += 1) {\n        if (thumbsSwiper.slides[swiper.realIndex + i]) {\n          thumbsSwiper.slides[swiper.realIndex + i].classList.add(thumbActiveClass);\n        }\n      }\n    }\n\n    const autoScrollOffset = swiper.params.thumbs.autoScrollOffset;\n    const useOffset = autoScrollOffset && !thumbsSwiper.params.loop;\n    if (swiper.realIndex !== thumbsSwiper.realIndex || useOffset) {\n      const currentThumbsIndex = thumbsSwiper.activeIndex;\n      let newThumbsIndex;\n      let direction;\n      if (thumbsSwiper.params.loop) {\n        const newThumbsSlide = thumbsSwiper.slides.filter(\n          (slideEl) => slideEl.getAttribute('data-swiper-slide-index') === `${swiper.realIndex}`,\n        )[0];\n        newThumbsIndex = thumbsSwiper.slides.indexOf(newThumbsSlide);\n\n        direction = swiper.activeIndex > swiper.previousIndex ? 'next' : 'prev';\n      } else {\n        newThumbsIndex = swiper.realIndex;\n        direction = newThumbsIndex > swiper.previousIndex ? 'next' : 'prev';\n      }\n      if (useOffset) {\n        newThumbsIndex += direction === 'next' ? autoScrollOffset : -1 * autoScrollOffset;\n      }\n\n      if (\n        thumbsSwiper.visibleSlidesIndexes &&\n        thumbsSwiper.visibleSlidesIndexes.indexOf(newThumbsIndex) < 0\n      ) {\n        if (thumbsSwiper.params.centeredSlides) {\n          if (newThumbsIndex > currentThumbsIndex) {\n            newThumbsIndex = newThumbsIndex - Math.floor(slidesPerView / 2) + 1;\n          } else {\n            newThumbsIndex = newThumbsIndex + Math.floor(slidesPerView / 2) - 1;\n          }\n        } else if (\n          newThumbsIndex > currentThumbsIndex &&\n          thumbsSwiper.params.slidesPerGroup === 1\n        ) {\n          // newThumbsIndex = newThumbsIndex - slidesPerView + 1;\n        }\n        thumbsSwiper.slideTo(newThumbsIndex, initial ? 0 : undefined);\n      }\n    }\n  }\n\n  on('beforeInit', () => {\n    const { thumbs } = swiper.params;\n    if (!thumbs || !thumbs.swiper) return;\n    if (typeof thumbs.swiper === 'string' || thumbs.swiper instanceof HTMLElement) {\n      const document = getDocument();\n      const getThumbsElementAndInit = () => {\n        const thumbsElement =\n          typeof thumbs.swiper === 'string' ? document.querySelector(thumbs.swiper) : thumbs.swiper;\n        if (thumbsElement && thumbsElement.swiper) {\n          thumbs.swiper = thumbsElement.swiper;\n          init();\n          update(true);\n        } else if (thumbsElement) {\n          const onThumbsSwiper = (e) => {\n            thumbs.swiper = e.detail[0];\n            thumbsElement.removeEventListener('init', onThumbsSwiper);\n            init();\n            update(true);\n            thumbs.swiper.update();\n            swiper.update();\n          };\n          thumbsElement.addEventListener('init', onThumbsSwiper);\n        }\n        return thumbsElement;\n      };\n\n      const watchForThumbsToAppear = () => {\n        if (swiper.destroyed) return;\n        const thumbsElement = getThumbsElementAndInit();\n        if (!thumbsElement) {\n          requestAnimationFrame(watchForThumbsToAppear);\n        }\n      };\n      requestAnimationFrame(watchForThumbsToAppear);\n    } else {\n      init();\n      update(true);\n    }\n  });\n  on('slideChange update resize observerUpdate', () => {\n    update();\n  });\n  on('setTransition', (_s, duration) => {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n    thumbsSwiper.setTransition(duration);\n  });\n  on('beforeDestroy', () => {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n    if (swiperCreated) {\n      thumbsSwiper.destroy();\n    }\n  });\n\n  Object.assign(swiper.thumbs, {\n    init,\n    update,\n  });\n}\n", "import { elementTransitionEnd, now } from '../../shared/utils.js';\n\nexport default function freeMode({ swiper, extendParams, emit, once }) {\n  extendParams({\n    freeMode: {\n      enabled: false,\n      momentum: true,\n      momentumRatio: 1,\n      momentumBounce: true,\n      momentumBounceRatio: 1,\n      momentumVelocityRatio: 1,\n      sticky: false,\n      minimumVelocity: 0.02,\n    },\n  });\n\n  function onTouchStart() {\n    const translate = swiper.getTranslate();\n    swiper.setTranslate(translate);\n    swiper.setTransition(0);\n    swiper.touchEventsData.velocities.length = 0;\n    swiper.freeMode.onTouchEnd({ currentPos: swiper.rtl ? swiper.translate : -swiper.translate });\n  }\n\n  function onTouchMove() {\n    const { touchEventsData: data, touches } = swiper;\n    // Velocity\n    if (data.velocities.length === 0) {\n      data.velocities.push({\n        position: touches[swiper.isHorizontal() ? 'startX' : 'startY'],\n        time: data.touchStartTime,\n      });\n    }\n    data.velocities.push({\n      position: touches[swiper.isHorizontal() ? 'currentX' : 'currentY'],\n      time: now(),\n    });\n  }\n\n  function onTouchEnd({ currentPos }) {\n    const { params, wrapperEl, rtlTranslate: rtl, snapGrid, touchEventsData: data } = swiper;\n    // Time diff\n    const touchEndTime = now();\n    const timeDiff = touchEndTime - data.touchStartTime;\n\n    if (currentPos < -swiper.minTranslate()) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (currentPos > -swiper.maxTranslate()) {\n      if (swiper.slides.length < snapGrid.length) {\n        swiper.slideTo(snapGrid.length - 1);\n      } else {\n        swiper.slideTo(swiper.slides.length - 1);\n      }\n      return;\n    }\n\n    if (params.freeMode.momentum) {\n      if (data.velocities.length > 1) {\n        const lastMoveEvent = data.velocities.pop();\n        const velocityEvent = data.velocities.pop();\n\n        const distance = lastMoveEvent.position - velocityEvent.position;\n        const time = lastMoveEvent.time - velocityEvent.time;\n        swiper.velocity = distance / time;\n        swiper.velocity /= 2;\n        if (Math.abs(swiper.velocity) < params.freeMode.minimumVelocity) {\n          swiper.velocity = 0;\n        }\n        // this implies that the user stopped moving a finger then released.\n        // There would be no events with distance zero, so the last event is stale.\n        if (time > 150 || now() - lastMoveEvent.time > 300) {\n          swiper.velocity = 0;\n        }\n      } else {\n        swiper.velocity = 0;\n      }\n      swiper.velocity *= params.freeMode.momentumVelocityRatio;\n\n      data.velocities.length = 0;\n      let momentumDuration = 1000 * params.freeMode.momentumRatio;\n      const momentumDistance = swiper.velocity * momentumDuration;\n\n      let newPosition = swiper.translate + momentumDistance;\n      if (rtl) newPosition = -newPosition;\n\n      let doBounce = false;\n      let afterBouncePosition;\n      const bounceAmount = Math.abs(swiper.velocity) * 20 * params.freeMode.momentumBounceRatio;\n      let needsLoopFix;\n      if (newPosition < swiper.maxTranslate()) {\n        if (params.freeMode.momentumBounce) {\n          if (newPosition + swiper.maxTranslate() < -bounceAmount) {\n            newPosition = swiper.maxTranslate() - bounceAmount;\n          }\n          afterBouncePosition = swiper.maxTranslate();\n          doBounce = true;\n          data.allowMomentumBounce = true;\n        } else {\n          newPosition = swiper.maxTranslate();\n        }\n        if (params.loop && params.centeredSlides) needsLoopFix = true;\n      } else if (newPosition > swiper.minTranslate()) {\n        if (params.freeMode.momentumBounce) {\n          if (newPosition - swiper.minTranslate() > bounceAmount) {\n            newPosition = swiper.minTranslate() + bounceAmount;\n          }\n          afterBouncePosition = swiper.minTranslate();\n          doBounce = true;\n          data.allowMomentumBounce = true;\n        } else {\n          newPosition = swiper.minTranslate();\n        }\n        if (params.loop && params.centeredSlides) needsLoopFix = true;\n      } else if (params.freeMode.sticky) {\n        let nextSlide;\n        for (let j = 0; j < snapGrid.length; j += 1) {\n          if (snapGrid[j] > -newPosition) {\n            nextSlide = j;\n            break;\n          }\n        }\n\n        if (\n          Math.abs(snapGrid[nextSlide] - newPosition) <\n            Math.abs(snapGrid[nextSlide - 1] - newPosition) ||\n          swiper.swipeDirection === 'next'\n        ) {\n          newPosition = snapGrid[nextSlide];\n        } else {\n          newPosition = snapGrid[nextSlide - 1];\n        }\n        newPosition = -newPosition;\n      }\n      if (needsLoopFix) {\n        once('transitionEnd', () => {\n          swiper.loopFix();\n        });\n      }\n      // Fix duration\n      if (swiper.velocity !== 0) {\n        if (rtl) {\n          momentumDuration = Math.abs((-newPosition - swiper.translate) / swiper.velocity);\n        } else {\n          momentumDuration = Math.abs((newPosition - swiper.translate) / swiper.velocity);\n        }\n        if (params.freeMode.sticky) {\n          // If freeMode.sticky is active and the user ends a swipe with a slow-velocity\n          // event, then durations can be 20+ seconds to slide one (or zero!) slides.\n          // It's easy to see this when simulating touch with mouse events. To fix this,\n          // limit single-slide swipes to the default slide duration. This also has the\n          // nice side effect of matching slide speed if the user stopped moving before\n          // lifting finger or mouse vs. moving slowly before lifting the finger/mouse.\n          // For faster swipes, also apply limits (albeit higher ones).\n          const moveDistance = Math.abs((rtl ? -newPosition : newPosition) - swiper.translate);\n          const currentSlideSize = swiper.slidesSizesGrid[swiper.activeIndex];\n          if (moveDistance < currentSlideSize) {\n            momentumDuration = params.speed;\n          } else if (moveDistance < 2 * currentSlideSize) {\n            momentumDuration = params.speed * 1.5;\n          } else {\n            momentumDuration = params.speed * 2.5;\n          }\n        }\n      } else if (params.freeMode.sticky) {\n        swiper.slideToClosest();\n        return;\n      }\n\n      if (params.freeMode.momentumBounce && doBounce) {\n        swiper.updateProgress(afterBouncePosition);\n        swiper.setTransition(momentumDuration);\n        swiper.setTranslate(newPosition);\n        swiper.transitionStart(true, swiper.swipeDirection);\n        swiper.animating = true;\n        elementTransitionEnd(wrapperEl, () => {\n          if (!swiper || swiper.destroyed || !data.allowMomentumBounce) return;\n          emit('momentumBounce');\n          swiper.setTransition(params.speed);\n          setTimeout(() => {\n            swiper.setTranslate(afterBouncePosition);\n            elementTransitionEnd(wrapperEl, () => {\n              if (!swiper || swiper.destroyed) return;\n              swiper.transitionEnd();\n            });\n          }, 0);\n        });\n      } else if (swiper.velocity) {\n        emit('_freeModeNoMomentumRelease');\n        swiper.updateProgress(newPosition);\n        swiper.setTransition(momentumDuration);\n        swiper.setTranslate(newPosition);\n        swiper.transitionStart(true, swiper.swipeDirection);\n        if (!swiper.animating) {\n          swiper.animating = true;\n          elementTransitionEnd(wrapperEl, () => {\n            if (!swiper || swiper.destroyed) return;\n            swiper.transitionEnd();\n          });\n        }\n      } else {\n        swiper.updateProgress(newPosition);\n      }\n\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    } else if (params.freeMode.sticky) {\n      swiper.slideToClosest();\n      return;\n    } else if (params.freeMode) {\n      emit('_freeModeNoMomentumRelease');\n    }\n\n    if (!params.freeMode.momentum || timeDiff >= params.longSwipesMs) {\n      swiper.updateProgress();\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n  }\n\n  Object.assign(swiper, {\n    freeMode: {\n      onTouchStart,\n      onTouchMove,\n      onTouchEnd,\n    },\n  });\n}\n", "export default function Grid({ swiper, extendParams }) {\n  extendParams({\n    grid: {\n      rows: 1,\n      fill: 'column',\n    },\n  });\n\n  let slidesNumberEvenToRows;\n  let slidesPerRow;\n  let numFullColumns;\n\n  const initSlides = (slidesLength) => {\n    const { slidesPerView } = swiper.params;\n    const { rows, fill } = swiper.params.grid;\n    slidesPerRow = slidesNumberEvenToRows / rows;\n    numFullColumns = Math.floor(slidesLength / rows);\n    if (Math.floor(slidesLength / rows) === slidesLength / rows) {\n      slidesNumberEvenToRows = slidesLength;\n    } else {\n      slidesNumberEvenToRows = Math.ceil(slidesLength / rows) * rows;\n    }\n    if (slidesPerView !== 'auto' && fill === 'row') {\n      slidesNumberEvenToRows = Math.max(slidesNumberEvenToRows, slidesPerView * rows);\n    }\n  };\n\n  const updateSlide = (i, slide, slidesLength, getDirectionLabel) => {\n    const { slidesPerGroup, spaceBetween } = swiper.params;\n    const { rows, fill } = swiper.params.grid;\n    // Set slides order\n    let newSlideOrderIndex;\n    let column;\n    let row;\n    if (fill === 'row' && slidesPerGroup > 1) {\n      const groupIndex = Math.floor(i / (slidesPerGroup * rows));\n      const slideIndexInGroup = i - rows * slidesPerGroup * groupIndex;\n      const columnsInGroup =\n        groupIndex === 0\n          ? slidesPerGroup\n          : Math.min(\n              Math.ceil((slidesLength - groupIndex * rows * slidesPerGroup) / rows),\n              slidesPerGroup,\n            );\n      row = Math.floor(slideIndexInGroup / columnsInGroup);\n      column = slideIndexInGroup - row * columnsInGroup + groupIndex * slidesPerGroup;\n\n      newSlideOrderIndex = column + (row * slidesNumberEvenToRows) / rows;\n      slide.style.order = newSlideOrderIndex;\n    } else if (fill === 'column') {\n      column = Math.floor(i / rows);\n      row = i - column * rows;\n      if (column > numFullColumns || (column === numFullColumns && row === rows - 1)) {\n        row += 1;\n        if (row >= rows) {\n          row = 0;\n          column += 1;\n        }\n      }\n    } else {\n      row = Math.floor(i / slidesPerRow);\n      column = i - row * slidesPerRow;\n    }\n    slide.style[getDirectionLabel('margin-top')] =\n      row !== 0 ? spaceBetween && `${spaceBetween}px` : '';\n  };\n\n  const updateWrapperSize = (slideSize, snapGrid, getDirectionLabel) => {\n    const { spaceBetween, centeredSlides, roundLengths } = swiper.params;\n    const { rows } = swiper.params.grid;\n    swiper.virtualSize = (slideSize + spaceBetween) * slidesNumberEvenToRows;\n    swiper.virtualSize = Math.ceil(swiper.virtualSize / rows) - spaceBetween;\n    swiper.wrapperEl.style[getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n    if (centeredSlides) {\n      const newSlidesGrid = [];\n      for (let i = 0; i < snapGrid.length; i += 1) {\n        let slidesGridItem = snapGrid[i];\n        if (roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n        if (snapGrid[i] < swiper.virtualSize + snapGrid[0]) newSlidesGrid.push(slidesGridItem);\n      }\n      snapGrid.splice(0, snapGrid.length);\n      snapGrid.push(...newSlidesGrid);\n    }\n  };\n\n  swiper.grid = {\n    initSlides,\n    updateSlide,\n    updateWrapperSize,\n  };\n}\n", "import appendSlide from './methods/appendSlide.js';\nimport prependSlide from './methods/prependSlide.js';\nimport addSlide from './methods/addSlide.js';\nimport removeSlide from './methods/removeSlide.js';\nimport removeAllSlides from './methods/removeAllSlides.js';\n\nexport default function Manipulation({ swiper }) {\n  Object.assign(swiper, {\n    appendSlide: appendSlide.bind(swiper),\n    prependSlide: prependSlide.bind(swiper),\n    addSlide: addSlide.bind(swiper),\n    removeSlide: removeSlide.bind(swiper),\n    removeAllSlides: removeAllSlides.bind(swiper),\n  });\n}\n", "import effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport effectVirtualTransitionEnd from '../../shared/effect-virtual-transition-end.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\n\nexport default function EffectFade({ swiper, extendParams, on }) {\n  extendParams({\n    fadeEffect: {\n      crossFade: false,\n    },\n  });\n\n  const setTranslate = () => {\n    const { slides } = swiper;\n    const params = swiper.params.fadeEffect;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = swiper.slides[i];\n      const offset = slideEl.swiperSlideOffset;\n      let tx = -offset;\n      if (!swiper.params.virtualTranslate) tx -= swiper.translate;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n      }\n      const slideOpacity = swiper.params.fadeEffect.crossFade\n        ? Math.max(1 - Math.abs(slideEl.progress), 0)\n        : 1 + Math.min(Math.max(slideEl.progress, -1), 0);\n\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.opacity = slideOpacity;\n      targetEl.style.transform = `translate3d(${tx}px, ${ty}px, 0px)`;\n    }\n  };\n  const setTransition = (duration) => {\n    const transformElements = swiper.slides.map((slideEl) => getSlideTransformEl(slideEl));\n    transformElements.forEach((el) => {\n      el.style.transitionDuration = `${duration}ms`;\n    });\n\n    effectVirtualTransitionEnd({ swiper, duration, transformElements, allSlides: true });\n  };\n\n  effectInit({\n    effect: 'fade',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      spaceBetween: 0,\n      virtualTranslate: !swiper.params.cssMode,\n    }),\n  });\n}\n", "import effectInit from '../../shared/effect-init.js';\nimport { createElement } from '../../shared/utils.js';\n\nexport default function EffectCube({ swiper, extendParams, on }) {\n  extendParams({\n    cubeEffect: {\n      slideShadows: true,\n      shadow: true,\n      shadowOffset: 20,\n      shadowScale: 0.94,\n    },\n  });\n\n  const createSlideShadows = (slideEl, progress, isHorizontal) => {\n    let shadowBefore = isHorizontal\n      ? slideEl.querySelector('.swiper-slide-shadow-left')\n      : slideEl.querySelector('.swiper-slide-shadow-top');\n    let shadowAfter = isHorizontal\n      ? slideEl.querySelector('.swiper-slide-shadow-right')\n      : slideEl.querySelector('.swiper-slide-shadow-bottom');\n    if (!shadowBefore) {\n      shadowBefore = createElement('div', `swiper-slide-shadow-${isHorizontal ? 'left' : 'top'}`);\n      slideEl.append(shadowBefore);\n    }\n    if (!shadowAfter) {\n      shadowAfter = createElement(\n        'div',\n        `swiper-slide-shadow-${isHorizontal ? 'right' : 'bottom'}`,\n      );\n      slideEl.append(shadowAfter);\n    }\n    if (shadowBefore) shadowBefore.style.opacity = Math.max(-progress, 0);\n    if (shadowAfter) shadowAfter.style.opacity = Math.max(progress, 0);\n  };\n\n  const recreateShadows = () => {\n    // create new ones\n    const isHorizontal = swiper.isHorizontal();\n    swiper.slides.forEach((slideEl) => {\n      const progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      createSlideShadows(slideEl, progress, isHorizontal);\n    });\n  };\n\n  const setTranslate = () => {\n    const {\n      el,\n      wrapperEl,\n      slides,\n      width: swiperWidth,\n      height: swiperHeight,\n      rtlTranslate: rtl,\n      size: swiperSize,\n      browser,\n    } = swiper;\n    const params = swiper.params.cubeEffect;\n    const isHorizontal = swiper.isHorizontal();\n    const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n    let wrapperRotate = 0;\n    let cubeShadowEl;\n\n    if (params.shadow) {\n      if (isHorizontal) {\n        cubeShadowEl = swiper.slidesEl.querySelector('.swiper-cube-shadow');\n        if (!cubeShadowEl) {\n          cubeShadowEl = createElement('div', 'swiper-cube-shadow');\n          swiper.slidesEl.append(cubeShadowEl);\n        }\n        cubeShadowEl.style.height = `${swiperWidth}px`;\n      } else {\n        cubeShadowEl = el.querySelector('.swiper-cube-shadow');\n        if (!cubeShadowEl) {\n          cubeShadowEl = createElement('div', 'swiper-cube-shadow');\n          el.append(cubeShadowEl);\n        }\n      }\n    }\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      let slideIndex = i;\n      if (isVirtual) {\n        slideIndex = parseInt(slideEl.getAttribute('data-swiper-slide-index'), 10);\n      }\n      let slideAngle = slideIndex * 90;\n      let round = Math.floor(slideAngle / 360);\n      if (rtl) {\n        slideAngle = -slideAngle;\n        round = Math.floor(-slideAngle / 360);\n      }\n      const progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      let tx = 0;\n      let ty = 0;\n      let tz = 0;\n      if (slideIndex % 4 === 0) {\n        tx = -round * 4 * swiperSize;\n        tz = 0;\n      } else if ((slideIndex - 1) % 4 === 0) {\n        tx = 0;\n        tz = -round * 4 * swiperSize;\n      } else if ((slideIndex - 2) % 4 === 0) {\n        tx = swiperSize + round * 4 * swiperSize;\n        tz = swiperSize;\n      } else if ((slideIndex - 3) % 4 === 0) {\n        tx = -swiperSize;\n        tz = 3 * swiperSize + swiperSize * 4 * round;\n      }\n      if (rtl) {\n        tx = -tx;\n      }\n\n      if (!isHorizontal) {\n        ty = tx;\n        tx = 0;\n      }\n\n      const transform = `rotateX(${isHorizontal ? 0 : -slideAngle}deg) rotateY(${\n        isHorizontal ? slideAngle : 0\n      }deg) translate3d(${tx}px, ${ty}px, ${tz}px)`;\n      if (progress <= 1 && progress > -1) {\n        wrapperRotate = slideIndex * 90 + progress * 90;\n        if (rtl) wrapperRotate = -slideIndex * 90 - progress * 90;\n      }\n      slideEl.style.transform = transform;\n      if (params.slideShadows) {\n        createSlideShadows(slideEl, progress, isHorizontal);\n      }\n    }\n    wrapperEl.style.transformOrigin = `50% 50% -${swiperSize / 2}px`;\n    wrapperEl.style['-webkit-transform-origin'] = `50% 50% -${swiperSize / 2}px`;\n\n    if (params.shadow) {\n      if (isHorizontal) {\n        cubeShadowEl.style.transform = `translate3d(0px, ${\n          swiperWidth / 2 + params.shadowOffset\n        }px, ${-swiperWidth / 2}px) rotateX(90deg) rotateZ(0deg) scale(${params.shadowScale})`;\n      } else {\n        const shadowAngle = Math.abs(wrapperRotate) - Math.floor(Math.abs(wrapperRotate) / 90) * 90;\n        const multiplier =\n          1.5 -\n          (Math.sin((shadowAngle * 2 * Math.PI) / 360) / 2 +\n            Math.cos((shadowAngle * 2 * Math.PI) / 360) / 2);\n        const scale1 = params.shadowScale;\n        const scale2 = params.shadowScale / multiplier;\n        const offset = params.shadowOffset;\n        cubeShadowEl.style.transform = `scale3d(${scale1}, 1, ${scale2}) translate3d(0px, ${\n          swiperHeight / 2 + offset\n        }px, ${-swiperHeight / 2 / scale2}px) rotateX(-90deg)`;\n      }\n    }\n    const zFactor =\n      (browser.isSafari || browser.isWebView) && browser.needPerspectiveFix ? -swiperSize / 2 : 0;\n    wrapperEl.style.transform = `translate3d(0px,0,${zFactor}px) rotateX(${\n      swiper.isHorizontal() ? 0 : wrapperRotate\n    }deg) rotateY(${swiper.isHorizontal() ? -wrapperRotate : 0}deg)`;\n\n    wrapperEl.style.setProperty('--swiper-cube-translate-z', `${zFactor}px`);\n  };\n  const setTransition = (duration) => {\n    const { el, slides } = swiper;\n    slides.forEach((slideEl) => {\n      slideEl.style.transitionDuration = `${duration}ms`;\n      slideEl\n        .querySelectorAll(\n          '.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left',\n        )\n        .forEach((subEl) => {\n          subEl.style.transitionDuration = `${duration}ms`;\n        });\n    });\n\n    if (swiper.params.cubeEffect.shadow && !swiper.isHorizontal()) {\n      const shadowEl = el.querySelector('.swiper-cube-shadow');\n      if (shadowEl) shadowEl.style.transitionDuration = `${duration}ms`;\n    }\n  };\n\n  effectInit({\n    effect: 'cube',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    recreateShadows,\n    getEffectParams: () => swiper.params.cubeEffect,\n    perspective: () => true,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      resistanceRatio: 0,\n      spaceBetween: 0,\n      centeredSlides: false,\n      virtualTranslate: true,\n    }),\n  });\n}\n", "import createShadow from '../../shared/create-shadow.js';\nimport effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport effectVirtualTransitionEnd from '../../shared/effect-virtual-transition-end.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\n\nexport default function EffectFlip({ swiper, extendParams, on }) {\n  extendParams({\n    flipEffect: {\n      slideShadows: true,\n      limitRotation: true,\n    },\n  });\n\n  const createSlideShadows = (slideEl, progress, params) => {\n    let shadowBefore = swiper.isHorizontal()\n      ? slideEl.querySelector('.swiper-slide-shadow-left')\n      : slideEl.querySelector('.swiper-slide-shadow-top');\n    let shadowAfter = swiper.isHorizontal()\n      ? slideEl.querySelector('.swiper-slide-shadow-right')\n      : slideEl.querySelector('.swiper-slide-shadow-bottom');\n    if (!shadowBefore) {\n      shadowBefore = createShadow(params, slideEl, swiper.isHorizontal() ? 'left' : 'top');\n    }\n    if (!shadowAfter) {\n      shadowAfter = createShadow(params, slideEl, swiper.isHorizontal() ? 'right' : 'bottom');\n    }\n    if (shadowBefore) shadowBefore.style.opacity = Math.max(-progress, 0);\n    if (shadowAfter) shadowAfter.style.opacity = Math.max(progress, 0);\n  };\n\n  const recreateShadows = () => {\n    // Set shadows\n    const params = swiper.params.flipEffect;\n    swiper.slides.forEach((slideEl) => {\n      let progress = slideEl.progress;\n      if (swiper.params.flipEffect.limitRotation) {\n        progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      }\n      createSlideShadows(slideEl, progress, params);\n    });\n  };\n\n  const setTranslate = () => {\n    const { slides, rtlTranslate: rtl } = swiper;\n    const params = swiper.params.flipEffect;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      let progress = slideEl.progress;\n      if (swiper.params.flipEffect.limitRotation) {\n        progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      }\n      const offset = slideEl.swiperSlideOffset;\n      const rotate = -180 * progress;\n      let rotateY = rotate;\n      let rotateX = 0;\n      let tx = swiper.params.cssMode ? -offset - swiper.translate : -offset;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n        rotateX = -rotateY;\n        rotateY = 0;\n      } else if (rtl) {\n        rotateY = -rotateY;\n      }\n\n      slideEl.style.zIndex = -Math.abs(Math.round(progress)) + slides.length;\n\n      if (params.slideShadows) {\n        createSlideShadows(slideEl, progress, params);\n      }\n      const transform = `translate3d(${tx}px, ${ty}px, 0px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n    }\n  };\n\n  const setTransition = (duration) => {\n    const transformElements = swiper.slides.map((slideEl) => getSlideTransformEl(slideEl));\n\n    transformElements.forEach((el) => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll(\n        '.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left',\n      ).forEach((shadowEl) => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n\n    effectVirtualTransitionEnd({ swiper, duration, transformElements });\n  };\n\n  effectInit({\n    effect: 'flip',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    recreateShadows,\n    getEffectParams: () => swiper.params.flipEffect,\n    perspective: () => true,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      spaceBetween: 0,\n      virtualTranslate: !swiper.params.cssMode,\n    }),\n  });\n}\n", "import createShadow from '../../shared/create-shadow.js';\nimport effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\n\nexport default function EffectCoverflow({ swiper, extendParams, on }) {\n  extendParams({\n    coverflowEffect: {\n      rotate: 50,\n      stretch: 0,\n      depth: 100,\n      scale: 1,\n      modifier: 1,\n      slideShadows: true,\n    },\n  });\n\n  const setTranslate = () => {\n    const { width: swiperWidth, height: swiperHeight, slides, slidesSizesGrid } = swiper;\n    const params = swiper.params.coverflowEffect;\n    const isHorizontal = swiper.isHorizontal();\n    const transform = swiper.translate;\n    const center = isHorizontal ? -transform + swiperWidth / 2 : -transform + swiperHeight / 2;\n    const rotate = isHorizontal ? params.rotate : -params.rotate;\n    const translate = params.depth;\n    // Each slide offset from center\n    for (let i = 0, length = slides.length; i < length; i += 1) {\n      const slideEl = slides[i];\n      const slideSize = slidesSizesGrid[i];\n      const slideOffset = slideEl.swiperSlideOffset;\n      const centerOffset = (center - slideOffset - slideSize / 2) / slideSize;\n      const offsetMultiplier =\n        typeof params.modifier === 'function'\n          ? params.modifier(centerOffset)\n          : centerOffset * params.modifier;\n\n      let rotateY = isHorizontal ? rotate * offsetMultiplier : 0;\n      let rotateX = isHorizontal ? 0 : rotate * offsetMultiplier;\n      // var rotateZ = 0\n      let translateZ = -translate * Math.abs(offsetMultiplier);\n\n      let stretch = params.stretch;\n      // Allow percentage to make a relative stretch for responsive sliders\n      if (typeof stretch === 'string' && stretch.indexOf('%') !== -1) {\n        stretch = (parseFloat(params.stretch) / 100) * slideSize;\n      }\n      let translateY = isHorizontal ? 0 : stretch * offsetMultiplier;\n      let translateX = isHorizontal ? stretch * offsetMultiplier : 0;\n\n      let scale = 1 - (1 - params.scale) * Math.abs(offsetMultiplier);\n\n      // Fix for ultra small values\n      if (Math.abs(translateX) < 0.001) translateX = 0;\n      if (Math.abs(translateY) < 0.001) translateY = 0;\n      if (Math.abs(translateZ) < 0.001) translateZ = 0;\n      if (Math.abs(rotateY) < 0.001) rotateY = 0;\n      if (Math.abs(rotateX) < 0.001) rotateX = 0;\n      if (Math.abs(scale) < 0.001) scale = 0;\n\n      const slideTransform = `translate3d(${translateX}px,${translateY}px,${translateZ}px)  rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(${scale})`;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = slideTransform;\n\n      slideEl.style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n\n      if (params.slideShadows) {\n        // Set shadows\n        let shadowBeforeEl = isHorizontal\n          ? slideEl.querySelector('.swiper-slide-shadow-left')\n          : slideEl.querySelector('.swiper-slide-shadow-top');\n        let shadowAfterEl = isHorizontal\n          ? slideEl.querySelector('.swiper-slide-shadow-right')\n          : slideEl.querySelector('.swiper-slide-shadow-bottom');\n        if (!shadowBeforeEl) {\n          shadowBeforeEl = createShadow(params, slideEl, isHorizontal ? 'left' : 'top');\n        }\n        if (!shadowAfterEl) {\n          shadowAfterEl = createShadow(params, slideEl, isHorizontal ? 'right' : 'bottom');\n        }\n        if (shadowBeforeEl)\n          shadowBeforeEl.style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0;\n        if (shadowAfterEl)\n          shadowAfterEl.style.opacity = -offsetMultiplier > 0 ? -offsetMultiplier : 0;\n      }\n    }\n  };\n  const setTransition = (duration) => {\n    const transformElements = swiper.slides.map((slideEl) => getSlideTransformEl(slideEl));\n\n    transformElements.forEach((el) => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll(\n        '.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left',\n      ).forEach((shadowEl) => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n  };\n\n  effectInit({\n    effect: 'coverflow',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => true,\n    overwriteParams: () => ({\n      watchSlidesProgress: true,\n    }),\n  });\n}\n", "import createShadow from '../../shared/create-shadow.js';\nimport effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport effectVirtualTransitionEnd from '../../shared/effect-virtual-transition-end.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\n\nexport default function EffectCreative({ swiper, extendParams, on }) {\n  extendParams({\n    creativeEffect: {\n      limitProgress: 1,\n      shadowPerProgress: false,\n      progressMultiplier: 1,\n      perspective: true,\n      prev: {\n        translate: [0, 0, 0],\n        rotate: [0, 0, 0],\n        opacity: 1,\n        scale: 1,\n      },\n      next: {\n        translate: [0, 0, 0],\n        rotate: [0, 0, 0],\n        opacity: 1,\n        scale: 1,\n      },\n    },\n  });\n\n  const getTranslateValue = (value) => {\n    if (typeof value === 'string') return value;\n    return `${value}px`;\n  };\n\n  const setTranslate = () => {\n    const { slides, wrapperEl, slidesSizesGrid } = swiper;\n    const params = swiper.params.creativeEffect;\n    const { progressMultiplier: multiplier } = params;\n\n    const isCenteredSlides = swiper.params.centeredSlides;\n\n    if (isCenteredSlides) {\n      const margin = slidesSizesGrid[0] / 2 - swiper.params.slidesOffsetBefore || 0;\n      wrapperEl.style.transform = `translateX(calc(50% - ${margin}px))`;\n    }\n\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      const slideProgress = slideEl.progress;\n      const progress = Math.min(\n        Math.max(slideEl.progress, -params.limitProgress),\n        params.limitProgress,\n      );\n      let originalProgress = progress;\n\n      if (!isCenteredSlides) {\n        originalProgress = Math.min(\n          Math.max(slideEl.originalProgress, -params.limitProgress),\n          params.limitProgress,\n        );\n      }\n\n      const offset = slideEl.swiperSlideOffset;\n      const t = [swiper.params.cssMode ? -offset - swiper.translate : -offset, 0, 0];\n      const r = [0, 0, 0];\n      let custom = false;\n      if (!swiper.isHorizontal()) {\n        t[1] = t[0];\n        t[0] = 0;\n      }\n      let data = {\n        translate: [0, 0, 0],\n        rotate: [0, 0, 0],\n        scale: 1,\n        opacity: 1,\n      };\n      if (progress < 0) {\n        data = params.next;\n        custom = true;\n      } else if (progress > 0) {\n        data = params.prev;\n        custom = true;\n      }\n      // set translate\n      t.forEach((value, index) => {\n        t[index] = `calc(${value}px + (${getTranslateValue(data.translate[index])} * ${Math.abs(\n          progress * multiplier,\n        )}))`;\n      });\n      // set rotates\n      r.forEach((value, index) => {\n        r[index] = data.rotate[index] * Math.abs(progress * multiplier);\n      });\n\n      slideEl.style.zIndex = -Math.abs(Math.round(slideProgress)) + slides.length;\n\n      const translateString = t.join(', ');\n      const rotateString = `rotateX(${r[0]}deg) rotateY(${r[1]}deg) rotateZ(${r[2]}deg)`;\n      const scaleString =\n        originalProgress < 0\n          ? `scale(${1 + (1 - data.scale) * originalProgress * multiplier})`\n          : `scale(${1 - (1 - data.scale) * originalProgress * multiplier})`;\n      const opacityString =\n        originalProgress < 0\n          ? 1 + (1 - data.opacity) * originalProgress * multiplier\n          : 1 - (1 - data.opacity) * originalProgress * multiplier;\n      const transform = `translate3d(${translateString}) ${rotateString} ${scaleString}`;\n\n      // Set shadows\n      if ((custom && data.shadow) || !custom) {\n        let shadowEl = slideEl.querySelector('.swiper-slide-shadow');\n        if (!shadowEl && data.shadow) {\n          shadowEl = createShadow(params, slideEl);\n        }\n        if (shadowEl) {\n          const shadowOpacity = params.shadowPerProgress\n            ? progress * (1 / params.limitProgress)\n            : progress;\n          shadowEl.style.opacity = Math.min(Math.max(Math.abs(shadowOpacity), 0), 1);\n        }\n      }\n\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n      targetEl.style.opacity = opacityString;\n      if (data.origin) {\n        targetEl.style.transformOrigin = data.origin;\n      }\n    }\n  };\n\n  const setTransition = (duration) => {\n    const transformElements = swiper.slides.map((slideEl) => getSlideTransformEl(slideEl));\n\n    transformElements.forEach((el) => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow').forEach((shadowEl) => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n\n    effectVirtualTransitionEnd({ swiper, duration, transformElements, allSlides: true });\n  };\n\n  effectInit({\n    effect: 'creative',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => swiper.params.creativeEffect.perspective,\n    overwriteParams: () => ({\n      watchSlidesProgress: true,\n      virtualTranslate: !swiper.params.cssMode,\n    }),\n  });\n}\n", "import createShadow from '../../shared/create-shadow.js';\nimport effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nimport effectVirtualTransitionEnd from '../../shared/effect-virtual-transition-end.js';\nimport { getSlideTransformEl } from '../../shared/utils.js';\n\nexport default function EffectCards({ swiper, extendParams, on }) {\n  extendParams({\n    cardsEffect: {\n      slideShadows: true,\n      rotate: true,\n      perSlideRotate: 2,\n      perSlideOffset: 8,\n    },\n  });\n\n  const setTranslate = () => {\n    const { slides, activeIndex } = swiper;\n    const params = swiper.params.cardsEffect;\n    const { startTranslate, isTouched } = swiper.touchEventsData;\n    const currentTranslate = swiper.translate;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      const slideProgress = slideEl.progress;\n      const progress = Math.min(Math.max(slideProgress, -4), 4);\n      let offset = slideEl.swiperSlideOffset;\n      if (swiper.params.centeredSlides && !swiper.params.cssMode) {\n        swiper.wrapperEl.style.transform = `translateX(${swiper.minTranslate()}px)`;\n      }\n      if (swiper.params.centeredSlides && swiper.params.cssMode) {\n        offset -= slides[0].swiperSlideOffset;\n      }\n      let tX = swiper.params.cssMode ? -offset - swiper.translate : -offset;\n      let tY = 0;\n      const tZ = -100 * Math.abs(progress);\n      let scale = 1;\n      let rotate = -params.perSlideRotate * progress;\n\n      let tXAdd = params.perSlideOffset - Math.abs(progress) * 0.75;\n\n      const slideIndex =\n        swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.from + i : i;\n\n      const isSwipeToNext =\n        (slideIndex === activeIndex || slideIndex === activeIndex - 1) &&\n        progress > 0 &&\n        progress < 1 &&\n        (isTouched || swiper.params.cssMode) &&\n        currentTranslate < startTranslate;\n      const isSwipeToPrev =\n        (slideIndex === activeIndex || slideIndex === activeIndex + 1) &&\n        progress < 0 &&\n        progress > -1 &&\n        (isTouched || swiper.params.cssMode) &&\n        currentTranslate > startTranslate;\n\n      if (isSwipeToNext || isSwipeToPrev) {\n        const subProgress = (1 - Math.abs((Math.abs(progress) - 0.5) / 0.5)) ** 0.5;\n        rotate += -28 * progress * subProgress;\n        scale += -0.5 * subProgress;\n        tXAdd += 96 * subProgress;\n        tY = `${-25 * subProgress * Math.abs(progress)}%`;\n      }\n\n      if (progress < 0) {\n        // next\n        tX = `calc(${tX}px + (${tXAdd * Math.abs(progress)}%))`;\n      } else if (progress > 0) {\n        // prev\n        tX = `calc(${tX}px + (-${tXAdd * Math.abs(progress)}%))`;\n      } else {\n        tX = `${tX}px`;\n      }\n      if (!swiper.isHorizontal()) {\n        const prevY = tY;\n        tY = tX;\n        tX = prevY;\n      }\n\n      const scaleString =\n        progress < 0 ? `${1 + (1 - scale) * progress}` : `${1 - (1 - scale) * progress}`;\n\n      const transform = `\n        translate3d(${tX}, ${tY}, ${tZ}px)\n        rotateZ(${params.rotate ? rotate : 0}deg)\n        scale(${scaleString})\n      `;\n\n      if (params.slideShadows) {\n        // Set shadows\n        let shadowEl = slideEl.querySelector('.swiper-slide-shadow');\n        if (!shadowEl) {\n          shadowEl = createShadow(params, slideEl);\n        }\n        if (shadowEl)\n          shadowEl.style.opacity = Math.min(Math.max((Math.abs(progress) - 0.5) / 0.5, 0), 1);\n      }\n\n      slideEl.style.zIndex = -Math.abs(Math.round(slideProgress)) + slides.length;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n    }\n  };\n\n  const setTransition = (duration) => {\n    const transformElements = swiper.slides.map((slideEl) => getSlideTransformEl(slideEl));\n    transformElements.forEach((el) => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow').forEach((shadowEl) => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n\n    effectVirtualTransitionEnd({ swiper, duration, transformElements });\n  };\n\n  effectInit({\n    effect: 'cards',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => true,\n    overwriteParams: () => ({\n      watchSlidesProgress: true,\n      virtualTranslate: !swiper.params.cssMode,\n    }),\n  });\n}\n"], "mappings": ";;;;;;;;;;;;uOAYA,SAASA,EAASC,GACd,OAAgB,OAARA,GACW,iBAARA,GACP,gBAAiBA,GACjBA,EAAIC,cAAgBC,MAC5B,CACA,SAASC,EAAOC,EAAaC,QAAP,IAAND,MAAS,SAAO,IAAHC,MAAM,IAC/BH,OAAOI,KAAKD,GAAKE,SAASC,SACK,IAAhBJ,EAAOI,GACdJ,EAAOI,GAAOH,EAAIG,GACbT,EAASM,EAAIG,KAClBT,EAASK,EAAOI,KAChBN,OAAOI,KAAKD,EAAIG,IAAMC,OAAS,GAC/BN,EAAOC,EAAOI,GAAMH,EAAIG,GAC5B,GAER,CAEA,MAAME,EAAc,CAChBC,KAAM,GACNC,mBAAmB,EACnBC,sBAAsB,EACtBC,cAAe,CACXC,OAAO,EACPC,SAAU,IAEdC,cAAa,IACF,KAEXC,iBAAgB,IACL,GAEXC,eAAc,IACH,KAEXC,YAAW,KACA,CACHC,YAAY,IAGpBC,cAAa,KACF,CACHC,SAAU,GACVC,WAAY,GACZC,MAAO,GACPC,eAAe,EACfC,qBAAoB,IACT,KAInBC,gBAAe,KACJ,IAEXC,WAAU,IACC,KAEXC,SAAU,CACNC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,KAGhB,SAASC,IACL,MAAMC,EAA0B,oBAAbC,SAA2BA,SAAW,GAEzD,OADAtC,EAAOqC,EAAK9B,GACL8B,CACX,CAEA,MAAME,EAAY,CACdD,SAAU/B,EACViC,UAAW,CACPC,UAAW,IAEfd,SAAU,CACNC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,IAEZO,QAAS,CACLC,eAAe,EACfC,YAAY,EACZC,KAAK,EACLC,OAAO,GAEXC,YAAa,WACT,OAAOC,I,EAEXvC,mBAAmB,EACnBC,sBAAsB,EACtBuC,iBAAgB,KACL,CACHC,iBAAgB,IACL,KAInBC,QAAQ,EACRC,OAAO,EACPC,OAAQ,GACRC,aAAa,EACbC,eAAe,EACfC,WAAU,KACC,IAEXC,sBAAsBC,GACQ,oBAAfJ,YACPI,IACO,MAEJJ,WAAWI,EAAU,GAEhCC,qBAAqBC,GACS,oBAAfN,YAGXC,aAAaK,EACjB,GAEJ,SAASC,IACL,MAAMC,EAAwB,oBAAXC,OAAyBA,OAAS,GAErD,OADA/D,EAAO8D,EAAKvB,GACLuB,CACX,CChIA,SAASE,EAASN,EAAUO,GAC1B,YAD+B,IAALA,MAAQ,GAC3BX,WAAWI,EAAUO,EAC9B,CACA,SAASC,IACP,OAAOd,KAAKc,KACd,CAgBA,SAASC,EAAaC,EAAIC,QAAI,IAAJA,MAAO,KAC/B,MAAMN,EAASF,IACf,IAAIS,EACAC,EACAC,EAEJ,MAAMC,EArBR,SAA0BL,GACxB,MAAML,EAASF,IACf,IAAIvC,EAWJ,OAVIyC,EAAOd,mBACT3B,EAAQyC,EAAOd,iBAAiBmB,EAAI,QAEjC9C,GAAS8C,EAAGM,eACfpD,EAAQ8C,EAAGM,cAERpD,IACHA,EAAQ8C,EAAG9C,OAGNA,CACT,CAOmB2B,CAAiBmB,GAwClC,OAtCIL,EAAOY,iBACTJ,EAAeE,EAASG,WAAaH,EAASI,gBAC1CN,EAAaO,MAAM,KAAKxE,OAAS,IACnCiE,EAAeA,EACZO,MAAM,MACNC,KAAKC,GAAMA,EAAEC,QAAQ,IAAK,OAC1BC,KAAK,OAIVV,EAAkB,IAAIT,EAAOY,gBAAiC,SAAjBJ,EAA0B,GAAKA,KAE5EC,EACEC,EAASU,cACTV,EAASW,YACTX,EAASY,aACTZ,EAASa,aACTb,EAASG,WACTH,EAASvB,iBAAiB,aAAa+B,QAAQ,aAAc,sBAC/DX,EAASE,EAAgBe,WAAWT,MAAM,MAG/B,MAATT,IAE0BE,EAAxBR,EAAOY,gBAAgCH,EAAgBgB,IAEhC,KAAlBlB,EAAOhE,OAA8BmF,WAAWnB,EAAO,KAE5CmB,WAAWnB,EAAO,KAE3B,MAATD,IAE0BE,EAAxBR,EAAOY,gBAAgCH,EAAgBkB,IAEhC,KAAlBpB,EAAOhE,OAA8BmF,WAAWnB,EAAO,KAE5CmB,WAAWnB,EAAO,KAEjCC,GAAgB,CACzB,CACA,SAAS3E,EAAS+F,GAChB,MACe,iBAANA,GACD,OAANA,GACAA,EAAE7F,aACiD,WAAnDC,OAAO6F,UAAUL,SAASM,KAAKF,GAAGG,MAAM,GAAI,EAEhD,CACA,SAASC,EAAOC,GAEd,MAAsB,oBAAXjC,aAAwD,IAAvBA,OAAOkC,YAC1CD,aAAgBC,YAElBD,IAA2B,IAAlBA,EAAKE,UAAoC,KAAlBF,EAAKE,SAC9C,CACA,SAASlG,IACP,MAAMmG,EAAKpG,OAAeqG,UAAA9F,QAAA,OAAA+F,EAAAD,UAAA,IACpBE,EAAW,CAAC,YAAa,cAAe,aAC9C,IAAK,IAAIC,EAAI,EAAGA,EAAIH,UAAK9F,OAAQiG,GAAK,EAAG,CACvC,MAAMC,EAAkBD,EAAC,GAAAH,UAAA9F,QAADiG,OAACF,EAAAD,UAADG,GACxB,GAAIC,UAAoDT,EAAOS,GAAa,CAC1E,MAAMC,EAAY1G,OAAOI,KAAKJ,OAAOyG,IAAaE,QAAQrG,GAAQiG,EAASK,QAAQtG,GAAO,IAC1F,IAAK,IAAIuG,EAAY,EAAGC,EAAMJ,EAAUnG,OAAQsG,EAAYC,EAAKD,GAAa,EAAG,CAC/E,MAAME,EAAUL,EAAUG,GACpBG,EAAOhH,OAAOiH,yBAAyBR,EAAYM,QAC5CT,IAATU,GAAsBA,EAAKE,aACzBrH,EAASuG,EAAGW,KAAalH,EAAS4G,EAAWM,IAC3CN,EAAWM,GAASI,WACtBf,EAAGW,GAAWN,EAAWM,GAEzB9G,EAAOmG,EAAGW,GAAUN,EAAWM,KAEvBlH,EAASuG,EAAGW,KAAalH,EAAS4G,EAAWM,KACvDX,EAAGW,GAAW,GACVN,EAAWM,GAASI,WACtBf,EAAGW,GAAWN,EAAWM,GAEzB9G,EAAOmG,EAAGW,GAAUN,EAAWM,KAGjCX,EAAGW,GAAWN,EAAWM,GAG/B,CACF,CACF,CACA,OAAOX,CACT,CAEA,SAASgB,EAAe/C,EAAIgD,EAASC,GACnCjD,EAAG9C,MAAMgG,YAAYF,EAASC,EAChC,CAEA,SAASE,EAAuDC,GAAA,IAAlCC,OAAEA,EAAMC,eAAEA,EAAcC,KAAEA,GAAMH,EAC5D,MAAMzD,EAASF,IACT+D,GAAiBH,EAAOI,UAC9B,IACIC,EADAC,EAAY,KAEhB,MAAMC,EAAWP,EAAOQ,OAAOC,MAE/BT,EAAOU,UAAU7G,MAAM8G,eAAiB,OACxCrE,EAAOJ,qBAAqB8D,EAAOY,gBAEnC,MAAMC,EAAMZ,EAAiBE,EAAgB,OAAS,OAEhDW,EAAe,CAACC,EAASvI,IACb,SAARqI,GAAkBE,GAAWvI,GAAoB,SAARqI,GAAkBE,GAAWvI,EAG1EwI,EAAU,KACdX,GAAO,IAAI1E,MAAOsF,UACA,OAAdX,IACFA,EAAYD,GAGd,MAAMa,EAAWC,KAAKC,IAAID,KAAKE,KAAKhB,EAAOC,GAAaC,EAAU,GAAI,GAChEe,EAAe,GAAMH,KAAKI,IAAIL,EAAWC,KAAKK,IAAM,EAC1D,IAAIC,EAAkBtB,EAAgBmB,GAAgBrB,EAAiBE,GAQvE,GANIW,EAAaW,EAAiBxB,KAChCwB,EAAkBxB,GAEpBD,EAAOU,UAAUgB,SAAS,CACxBxB,CAACA,GAAOuB,IAENX,EAAaW,EAAiBxB,GAUhC,OATAD,EAAOU,UAAU7G,MAAM8H,SAAW,SAClC3B,EAAOU,UAAU7G,MAAM8G,eAAiB,GACxC9E,YAAW,KACTmE,EAAOU,UAAU7G,MAAM8H,SAAW,GAClC3B,EAAOU,UAAUgB,SAAS,CACxBxB,CAACA,GAAOuB,GACR,SAEJnF,EAAOJ,qBAAqB8D,EAAOY,gBAGrCZ,EAAOY,eAAiBtE,EAAON,sBAAsBgF,EAAQ,EAE/DA,GACF,CAEA,SAASY,EAAoBC,GAC3B,OACEA,EAAQxI,cAAc,4BACrBwI,EAAQC,UAAYD,EAAQC,SAASzI,cAAc,4BACpDwI,CAEJ,CASA,SAASE,EAAgBC,EAASC,GAChC,YADwC,IAARA,MAAW,IACpC,IAAID,EAAQrI,UAAUsF,QAAQtC,GAAOA,EAAGuF,QAAQD,IACzD,CAEA,SAASvI,EAAcyI,EAAKC,QAAO,IAAPA,MAAU,IACpC,MAAMzF,EAAK9B,SAASnB,cAAcyI,GAElC,OADAxF,EAAG0F,UAAUC,OAAQC,MAAMC,QAAQJ,GAAWA,EAAU,CAACA,IAClDzF,CACT,CACA,SAAS8F,EAAc9F,GACrB,MAAML,EAASF,IACTvB,EAAWF,IACX+H,EAAM/F,EAAGgG,wBACT5J,EAAO8B,EAAS9B,KAChB6J,EAAYjG,EAAGiG,WAAa7J,EAAK6J,WAAa,EAC9CC,EAAalG,EAAGkG,YAAc9J,EAAK8J,YAAc,EACjDC,EAAYnG,IAAOL,EAASA,EAAOyG,QAAUpG,EAAGmG,UAChDE,EAAarG,IAAOL,EAASA,EAAO2G,QAAUtG,EAAGqG,WACvD,MAAO,CACLE,IAAKR,EAAIQ,IAAMJ,EAAYF,EAC3BO,KAAMT,EAAIS,KAAOH,EAAaH,EAElC,CAuBA,SAASO,EAAazG,EAAI0G,GAExB,OADejH,IACDZ,iBAAiBmB,EAAI,MAAMlB,iBAAiB4H,EAC5D,CACA,SAASC,EAAa3G,GACpB,IACImC,EADAyE,EAAQ5G,EAEZ,GAAI4G,EAAO,CAGT,IAFAzE,EAAI,EAEuC,QAAnCyE,EAAQA,EAAMC,kBACG,IAAnBD,EAAM9E,WAAgBK,GAAK,GAEjC,OAAOA,CACT,CAEF,CAEA,SAAS2E,EAAe9G,EAAIsF,GAC1B,MAAMyB,EAAU,GAChB,IAAIC,EAAShH,EAAGiH,cAChB,KAAOD,GACD1B,EACE0B,EAAOzB,QAAQD,IAAWyB,EAAQG,KAAKF,GAE3CD,EAAQG,KAAKF,GAEfA,EAASA,EAAOC,cAElB,OAAOF,CACT,CAEA,SAASI,EAAqBnH,EAAIV,GAM5BA,GACFU,EAAG3D,iBAAiB,iBANtB,SAAS+K,EAAaC,GAChBA,EAAExL,SAAWmE,IACjBV,EAASmC,KAAKzB,EAAIqH,GAClBrH,EAAG1D,oBAAoB,gBAAiB8K,GAC1C,GAIF,CAEA,SAASE,EAAiBtH,EAAIuH,EAAMC,GAClC,MAAM7H,EAASF,IACf,OAAI+H,EAEAxH,EAAY,UAATuH,EAAmB,cAAgB,gBACtClG,WACE1B,EACGd,iBAAiBmB,EAAI,MACrBlB,iBAA0B,UAATyI,EAAmB,eAAiB,eAE1DlG,WACE1B,EACGd,iBAAiBmB,EAAI,MACrBlB,iBAA0B,UAATyI,EAAmB,cAAgB,kBAItDvH,EAAGyH,WACZ,CCnTA,IAAIC,ECCAC,ECDAC,EFgBJ,SAASC,IAIP,OAHKH,IACHA,EAhBJ,WACE,MAAM/H,EAASF,IACTvB,EAAWF,IAEjB,MAAO,CACL8J,aAAc5J,EAAS6J,iBAAmB,mBAAoB7J,EAAS6J,gBAAgB7K,MAEvF8K,SACE,iBAAkBrI,GACjBA,EAAOsI,eAAiB/J,aAAoByB,EAAOsI,eAG1D,CAIcC,IAELR,CACT,CC2CA,SAASS,EAAUC,GAIjB,YAJ0B,IAATA,MAAY,IACxBT,IACHA,EA/DJ,SAAwCU,GAAA,IAApBhK,UAAEA,QAAW,IAAAgK,EAAG,GAAEA,EACpC,MAAMX,EAAUG,IACVlI,EAASF,IACT6I,EAAW3I,EAAOvB,UAAUkK,SAC5BC,EAAKlK,GAAasB,EAAOvB,UAAUC,UAEnCmK,EAAS,CACbC,KAAK,EACLC,SAAS,GAGLC,EAAchJ,EAAOV,OAAO2J,MAC5BC,EAAelJ,EAAOV,OAAO6J,OAE7BJ,EAAUH,EAAGQ,MAAM,+BACzB,IAAIC,EAAOT,EAAGQ,MAAM,wBACpB,MAAME,EAAOV,EAAGQ,MAAM,2BAChBG,GAAUF,GAAQT,EAAGQ,MAAM,8BAC3BI,EAAuB,UAAbb,EAChB,IAAIc,EAAqB,aAAbd,EAuCZ,OArBGU,GACDI,GACA1B,EAAQM,OAjBU,CAClB,YACA,YACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,YAMYzF,QAAS,GAAEoG,KAAeE,MAAmB,IAEzDG,EAAOT,EAAGQ,MAAM,uBACXC,IAAMA,EAAO,CAAC,EAAG,EAAG,WACzBI,GAAQ,GAINV,IAAYS,IACdX,EAAOa,GAAK,UACZb,EAAOE,SAAU,IAEfM,GAAQE,GAAUD,KACpBT,EAAOa,GAAK,MACZb,EAAOC,KAAM,GAIRD,CACT,CAImBc,CAAWlB,IAErBT,CACT,CC1CA,SAAS4B,IAIP,OAHK3B,IACHA,EA3BJ,WACE,MAAMjI,EAASF,IACf,IAAI+J,GAAqB,EACzB,SAASC,IACP,MAAMlB,EAAK5I,EAAOvB,UAAUC,UAAUqL,cACtC,OAAOnB,EAAGhG,QAAQ,WAAa,GAAKgG,EAAGhG,QAAQ,UAAY,GAAKgG,EAAGhG,QAAQ,WAAa,CAC1F,CACA,GAAIkH,IAAY,CACd,MAAMlB,EAAKoB,OAAOhK,EAAOvB,UAAUC,WACnC,GAAIkK,EAAGqB,SAAS,YAAa,CAC3B,MAAOC,EAAOC,GAASvB,EACpB7H,MAAM,YAAY,GAClBA,MAAM,KAAK,GACXA,MAAM,KACNC,KAAKoJ,GAAQC,OAAOD,KACvBP,EAAqBK,EAAQ,IAAiB,KAAVA,GAAgBC,EAAQ,CAC9D,CACF,CACA,MAAO,CACLL,SAAUD,GAAsBC,IAChCD,qBACAS,UAAW,+CAA+CC,KAAKvK,EAAOvB,UAAUC,WAEpF,CAIc8L,IAELvC,CACT,CChCA,IAAAwC,EAAe,CACbC,GAAGC,EAAQC,EAASC,GAClB,MAAMC,EAAO7L,KACb,IAAK6L,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,GAAuB,mBAAZF,EAAwB,OAAOE,EAC1C,MAAMG,EAASJ,EAAW,UAAY,OAKtC,OAJAF,EAAO5J,MAAM,KAAK1E,SAAS6O,IACpBJ,EAAKC,gBAAgBG,KAAQJ,EAAKC,gBAAgBG,GAAS,IAChEJ,EAAKC,gBAAgBG,GAAOD,GAAQL,EAAQ,IAEvCE,C,EAGTK,KAAKR,EAAQC,EAASC,GACpB,MAAMC,EAAO7L,KACb,IAAK6L,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,GAAuB,mBAAZF,EAAwB,OAAOE,EAC1C,SAASM,IACPN,EAAKO,IAAIV,EAAQS,GACbA,EAAYE,uBACPF,EAAYE,eACpB,QAAAC,EAAAlJ,UAAA9F,OAJqBiP,EAAI,IAAAvF,MAAAsF,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAJD,EAAIC,GAAApJ,UAAAoJ,GAK1Bb,EAAQc,MAAMZ,EAAMU,EACtB,CAEA,OADAJ,EAAYE,eAAiBV,EACtBE,EAAKJ,GAAGC,EAAQS,EAAaP,E,EAGtCc,MAAMf,EAASC,GACb,MAAMC,EAAO7L,KACb,IAAK6L,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,GAAuB,mBAAZF,EAAwB,OAAOE,EAC1C,MAAMG,EAASJ,EAAW,UAAY,OAItC,OAHIC,EAAKc,mBAAmBhJ,QAAQgI,GAAW,GAC7CE,EAAKc,mBAAmBX,GAAQL,GAE3BE,C,EAGTe,OAAOjB,GACL,MAAME,EAAO7L,KACb,IAAK6L,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,IAAKA,EAAKc,mBAAoB,OAAOd,EACrC,MAAMgB,EAAQhB,EAAKc,mBAAmBhJ,QAAQgI,GAI9C,OAHIkB,GAAS,GACXhB,EAAKc,mBAAmBG,OAAOD,EAAO,GAEjChB,C,EAGTO,IAAIV,EAAQC,GACV,MAAME,EAAO7L,KACb,OAAK6L,EAAKC,iBAAmBD,EAAKE,UAAkBF,EAC/CA,EAAKC,iBACVJ,EAAO5J,MAAM,KAAK1E,SAAS6O,SACF,IAAZN,EACTE,EAAKC,gBAAgBG,GAAS,GACrBJ,EAAKC,gBAAgBG,IAC9BJ,EAAKC,gBAAgBG,GAAO7O,SAAQ,CAAC2P,EAAcF,MAE/CE,IAAiBpB,GAChBoB,EAAaV,gBAAkBU,EAAaV,iBAAmBV,IAEhEE,EAAKC,gBAAgBG,GAAOa,OAAOD,EAAO,EAC5C,GAEJ,IAEKhB,GAf2BA,C,EAkBpCmB,OACE,MAAMnB,EAAO7L,KACb,IAAK6L,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,IAAKA,EAAKC,gBAAiB,OAAOD,EAClC,IAAIH,EACAuB,EACAC,EAAQ,QAAAC,EAAA/J,UAAA9F,OANNiP,EAAI,IAAAvF,MAAAmG,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJb,EAAIa,GAAAhK,UAAAgK,GAOa,iBAAZb,EAAK,IAAmBvF,MAAMC,QAAQsF,EAAK,KACpDb,EAASa,EAAK,GACdU,EAAOV,EAAKzJ,MAAM,EAAGyJ,EAAKjP,QAC1B4P,EAAUrB,IAEVH,EAASa,EAAK,GAAGb,OACjBuB,EAAOV,EAAK,GAAGU,KACfC,EAAUX,EAAK,GAAGW,SAAWrB,GAE/BoB,EAAKI,QAAQH,GAeb,OAdoBlG,MAAMC,QAAQyE,GAAUA,EAASA,EAAO5J,MAAM,MAEtD1E,SAAS6O,IACfJ,EAAKc,oBAAsBd,EAAKc,mBAAmBrP,QACrDuO,EAAKc,mBAAmBvP,SAAS2P,IAC/BA,EAAaN,MAAMS,EAAS,CAACjB,KAAUgB,GAAM,IAG7CpB,EAAKC,iBAAmBD,EAAKC,gBAAgBG,IAC/CJ,EAAKC,gBAAgBG,GAAO7O,SAAS2P,IACnCA,EAAaN,MAAMS,EAASD,EAAK,GAErC,IAEKpB,CACT,GC/FF,IAAAyB,EAAe,CACbC,WCTa,WACb,MAAM9I,EAASzE,KACf,IAAIgK,EACAE,EACJ,MAAM9I,EAAKqD,EAAOrD,GAEhB4I,OADiC,IAAxBvF,EAAOQ,OAAO+E,OAAiD,OAAxBvF,EAAOQ,OAAO+E,MACtDvF,EAAOQ,OAAO+E,MAEd5I,EAAGoM,YAGXtD,OADkC,IAAzBzF,EAAOQ,OAAOiF,QAAmD,OAAzBzF,EAAOQ,OAAOiF,OACtDzF,EAAOQ,OAAOiF,OAEd9I,EAAGqM,aAEC,IAAVzD,GAAevF,EAAOiJ,gBAA+B,IAAXxD,GAAgBzF,EAAOkJ,eAKtE3D,EACEA,EACA4D,SAAS/F,EAAazG,EAAI,iBAAmB,EAAG,IAChDwM,SAAS/F,EAAazG,EAAI,kBAAoB,EAAG,IACnD8I,EACEA,EACA0D,SAAS/F,EAAazG,EAAI,gBAAkB,EAAG,IAC/CwM,SAAS/F,EAAazG,EAAI,mBAAqB,EAAG,IAEhDgK,OAAOyC,MAAM7D,KAAQA,EAAQ,GAC7BoB,OAAOyC,MAAM3D,KAASA,EAAS,GAEnCnN,OAAO+Q,OAAOrJ,EAAQ,CACpBuF,QACAE,SACAvB,KAAMlE,EAAOiJ,eAAiB1D,EAAQE,IAE1C,ED3BE6D,aELa,WACb,MAAMtJ,EAASzE,KACf,SAASgO,EAAkBC,GACzB,OAAIxJ,EAAOiJ,eACFO,EAGF,CACLjE,MAAS,SACT,aAAc,cACd,iBAAkB,eAClB,cAAe,aACf,eAAgB,gBAChB,eAAgB,cAChB,gBAAiB,iBACjBkE,YAAe,gBACfD,EACJ,CACA,SAASE,EAA0BnL,EAAMoL,GACvC,OAAO3L,WAAWO,EAAK9C,iBAAiB8N,EAAkBI,KAAW,EACvE,CAEA,MAAMnJ,EAASR,EAAOQ,QAEhBE,UAAEA,EAASkJ,SAAEA,EAAU1F,KAAM2F,EAAYC,aAAcC,EAAGC,SAAEA,GAAahK,EACzEiK,EAAYjK,EAAOkK,SAAW1J,EAAO0J,QAAQC,QAC7CC,EAAuBH,EAAYjK,EAAOkK,QAAQG,OAAOxR,OAASmH,EAAOqK,OAAOxR,OAChFwR,EAAStI,EAAgB6H,EAAW,IAAG5J,EAAOQ,OAAO8J,4BACrDC,EAAeN,EAAYjK,EAAOkK,QAAQG,OAAOxR,OAASwR,EAAOxR,OACvE,IAAI2R,EAAW,GACf,MAAMC,EAAa,GACbC,EAAkB,GAExB,IAAIC,EAAenK,EAAOoK,mBACE,mBAAjBD,IACTA,EAAenK,EAAOoK,mBAAmBxM,KAAK4B,IAGhD,IAAI6K,EAAcrK,EAAOsK,kBACE,mBAAhBD,IACTA,EAAcrK,EAAOsK,kBAAkB1M,KAAK4B,IAG9C,MAAM+K,EAAyB/K,EAAOwK,SAAS3R,OACzCmS,EAA2BhL,EAAOyK,WAAW5R,OAEnD,IAAIoS,EAAezK,EAAOyK,aACtBC,GAAiBP,EACjBQ,EAAgB,EAChB/C,EAAQ,EACZ,QAA0B,IAAfyB,EACT,OAE0B,iBAAjBoB,GAA6BA,EAAa/L,QAAQ,MAAQ,IACnE+L,EAAgBjN,WAAWiN,EAAazN,QAAQ,IAAK,KAAO,IAAOqM,GAGrE7J,EAAOoL,aAAeH,EAGtBZ,EAAO1R,SAASkJ,IACVkI,EACFlI,EAAQhI,MAAMwR,WAAa,GAE3BxJ,EAAQhI,MAAM4P,YAAc,GAE9B5H,EAAQhI,MAAMyR,aAAe,GAC7BzJ,EAAQhI,MAAM0R,UAAY,EAAE,IAI1B/K,EAAOgL,gBAAkBhL,EAAOiL,UAClC/L,EAAegB,EAAW,kCAAmC,IAC7DhB,EAAegB,EAAW,iCAAkC,KAG9D,MAAMgL,EAAclL,EAAOmL,MAAQnL,EAAOmL,KAAKC,KAAO,GAAK5L,EAAO2L,KAMlE,IAAIE,EALAH,GACF1L,EAAO2L,KAAKG,WAAWvB,GAMzB,MAAMwB,EACqB,SAAzBvL,EAAOwL,eACPxL,EAAOyL,aACP3T,OAAOI,KAAK8H,EAAOyL,aAAahN,QAAQrG,QACkB,IAA1C4H,EAAOyL,YAAYrT,GAAKoT,gBACrCnT,OAAS,EAEd,IAAK,IAAIiG,EAAI,EAAGA,EAAIyL,EAAczL,GAAK,EAAG,CAExC,IAAIoN,EAKJ,GANAL,EAAY,EAERxB,EAAOvL,KAAIoN,EAAQ7B,EAAOvL,IAC1B4M,GACF1L,EAAO2L,KAAKQ,YAAYrN,EAAGoN,EAAO3B,EAAchB,IAE9Cc,EAAOvL,IAAyC,SAAnCsE,EAAa8I,EAAO,WAArC,CAEA,GAA6B,SAAzB1L,EAAOwL,cAA0B,CAC/BD,IACF1B,EAAOvL,GAAGjF,MAAM0P,EAAkB,UAAa,IAEjD,MAAM6C,EAAc5Q,iBAAiB0Q,GAC/BG,EAAmBH,EAAMrS,MAAMsD,UAC/BmP,EAAyBJ,EAAMrS,MAAMuD,gBAO3C,GANIiP,IACFH,EAAMrS,MAAMsD,UAAY,QAEtBmP,IACFJ,EAAMrS,MAAMuD,gBAAkB,QAE5BoD,EAAO+L,aACTV,EAAY7L,EAAOiJ,eACfhF,EAAiBiI,EAAO,SAAS,GACjCjI,EAAiBiI,EAAO,UAAU,OACjC,CAEL,MAAM3G,EAAQmE,EAA0B0C,EAAa,SAC/CI,EAAc9C,EAA0B0C,EAAa,gBACrDK,EAAe/C,EAA0B0C,EAAa,iBACtDf,EAAa3B,EAA0B0C,EAAa,eACpD3C,EAAcC,EAA0B0C,EAAa,gBACrDM,EAAYN,EAAY3Q,iBAAiB,cAC/C,GAAIiR,GAA2B,eAAdA,EACfb,EAAYtG,EAAQ8F,EAAa5B,MAC5B,CACL,MAAMV,YAAEA,EAAW3E,YAAEA,GAAgB8H,EACrCL,EACEtG,EACAiH,EACAC,EACApB,EACA5B,GACCrF,EAAc2E,EACnB,CACF,CACIsD,IACFH,EAAMrS,MAAMsD,UAAYkP,GAEtBC,IACFJ,EAAMrS,MAAMuD,gBAAkBkP,GAE5B9L,EAAO+L,eAAcV,EAAY1K,KAAKwL,MAAMd,GAClD,MACEA,GAAahC,GAAcrJ,EAAOwL,cAAgB,GAAKf,GAAgBzK,EAAOwL,cAC1ExL,EAAO+L,eAAcV,EAAY1K,KAAKwL,MAAMd,IAE5CxB,EAAOvL,KACTuL,EAAOvL,GAAGjF,MAAM0P,EAAkB,UAAa,GAAEsC,OAGjDxB,EAAOvL,KACTuL,EAAOvL,GAAG8N,gBAAkBf,GAE9BnB,EAAgB7G,KAAKgI,GAEjBrL,EAAOgL,gBACTN,EAAgBA,EAAgBW,EAAY,EAAIV,EAAgB,EAAIF,EAC9C,IAAlBE,GAA6B,IAANrM,IACzBoM,EAAgBA,EAAgBrB,EAAa,EAAIoB,GACzC,IAANnM,IAASoM,EAAgBA,EAAgBrB,EAAa,EAAIoB,GAC1D9J,KAAK0L,IAAI3B,GAAiB,OAAUA,EAAgB,GACpD1K,EAAO+L,eAAcrB,EAAgB/J,KAAKwL,MAAMzB,IAChD9C,EAAQ5H,EAAOsM,gBAAmB,GAAGtC,EAAS3G,KAAKqH,GACvDT,EAAW5G,KAAKqH,KAEZ1K,EAAO+L,eAAcrB,EAAgB/J,KAAKwL,MAAMzB,KAEjD9C,EAAQjH,KAAKE,IAAIrB,EAAOQ,OAAOuM,mBAAoB3E,IAClDpI,EAAOQ,OAAOsM,gBAChB,GAEAtC,EAAS3G,KAAKqH,GAChBT,EAAW5G,KAAKqH,GAChBA,EAAgBA,EAAgBW,EAAYZ,GAG9CjL,EAAOoL,aAAeS,EAAYZ,EAElCE,EAAgBU,EAEhBzD,GAAS,CArFmD,CAsF9D,CAgBA,GAdApI,EAAOoL,YAAcjK,KAAKC,IAAIpB,EAAOoL,YAAavB,GAAcgB,EAE5Dd,GAAOC,IAA+B,UAAlBxJ,EAAOwM,QAAwC,cAAlBxM,EAAOwM,UAC1DtM,EAAU7G,MAAM0L,MAAS,GAAEvF,EAAOoL,YAAc5K,EAAOyK,kBAErDzK,EAAOyM,iBACTvM,EAAU7G,MAAM0P,EAAkB,UAAa,GAAEvJ,EAAOoL,YAAc5K,EAAOyK,kBAG3ES,GACF1L,EAAO2L,KAAKuB,kBAAkBrB,EAAWrB,EAAUjB,IAIhD/I,EAAOgL,eAAgB,CAC1B,MAAM2B,EAAgB,GACtB,IAAK,IAAIrO,EAAI,EAAGA,EAAI0L,EAAS3R,OAAQiG,GAAK,EAAG,CAC3C,IAAIsO,EAAiB5C,EAAS1L,GAC1B0B,EAAO+L,eAAca,EAAiBjM,KAAKwL,MAAMS,IACjD5C,EAAS1L,IAAMkB,EAAOoL,YAAcvB,GACtCsD,EAActJ,KAAKuJ,EAEvB,CACA5C,EAAW2C,EAGThM,KAAKwL,MAAM3M,EAAOoL,YAAcvB,GAAc1I,KAAKwL,MAAMnC,EAASA,EAAS3R,OAAS,IACpF,GAEA2R,EAAS3G,KAAK7D,EAAOoL,YAAcvB,EAEvC,CACA,GAAII,GAAazJ,EAAO6M,KAAM,CAC5B,MAAMnJ,EAAOwG,EAAgB,GAAKO,EAClC,GAAIzK,EAAOsM,eAAiB,EAAG,CAC7B,MAAMQ,EAASnM,KAAKoM,MACjBvN,EAAOkK,QAAQsD,aAAexN,EAAOkK,QAAQuD,aAAejN,EAAOsM,gBAEhEY,EAAYxJ,EAAO1D,EAAOsM,eAChC,IAAK,IAAIhO,EAAI,EAAGA,EAAIwO,EAAQxO,GAAK,EAC/B0L,EAAS3G,KAAK2G,EAASA,EAAS3R,OAAS,GAAK6U,EAElD,CACA,IAAK,IAAI5O,EAAI,EAAGA,EAAIkB,EAAOkK,QAAQsD,aAAexN,EAAOkK,QAAQuD,YAAa3O,GAAK,EACnD,IAA1B0B,EAAOsM,gBACTtC,EAAS3G,KAAK2G,EAASA,EAAS3R,OAAS,GAAKqL,GAEhDuG,EAAW5G,KAAK4G,EAAWA,EAAW5R,OAAS,GAAKqL,GACpDlE,EAAOoL,aAAelH,CAE1B,CAGA,GAFwB,IAApBsG,EAAS3R,SAAc2R,EAAW,CAAC,IAEX,IAAxBhK,EAAOyK,aAAoB,CAC7B,MAAMrS,EAAMoH,EAAOiJ,gBAAkBc,EAAM,aAAeR,EAAkB,eAC5Ec,EACGpL,QAAO,CAAC0O,EAAGC,MACLpN,EAAOiL,UAAWjL,EAAO6M,OAC1BO,IAAevD,EAAOxR,OAAS,IAKpCF,SAASkJ,IACRA,EAAQhI,MAAMjB,GAAQ,GAAEqS,KAAgB,GAE9C,CAEA,GAAIzK,EAAOgL,gBAAkBhL,EAAOqN,qBAAsB,CACxD,IAAIC,EAAgB,EACpBpD,EAAgB/R,SAASoV,IACvBD,GAAiBC,GAAkBvN,EAAOyK,aAAezK,EAAOyK,aAAe,EAAE,IAEnF6C,GAAiBtN,EAAOyK,aACxB,MAAM+C,EAAUF,EAAgBjE,EAChCW,EAAWA,EAASlN,KAAK2Q,GACnBA,EAAO,GAAWtD,EAClBsD,EAAOD,EAAgBA,EAAUnD,EAC9BoD,GAEX,CAEA,GAAIzN,EAAO0N,yBAA0B,CACnC,IAAIJ,EAAgB,EAKpB,GAJApD,EAAgB/R,SAASoV,IACvBD,GAAiBC,GAAkBvN,EAAOyK,aAAezK,EAAOyK,aAAe,EAAE,IAEnF6C,GAAiBtN,EAAOyK,aACpB6C,EAAgBjE,EAAY,CAC9B,MAAMsE,GAAmBtE,EAAaiE,GAAiB,EACvDtD,EAAS7R,SAAQ,CAACsV,EAAMG,KACtB5D,EAAS4D,GAAaH,EAAOE,CAAe,IAE9C1D,EAAW9R,SAAQ,CAACsV,EAAMG,KACxB3D,EAAW2D,GAAaH,EAAOE,CAAe,GAElD,CACF,CASA,GAPA7V,OAAO+Q,OAAOrJ,EAAQ,CACpBqK,SACAG,WACAC,aACAC,oBAGElK,EAAOgL,gBAAkBhL,EAAOiL,UAAYjL,EAAOqN,qBAAsB,CAC3EnO,EAAegB,EAAW,mCAAuC8J,EAAS,GAAZ,MAC9D9K,EACEgB,EACA,iCACGV,EAAOkE,KAAO,EAAIwG,EAAgBA,EAAgB7R,OAAS,GAAK,EAAlE,MAEH,MAAMwV,GAAiBrO,EAAOwK,SAAS,GACjC8D,GAAmBtO,EAAOyK,WAAW,GAC3CzK,EAAOwK,SAAWxK,EAAOwK,SAASlN,KAAKiR,GAAMA,EAAIF,IACjDrO,EAAOyK,WAAazK,EAAOyK,WAAWnN,KAAKiR,GAAMA,EAAID,GACvD,CAiBA,GAfI/D,IAAiBH,GACnBpK,EAAOuI,KAAK,sBAEViC,EAAS3R,SAAWkS,IAClB/K,EAAOQ,OAAOgO,eAAexO,EAAOyO,gBACxCzO,EAAOuI,KAAK,yBAEVkC,EAAW5R,SAAWmS,GACxBhL,EAAOuI,KAAK,0BAGV/H,EAAOkO,qBACT1O,EAAO2O,uBAGJ1E,GAAczJ,EAAOiL,SAA8B,UAAlBjL,EAAOwM,QAAwC,SAAlBxM,EAAOwM,QAAoB,CAC5F,MAAM4B,EAAuB,GAAEpO,EAAOqO,wCAChCC,EAA6B9O,EAAOrD,GAAG0F,UAAU0M,SAASH,GAC5DrE,GAAgB/J,EAAOwO,wBACpBF,GAA4B9O,EAAOrD,GAAG0F,UAAUC,IAAIsM,GAChDE,GACT9O,EAAOrD,GAAG0F,UAAU4M,OAAOL,EAE/B,CACF,EFnUEM,iBGba,SAA0BzO,GACvC,MAAMT,EAASzE,KACT4T,EAAe,GACflF,EAAYjK,EAAOkK,SAAWlK,EAAOQ,OAAO0J,QAAQC,QAC1D,IACIrL,EADAsQ,EAAY,EAEK,iBAAV3O,EACTT,EAAOqP,cAAc5O,IACF,IAAVA,GACTT,EAAOqP,cAAcrP,EAAOQ,OAAOC,OAGrC,MAAM6O,EAAmBlH,GACnB6B,EACKjK,EAAOqK,OAAOpL,QAClBtC,GAAOwM,SAASxM,EAAG4S,aAAa,2BAA4B,MAAQnH,IACrE,GAEGpI,EAAOqK,OAAOjC,GAGvB,GAAoC,SAAhCpI,EAAOQ,OAAOwL,eAA4BhM,EAAOQ,OAAOwL,cAAgB,EAC1E,GAAIhM,EAAOQ,OAAOgL,gBACfxL,EAAOwP,eAAiB,IAAI7W,SAASuT,IACpCiD,EAAatL,KAAKqI,EAAM,SAG1B,IAAKpN,EAAI,EAAGA,EAAIqC,KAAKoM,KAAKvN,EAAOQ,OAAOwL,eAAgBlN,GAAK,EAAG,CAC9D,MAAMsJ,EAAQpI,EAAOyP,YAAc3Q,EACnC,GAAIsJ,EAAQpI,EAAOqK,OAAOxR,SAAWoR,EAAW,MAChDkF,EAAatL,KAAKyL,EAAgBlH,GACpC,MAGF+G,EAAatL,KAAKyL,EAAgBtP,EAAOyP,cAI3C,IAAK3Q,EAAI,EAAGA,EAAIqQ,EAAatW,OAAQiG,GAAK,EACxC,QAA+B,IAApBqQ,EAAarQ,GAAoB,CAC1C,MAAM2G,EAAS0J,EAAarQ,GAAG4Q,aAC/BN,EAAY3J,EAAS2J,EAAY3J,EAAS2J,CAC5C,EAIEA,GAA2B,IAAdA,KAAiBpP,EAAOU,UAAU7G,MAAM4L,OAAU,GAAE2J,MACvE,EHjCET,mBIda,WACb,MAAM3O,EAASzE,KACT8O,EAASrK,EAAOqK,OAEhBsF,EAAc3P,EAAO4P,UACvB5P,EAAOiJ,eACLjJ,EAAOU,UAAUmP,WACjB7P,EAAOU,UAAUoP,UACnB,EACJ,IAAK,IAAIhR,EAAI,EAAGA,EAAIuL,EAAOxR,OAAQiG,GAAK,EACtCuL,EAAOvL,GAAGiR,mBACP/P,EAAOiJ,eAAiBoB,EAAOvL,GAAG+Q,WAAaxF,EAAOvL,GAAGgR,WAAaH,CAE7E,EJEEK,qBKfa,SAA8B5P,QAAS,IAATA,MAAa7E,MAAQA,KAAK6E,WAAc,GACnF,MAAMJ,EAASzE,KACTiF,EAASR,EAAOQ,QAEhB6J,OAAEA,EAAQP,aAAcC,EAAGS,SAAEA,GAAaxK,EAEhD,GAAsB,IAAlBqK,EAAOxR,OAAc,YACkB,IAAhCwR,EAAO,GAAG0F,mBAAmC/P,EAAO2O,qBAE/D,IAAIsB,GAAgB7P,EAChB2J,IAAKkG,EAAe7P,GAGxBiK,EAAO1R,SAASkJ,IACdA,EAAQQ,UAAU4M,OAAOzO,EAAO0P,kBAAkB,IAGpDlQ,EAAOmQ,qBAAuB,GAC9BnQ,EAAOwP,cAAgB,GAEvB,IAAK,IAAI1Q,EAAI,EAAGA,EAAIuL,EAAOxR,OAAQiG,GAAK,EAAG,CACzC,MAAMoN,EAAQ7B,EAAOvL,GACrB,IAAIsR,EAAclE,EAAM6D,kBACpBvP,EAAOiL,SAAWjL,EAAOgL,iBAC3B4E,GAAe/F,EAAO,GAAG0F,mBAG3B,MAAMM,GACHJ,GAAgBzP,EAAOgL,eAAiBxL,EAAOsQ,eAAiB,GAAKF,IACrElE,EAAMU,gBAAkBpM,EAAOyK,cAC5BsF,GACHN,EACCzF,EAAS,IACRhK,EAAOgL,eAAiBxL,EAAOsQ,eAAiB,GACjDF,IACDlE,EAAMU,gBAAkBpM,EAAOyK,cAC5BuF,IAAgBP,EAAeG,GAC/BK,EAAaD,EAAcxQ,EAAO0K,gBAAgB5L,IAErD0R,GAAe,GAAKA,EAAcxQ,EAAOkE,KAAO,GAChDuM,EAAa,GAAKA,GAAczQ,EAAOkE,MACvCsM,GAAe,GAAKC,GAAczQ,EAAOkE,QAE1ClE,EAAOwP,cAAc3L,KAAKqI,GAC1BlM,EAAOmQ,qBAAqBtM,KAAK/E,GACjCuL,EAAOvL,GAAGuD,UAAUC,IAAI9B,EAAO0P,oBAEjChE,EAAMhL,SAAW6I,GAAOsG,EAAgBA,EACxCnE,EAAMwE,iBAAmB3G,GAAOwG,EAAwBA,CAC1D,CACF,ELlCEI,eMhBa,SAAwBvQ,GACrC,MAAMJ,EAASzE,KACf,QAAyB,IAAd6E,EAA2B,CACpC,MAAMwQ,EAAa5Q,EAAO8J,cAAgB,EAAI,EAE9C1J,EAAaJ,GAAUA,EAAOI,WAAaJ,EAAOI,UAAYwQ,GAAe,CAC/E,CACA,MAAMpQ,EAASR,EAAOQ,OAChBqQ,EAAiB7Q,EAAO8Q,eAAiB9Q,EAAOsQ,eACtD,IAAIpP,SAAEA,EAAQ6P,YAAEA,EAAWC,MAAEA,EAAKC,aAAEA,GAAiBjR,EACrD,MAAMkR,EAAeH,EACfI,EAASH,EACf,GAAuB,IAAnBH,EACF3P,EAAW,EACX6P,GAAc,EACdC,GAAQ,MACH,CACL9P,GAAYd,EAAYJ,EAAOsQ,gBAAkBO,EACjD,MAAMO,EAAqBjQ,KAAK0L,IAAIzM,EAAYJ,EAAOsQ,gBAAkB,EACnEe,EAAelQ,KAAK0L,IAAIzM,EAAYJ,EAAO8Q,gBAAkB,EACnEC,EAAcK,GAAsBlQ,GAAY,EAChD8P,EAAQK,GAAgBnQ,GAAY,EAChCkQ,IAAoBlQ,EAAW,GAC/BmQ,IAAcnQ,EAAW,EAC/B,CAEA,GAAIV,EAAO6M,KAAM,CACf,MAAMiE,EAAkBtR,EAAOuR,cAC7BvR,EAAOqK,OAAOpL,QAAQtC,GAAsD,MAA/CA,EAAG4S,aAAa,6BAAoC,IAE7EiC,EAAiBxR,EAAOuR,cAC5BvR,EAAOqK,OAAOpL,QACXtC,GAAoD,EAA7CA,EAAG4S,aAAa,4BAAmCvP,EAAOqK,OAAOxR,OAAS,IAClF,IAEE4Y,EAAsBzR,EAAOyK,WAAW6G,GACxCI,EAAqB1R,EAAOyK,WAAW+G,GACvCG,EAAe3R,EAAOyK,WAAWzK,EAAOyK,WAAW5R,OAAS,GAC5D+Y,EAAezQ,KAAK0L,IAAIzM,GAE5B6Q,EADEW,GAAgBH,GACFG,EAAeH,GAAuBE,GAEtCC,EAAeD,EAAeD,GAAsBC,EAElEV,EAAe,IAAGA,GAAgB,EACxC,CAEA3Y,OAAO+Q,OAAOrJ,EAAQ,CACpBkB,WACA+P,eACAF,cACAC,WAGExQ,EAAOkO,qBAAwBlO,EAAOgL,gBAAkBhL,EAAOqR,aACjE7R,EAAOgQ,qBAAqB5P,GAE1B2Q,IAAgBG,GAClBlR,EAAOuI,KAAK,yBAEVyI,IAAUG,GACZnR,EAAOuI,KAAK,oBAET2I,IAAiBH,GAAiBI,IAAWH,IAChDhR,EAAOuI,KAAK,YAGdvI,EAAOuI,KAAK,WAAYrH,EAC1B,ENnDE4Q,oBOfa,WACb,MAAM9R,EAASzE,MAET8O,OAAEA,EAAM7J,OAAEA,EAAMoJ,SAAEA,EAAQ6F,YAAEA,GAAgBzP,EAC5CiK,EAAYjK,EAAOkK,SAAW1J,EAAO0J,QAAQC,QAE7C4H,EAAoB9P,GACjBF,EACL6H,EACC,IAAGpJ,EAAO8J,aAAarI,kBAAyBA,KACjD,GAMJ,IAAI+P,EACJ,GALA3H,EAAO1R,SAASkJ,IACdA,EAAQQ,UAAU4M,OAAOzO,EAAOyR,iBAAkBzR,EAAO0R,eAAgB1R,EAAO2R,eAAe,IAI7FlI,EACF,GAAIzJ,EAAO6M,KAAM,CACf,IAAIO,EAAa6B,EAAczP,EAAOkK,QAAQsD,aAC1CI,EAAa,IAAGA,EAAa5N,EAAOkK,QAAQG,OAAOxR,OAAS+U,GAC5DA,GAAc5N,EAAOkK,QAAQG,OAAOxR,SAAQ+U,GAAc5N,EAAOkK,QAAQG,OAAOxR,QACpFmZ,EAAcD,EAAkB,6BAA4BnE,MAC9D,MACEoE,EAAcD,EAAkB,6BAA4BtC,YAG9DuC,EAAc3H,EAAOoF,GAGvB,GAAIuC,EAAa,CAEfA,EAAY3P,UAAUC,IAAI9B,EAAOyR,kBAGjC,IAAIG,EZwMR,SAAwBzV,EAAIsF,GAC1B,MAAMoQ,EAAU,GAChB,KAAO1V,EAAG2V,oBAAoB,CAC5B,MAAMC,EAAO5V,EAAG2V,mBACZrQ,EACEsQ,EAAKrQ,QAAQD,IAAWoQ,EAAQxO,KAAK0O,GACpCF,EAAQxO,KAAK0O,GACpB5V,EAAK4V,CACP,CACA,OAAOF,CACT,CYlNoBG,CAAeR,EAAc,IAAGxR,EAAO8J,4BAA4B,GAC/E9J,EAAO6M,OAAS+E,IAClBA,EAAY/H,EAAO,IAEjB+H,GACFA,EAAU/P,UAAUC,IAAI9B,EAAO0R,gBAGjC,IAAIO,EZqLR,SAAwB9V,EAAIsF,GAC1B,MAAMyQ,EAAU,GAChB,KAAO/V,EAAGgW,wBAAwB,CAChC,MAAMC,EAAOjW,EAAGgW,uBACZ1Q,EACE2Q,EAAK1Q,QAAQD,IAAWyQ,EAAQ7O,KAAK+O,GACpCF,EAAQ7O,KAAK+O,GACpBjW,EAAKiW,CACP,CACA,OAAOF,CACT,CY/LoBG,CAAeb,EAAc,IAAGxR,EAAO8J,4BAA4B,GAC/E9J,EAAO6M,MAAuB,KAAdoF,IAClBA,EAAYpI,EAAOA,EAAOxR,OAAS,IAEjC4Z,GACFA,EAAUpQ,UAAUC,IAAI9B,EAAO2R,eAEnC,CAEAnS,EAAO8S,mBACT,EPrCEC,kBQMa,SAA2BC,GACxC,MAAMhT,EAASzE,KACT6E,EAAYJ,EAAO8J,aAAe9J,EAAOI,WAAaJ,EAAOI,WAC7DoK,SACJA,EAAQhK,OACRA,EACAiP,YAAawD,EACbC,UAAWC,EACX/E,UAAWgF,GACTpT,EACJ,IACIoO,EADAqB,EAAcuD,EAGlB,MAAMK,EAAuBC,IAC3B,IAAIJ,EAAYI,EAAStT,EAAOkK,QAAQsD,aAOxC,OANI0F,EAAY,IACdA,EAAYlT,EAAOkK,QAAQG,OAAOxR,OAASqa,GAEzCA,GAAalT,EAAOkK,QAAQG,OAAOxR,SACrCqa,GAAalT,EAAOkK,QAAQG,OAAOxR,QAE9Bqa,CAAS,EAKlB,QAH2B,IAAhBzD,IACTA,EAhDG,SAAmCzP,GACxC,MAAMyK,WAAEA,EAAUjK,OAAEA,GAAWR,EACzBI,EAAYJ,EAAO8J,aAAe9J,EAAOI,WAAaJ,EAAOI,UACnE,IAAIqP,EACJ,IAAK,IAAI3Q,EAAI,EAAGA,EAAI2L,EAAW5R,OAAQiG,GAAK,OACT,IAAtB2L,EAAW3L,EAAI,GAEtBsB,GAAaqK,EAAW3L,IACxBsB,EAAYqK,EAAW3L,EAAI,IAAM2L,EAAW3L,EAAI,GAAK2L,EAAW3L,IAAM,EAEtE2Q,EAAc3Q,EACLsB,GAAaqK,EAAW3L,IAAMsB,EAAYqK,EAAW3L,EAAI,KAClE2Q,EAAc3Q,EAAI,GAEXsB,GAAaqK,EAAW3L,KACjC2Q,EAAc3Q,GAOlB,OAHI0B,EAAO+S,sBACL9D,EAAc,QAA4B,IAAhBA,KAA6BA,EAAc,GAEpEA,CACT,CAyBkB+D,CAA0BxT,IAEtCwK,EAAStL,QAAQkB,IAAc,EACjCgO,EAAY5D,EAAStL,QAAQkB,OACxB,CACL,MAAMqT,EAAOtS,KAAKE,IAAIb,EAAOuM,mBAAoB0C,GACjDrB,EAAYqF,EAAOtS,KAAKwL,OAAO8C,EAAcgE,GAAQjT,EAAOsM,eAC9D,CAEA,GADIsB,GAAa5D,EAAS3R,SAAQuV,EAAY5D,EAAS3R,OAAS,GAC5D4W,IAAgBwD,EAQlB,OAPI7E,IAAcgF,IAChBpT,EAAOoO,UAAYA,EACnBpO,EAAOuI,KAAK,yBAEVvI,EAAOQ,OAAO6M,MAAQrN,EAAOkK,SAAWlK,EAAOQ,OAAO0J,QAAQC,UAChEnK,EAAOkT,UAAYG,EAAoB5D,KAK3C,IAAIyD,EAEFA,EADElT,EAAOkK,SAAW1J,EAAO0J,QAAQC,SAAW3J,EAAO6M,KACzCgG,EAAoB5D,GACvBzP,EAAOqK,OAAOoF,GACXtG,SACVnJ,EAAOqK,OAAOoF,GAAaF,aAAa,4BAA8BE,EACtE,IAGUA,EAGdnX,OAAO+Q,OAAOrJ,EAAQ,CACpBoO,YACA8E,YACAD,gBACAxD,gBAEFzP,EAAOuI,KAAK,qBACZvI,EAAOuI,KAAK,mBACR4K,IAAsBD,GACxBlT,EAAOuI,KAAK,oBAEVvI,EAAO0T,aAAe1T,EAAOQ,OAAOmT,qBACtC3T,EAAOuI,KAAK,cAEhB,ER3EEqL,mBSnBa,SAA4B5P,GACzC,MAAMhE,EAASzE,KACTiF,EAASR,EAAOQ,OAChB0L,EAAQlI,EAAE6P,QAAS,IAAGrT,EAAO8J,4BACnC,IACIsD,EADAkG,GAAa,EAGjB,GAAI5H,EACF,IAAK,IAAIpN,EAAI,EAAGA,EAAIkB,EAAOqK,OAAOxR,OAAQiG,GAAK,EAC7C,GAAIkB,EAAOqK,OAAOvL,KAAOoN,EAAO,CAC9B4H,GAAa,EACblG,EAAa9O,EACb,KACF,CAIJ,IAAIoN,IAAS4H,EAUX,OAFA9T,EAAO+T,kBAAenV,OACtBoB,EAAOgU,kBAAepV,GARtBoB,EAAO+T,aAAe7H,EAClBlM,EAAOkK,SAAWlK,EAAOQ,OAAO0J,QAAQC,QAC1CnK,EAAOgU,aAAe7K,SAAS+C,EAAMqD,aAAa,2BAA4B,IAE9EvP,EAAOgU,aAAepG,EAQxBpN,EAAOyT,0BACiBrV,IAAxBoB,EAAOgU,cACPhU,EAAOgU,eAAiBhU,EAAOyP,aAE/BzP,EAAOiU,qBAEX,GC9BA,IAAA7T,EAAe,C,aCJA,SAA4BxD,QAAI,IAAJA,MAAOrB,KAAK0N,eAAiB,IAAM,KAC5E,MAEMzI,OAAEA,EAAQsJ,aAAcC,EAAG3J,UAAEA,EAASM,UAAEA,GAF/BnF,KAIf,GAAIiF,EAAO0T,iBACT,OAAOnK,GAAO3J,EAAYA,EAE5B,GAAII,EAAOiL,QACT,OAAOrL,EAGT,IAAI+T,EAAmBzX,EAAagE,EAAW9D,GAG/C,OAFImN,IAAKoK,GAAoBA,GAEtBA,GAAoB,CAC7B,EDVEC,aERa,SAAsBhU,EAAWiU,GAC9C,MAAMrU,EAASzE,MACPuO,aAAcC,EAAGvJ,OAAEA,EAAME,UAAEA,EAASQ,SAAEA,GAAalB,EAC3D,IAwBIsU,EAxBAC,EAAI,EACJC,EAAI,EAGJxU,EAAOiJ,eACTsL,EAAIxK,GAAO3J,EAAYA,EAEvBoU,EAAIpU,EAGFI,EAAO+L,eACTgI,EAAIpT,KAAKwL,MAAM4H,GACfC,EAAIrT,KAAKwL,MAAM6H,IAGbhU,EAAOiL,QACT/K,EAAUV,EAAOiJ,eAAiB,aAAe,aAAejJ,EAAOiJ,gBAAkBsL,GAAKC,EACpFhU,EAAO0T,mBACjBxT,EAAU7G,MAAMsD,UAAa,eAAcoX,QAAQC,aAErDxU,EAAOyU,kBAAoBzU,EAAOI,UAClCJ,EAAOI,UAAYJ,EAAOiJ,eAAiBsL,EAAIC,EAI/C,MAAM3D,EAAiB7Q,EAAO8Q,eAAiB9Q,EAAOsQ,eAEpDgE,EADqB,IAAnBzD,EACY,GAECzQ,EAAYJ,EAAOsQ,gBAAkBO,EAElDyD,IAAgBpT,GAClBlB,EAAO2Q,eAAevQ,GAGxBJ,EAAOuI,KAAK,eAAgBvI,EAAOI,UAAWiU,EAChD,EF9BE/D,aGTa,WACb,OAAQ/U,KAAKiP,SAAS,EACxB,EHQEsG,aIVa,WACb,OAAQvV,KAAKiP,SAASjP,KAAKiP,SAAS3R,OAAS,EAC/C,EJSE6b,YKTa,SACbtU,EACAK,EACAkU,EACAC,EACAC,QAJS,IAATzU,MAAY,QACP,IAALK,MAAQlF,KAAKiF,OAAOC,YACR,IAAZkU,OAAe,QACA,IAAfC,OAAkB,GAGlB,MAAM5U,EAASzE,MAETiF,OAAEA,EAAME,UAAEA,GAAcV,EAE9B,GAAIA,EAAO8U,WAAatU,EAAOuU,+BAC7B,OAAO,EAGT,MAAMzE,EAAetQ,EAAOsQ,eACtBQ,EAAe9Q,EAAO8Q,eAC5B,IAAIkE,EAQJ,GAPiDA,EAA7CJ,GAAmBxU,EAAYkQ,EAA6BA,EACvDsE,GAAmBxU,EAAY0Q,EAA6BA,EACjD1Q,EAGpBJ,EAAO2Q,eAAeqE,GAElBxU,EAAOiL,QAAS,CAClB,MAAMwJ,EAAMjV,EAAOiJ,eACnB,GAAc,IAAVxI,EACFC,EAAUuU,EAAM,aAAe,cAAgBD,MAC1C,CACL,IAAKhV,EAAOqE,QAAQI,aAElB,OADA3E,EAAqB,CAAEE,SAAQC,gBAAiB+U,EAAc9U,KAAM+U,EAAM,OAAS,SAC5E,EAETvU,EAAUgB,SAAS,CACjB,CAACuT,EAAM,OAAS,QAASD,EACzBE,SAAU,UAEd,CACA,OAAO,CACT,CAqCA,OAnCc,IAAVzU,GACFT,EAAOqP,cAAc,GACrBrP,EAAOoU,aAAaY,GAChBL,IACF3U,EAAOuI,KAAK,wBAAyB9H,EAAOoU,GAC5C7U,EAAOuI,KAAK,oBAGdvI,EAAOqP,cAAc5O,GACrBT,EAAOoU,aAAaY,GAChBL,IACF3U,EAAOuI,KAAK,wBAAyB9H,EAAOoU,GAC5C7U,EAAOuI,KAAK,oBAETvI,EAAO8U,YACV9U,EAAO8U,WAAY,EACd9U,EAAOmV,oCACVnV,EAAOmV,kCAAoC,SAAuBnR,GAC3DhE,IAAUA,EAAOsH,WAClBtD,EAAExL,SAAW+C,OACjByE,EAAOU,UAAUzH,oBACf,gBACA+G,EAAOmV,mCAETnV,EAAOmV,kCAAoC,YACpCnV,EAAOmV,kCACVR,GACF3U,EAAOuI,KAAK,iB,GAIlBvI,EAAOU,UAAU1H,iBAAiB,gBAAiBgH,EAAOmV,sCAIvD,CACT,GChFe,SAASC,EAA0DrV,GAAA,IAA3CC,OAAEA,EAAM2U,aAAEA,EAAYU,UAAEA,EAASC,KAAEA,GAAMvV,EAC9E,MAAM0P,YAAEA,EAAWwD,cAAEA,GAAkBjT,EACvC,IAAIa,EAAMwU,EASV,GARKxU,IAC8BA,EAA7B4O,EAAcwD,EAAqB,OAC9BxD,EAAcwD,EAAqB,OACjC,SAGbjT,EAAOuI,KAAM,aAAY+M,KAErBX,GAAgBlF,IAAgBwD,EAAe,CACjD,GAAY,UAARpS,EAEF,YADAb,EAAOuI,KAAM,uBAAsB+M,KAGrCtV,EAAOuI,KAAM,wBAAuB+M,KACxB,SAARzU,EACFb,EAAOuI,KAAM,sBAAqB+M,KAElCtV,EAAOuI,KAAM,sBAAqB+M,IAEtC,CACF,CCfA,IAAApJ,EAAe,CACbqJ,QCPa,SACbnN,EACA3H,EACAkU,EACAE,EACAW,QAJK,IAALpN,MAAQ,QACH,IAAL3H,MAAQlF,KAAKiF,OAAOC,YACR,IAAZkU,OAAe,GAIM,iBAAVvM,IACTA,EAAQe,SAASf,EAAO,KAG1B,MAAMpI,EAASzE,KACf,IAAIqS,EAAaxF,EACbwF,EAAa,IAAGA,EAAa,GAEjC,MAAMpN,OACJA,EAAMgK,SACNA,EAAQC,WACRA,EAAUwI,cACVA,EAAaxD,YACbA,EACA3F,aAAcC,EAAGrJ,UACjBA,EAASyJ,QACTA,GACEnK,EAEJ,GACGA,EAAO8U,WAAatU,EAAOuU,iCAC1B5K,IAAY0K,IAAaW,EAE3B,OAAO,EAGT,MAAM/B,EAAOtS,KAAKE,IAAIrB,EAAOQ,OAAOuM,mBAAoBa,GACxD,IAAIQ,EAAYqF,EAAOtS,KAAKwL,OAAOiB,EAAa6F,GAAQzT,EAAOQ,OAAOsM,gBAClEsB,GAAa5D,EAAS3R,SAAQuV,EAAY5D,EAAS3R,OAAS,GAEhE,MAAMuH,GAAaoK,EAAS4D,GAE5B,GAAI5N,EAAO+S,oBACT,IAAK,IAAIzU,EAAI,EAAGA,EAAI2L,EAAW5R,OAAQiG,GAAK,EAAG,CAC7C,MAAM2W,GAAuBtU,KAAKwL,MAAkB,IAAZvM,GAClCsV,EAAiBvU,KAAKwL,MAAsB,IAAhBlC,EAAW3L,IACvC6W,EAAqBxU,KAAKwL,MAA0B,IAApBlC,EAAW3L,EAAI,SACpB,IAAtB2L,EAAW3L,EAAI,GAEtB2W,GAAuBC,GACvBD,EAAsBE,GAAsBA,EAAqBD,GAAkB,EAEnF9H,EAAa9O,EAEb2W,GAAuBC,GACvBD,EAAsBE,IAEtB/H,EAAa9O,EAAI,GAEV2W,GAAuBC,IAChC9H,EAAa9O,EAEjB,CAGF,GAAIkB,EAAO0T,aAAe9F,IAAe6B,EAAa,CACpD,IACGzP,EAAO4V,gBACRxV,EAAYJ,EAAOI,WACnBA,EAAYJ,EAAOsQ,eAEnB,OAAO,EAET,IACGtQ,EAAO6V,gBACRzV,EAAYJ,EAAOI,WACnBA,EAAYJ,EAAO8Q,iBAEdrB,GAAe,KAAO7B,EACzB,OAAO,CAGb,CASA,IAAIyH,EAMJ,GAbIzH,KAAgBqF,GAAiB,IAAM0B,GACzC3U,EAAOuI,KAAK,0BAIdvI,EAAO2Q,eAAevQ,GAGQiV,EAA1BzH,EAAa6B,EAAyB,OACjC7B,EAAa6B,EAAyB,OAC9B,QAGZ1F,IAAQ3J,IAAcJ,EAAOI,YAAgB2J,GAAO3J,IAAcJ,EAAOI,UAc5E,OAbAJ,EAAO+S,kBAAkBnF,GAErBpN,EAAOqR,YACT7R,EAAOkP,mBAETlP,EAAO8R,sBACe,UAAlBtR,EAAOwM,QACThN,EAAOoU,aAAahU,GAEJ,UAAdiV,IACFrV,EAAO8V,gBAAgBnB,EAAcU,GACrCrV,EAAO+V,cAAcpB,EAAcU,KAE9B,EAET,GAAI7U,EAAOiL,QAAS,CAClB,MAAMwJ,EAAMjV,EAAOiJ,eACb+M,EAAIjM,EAAM3J,GAAaA,EAC7B,GAAc,IAAVK,EAAa,CACf,MAAMwJ,EAAYjK,EAAOkK,SAAWlK,EAAOQ,OAAO0J,QAAQC,QACtDF,IACFjK,EAAOU,UAAU7G,MAAM8G,eAAiB,OACxCX,EAAOiW,mBAAoB,GAGzBhM,IAAcjK,EAAOkW,2BAA6BlW,EAAOQ,OAAO2V,aAAe,GACjFnW,EAAOkW,2BAA4B,EACnCla,uBAAsB,KACpB0E,EAAUuU,EAAM,aAAe,aAAee,CAAC,KAGjDtV,EAAUuU,EAAM,aAAe,aAAee,EAE5C/L,GACFjO,uBAAsB,KACpBgE,EAAOU,UAAU7G,MAAM8G,eAAiB,GACxCX,EAAOiW,mBAAoB,CAAK,GAGtC,KAAO,CACL,IAAKjW,EAAOqE,QAAQI,aAElB,OADA3E,EAAqB,CAAEE,SAAQC,eAAgB+V,EAAG9V,KAAM+U,EAAM,OAAS,SAChE,EAETvU,EAAUgB,SAAS,CACjB,CAACuT,EAAM,OAAS,OAAQe,EACxBd,SAAU,UAEd,CACA,OAAO,CACT,CAyBA,OAxBAlV,EAAOqP,cAAc5O,GACrBT,EAAOoU,aAAahU,GACpBJ,EAAO+S,kBAAkBnF,GACzB5N,EAAO8R,sBACP9R,EAAOuI,KAAK,wBAAyB9H,EAAOoU,GAC5C7U,EAAO8V,gBAAgBnB,EAAcU,GAEvB,IAAV5U,EACFT,EAAO+V,cAAcpB,EAAcU,GACzBrV,EAAO8U,YACjB9U,EAAO8U,WAAY,EACd9U,EAAOoW,gCACVpW,EAAOoW,8BAAgC,SAAuBpS,GACvDhE,IAAUA,EAAOsH,WAClBtD,EAAExL,SAAW+C,OACjByE,EAAOU,UAAUzH,oBAAoB,gBAAiB+G,EAAOoW,+BAC7DpW,EAAOoW,8BAAgC,YAChCpW,EAAOoW,8BACdpW,EAAO+V,cAAcpB,EAAcU,G,GAGvCrV,EAAOU,UAAU1H,iBAAiB,gBAAiBgH,EAAOoW,iCAGrD,CACT,EDnKEC,YEVa,SACbjO,EACA3H,EACAkU,EACAE,GAEA,QALK,IAALzM,MAAQ,QACH,IAAL3H,MAAQlF,KAAKiF,OAAOC,YACR,IAAZkU,OAAe,GAGM,iBAAVvM,EAAoB,CAG7BA,EAFsBe,SAASf,EAAO,GAGxC,CAEA,MAAMpI,EAASzE,KACf,IAAI+a,EAAWlO,EAcf,OAbIpI,EAAOQ,OAAO6M,OACZrN,EAAOkK,SAAWlK,EAAOQ,OAAO0J,QAAQC,QAE1CmM,GAAsBtW,EAAOkK,QAAQsD,aAErC8I,EAAWtW,EAAOuR,cAChBvR,EAAOqK,OAAOpL,QACX4C,GAA8D,EAAlDA,EAAQ0N,aAAa,6BAAmC+G,IACrE,KAKDtW,EAAOuV,QAAQe,EAAU7V,EAAOkU,EAAcE,EACvD,EFjBE0B,UGVa,SAAmB9V,EAA2BkU,EAAqBE,QAA3C,IAALpU,MAAQlF,KAAKiF,OAAOC,YAAmB,IAAZkU,OAAe,GAC1E,MAAM3U,EAASzE,MACT4O,QAAEA,EAAO3J,OAAEA,EAAMsU,UAAEA,GAAc9U,EACvC,IAAKmK,EAAS,OAAOnK,EACrB,IAAIwW,EAAWhW,EAAOsM,eACO,SAAzBtM,EAAOwL,eAAsD,IAA1BxL,EAAOsM,gBAAwBtM,EAAOiW,qBAC3ED,EAAWrV,KAAKC,IAAIpB,EAAO0W,qBAAqB,WAAW,GAAO,IAEpE,MAAMC,EAAY3W,EAAOyP,YAAcjP,EAAOuM,mBAAqB,EAAIyJ,EACjEvM,EAAYjK,EAAOkK,SAAW1J,EAAO0J,QAAQC,QACnD,GAAI3J,EAAO6M,KAAM,CACf,GAAIyH,IAAc7K,GAAazJ,EAAOoW,oBAAqB,OAAO,EAClE5W,EAAO6W,QAAQ,CAAExB,UAAW,SAE5BrV,EAAO8W,YAAc9W,EAAOU,UAAUmC,UACxC,CACA,OAAIrC,EAAOuW,QAAU/W,EAAOgR,MACnBhR,EAAOuV,QAAQ,EAAG9U,EAAOkU,EAAcE,GAEzC7U,EAAOuV,QAAQvV,EAAOyP,YAAckH,EAAWlW,EAAOkU,EAAcE,EAC7E,EHTEmC,UIXa,SAAmBvW,EAA2BkU,EAAqBE,QAA3C,IAALpU,MAAQlF,KAAKiF,OAAOC,YAAmB,IAAZkU,OAAe,GAC1E,MAAM3U,EAASzE,MACTiF,OAAEA,EAAMgK,SAAEA,EAAQC,WAAEA,EAAUX,aAAEA,EAAYK,QAAEA,EAAO2K,UAAEA,GAAc9U,EAC3E,IAAKmK,EAAS,OAAOnK,EACrB,MAAMiK,EAAYjK,EAAOkK,SAAW1J,EAAO0J,QAAQC,QAEnD,GAAI3J,EAAO6M,KAAM,CACf,GAAIyH,IAAc7K,GAAazJ,EAAOoW,oBAAqB,OAAO,EAElE5W,EAAO6W,QAAQ,CAAExB,UAAW,SAE5BrV,EAAO8W,YAAc9W,EAAOU,UAAUmC,UACxC,CAGA,SAASoU,EAAUC,GACjB,OAAIA,EAAM,GAAW/V,KAAKwL,MAAMxL,KAAK0L,IAAIqK,IAClC/V,KAAKwL,MAAMuK,EACpB,CACA,MAAMzB,EAAsBwB,EANVnN,EAAe9J,EAAOI,WAAaJ,EAAOI,WAOtD+W,EAAqB3M,EAASlN,KAAK4Z,GAAQD,EAAUC,KAE3D,IAAIE,EAAW5M,EAAS2M,EAAmBjY,QAAQuW,GAAuB,GAC1E,QAAwB,IAAb2B,GAA4B5W,EAAOiL,QAAS,CACrD,IAAI4L,EACJ7M,EAAS7R,SAAQ,CAACsV,EAAMG,KAClBqH,GAAuBxH,IAEzBoJ,EAAgBjJ,EAClB,SAE2B,IAAlBiJ,IACTD,EAAW5M,EAAS6M,EAAgB,EAAIA,EAAgB,EAAIA,GAEhE,CACA,IAAIC,EAAY,EAahB,QAZwB,IAAbF,IACTE,EAAY7M,EAAWvL,QAAQkY,GAC3BE,EAAY,IAAGA,EAAYtX,EAAOyP,YAAc,GAEzB,SAAzBjP,EAAOwL,eACmB,IAA1BxL,EAAOsM,gBACPtM,EAAOiW,qBAEPa,EAAYA,EAAYtX,EAAO0W,qBAAqB,YAAY,GAAQ,EACxEY,EAAYnW,KAAKC,IAAIkW,EAAW,KAGhC9W,EAAOuW,QAAU/W,EAAO+Q,YAAa,CACvC,MAAMwG,EACJvX,EAAOQ,OAAO0J,SAAWlK,EAAOQ,OAAO0J,QAAQC,SAAWnK,EAAOkK,QAC7DlK,EAAOkK,QAAQG,OAAOxR,OAAS,EAC/BmH,EAAOqK,OAAOxR,OAAS,EAC7B,OAAOmH,EAAOuV,QAAQgC,EAAW9W,EAAOkU,EAAcE,EACxD,CACA,OAAO7U,EAAOuV,QAAQ+B,EAAW7W,EAAOkU,EAAcE,EACxD,EJ5CE2C,WKZa,SAAoB/W,EAA2BkU,EAAqBE,GAEjF,YAFsC,IAALpU,MAAQlF,KAAKiF,OAAOC,YAAmB,IAAZkU,OAAe,GAC5DpZ,KACDga,QADCha,KACckU,YAAahP,EAAOkU,EAAcE,EACjE,ELUE4C,eMba,SACbhX,EACAkU,EACAE,EACA6C,QAHK,IAALjX,MAAQlF,KAAKiF,OAAOC,YACR,IAAZkU,OAAe,QAEN,IAAT+C,MAAY,IAEZ,MAAM1X,EAASzE,KACf,IAAI6M,EAAQpI,EAAOyP,YACnB,MAAMgE,EAAOtS,KAAKE,IAAIrB,EAAOQ,OAAOuM,mBAAoB3E,GAClDgG,EAAYqF,EAAOtS,KAAKwL,OAAOvE,EAAQqL,GAAQzT,EAAOQ,OAAOsM,gBAE7D1M,EAAYJ,EAAO8J,aAAe9J,EAAOI,WAAaJ,EAAOI,UAEnE,GAAIA,GAAaJ,EAAOwK,SAAS4D,GAAY,CAG3C,MAAMuJ,EAAc3X,EAAOwK,SAAS4D,GAEhChO,EAAYuX,GADC3X,EAAOwK,SAAS4D,EAAY,GACHuJ,GAAeD,IACvDtP,GAASpI,EAAOQ,OAAOsM,eAE3B,KAAO,CAGL,MAAMsK,EAAWpX,EAAOwK,SAAS4D,EAAY,GAEzChO,EAAYgX,IADIpX,EAAOwK,SAAS4D,GACOgJ,GAAYM,IACrDtP,GAASpI,EAAOQ,OAAOsM,eAE3B,CAIA,OAHA1E,EAAQjH,KAAKC,IAAIgH,EAAO,GACxBA,EAAQjH,KAAKE,IAAI+G,EAAOpI,EAAOyK,WAAW5R,OAAS,GAE5CmH,EAAOuV,QAAQnN,EAAO3H,EAAOkU,EAAcE,EACpD,ENpBEZ,oBOba,WACb,MAAMjU,EAASzE,MACTiF,OAAEA,EAAMoJ,SAAEA,GAAa5J,EAEvBgM,EACqB,SAAzBxL,EAAOwL,cAA2BhM,EAAO0W,uBAAyBlW,EAAOwL,cAC3E,IACIkH,EADA0E,EAAe5X,EAAOgU,aAE1B,MAAM6D,EAAgB7X,EAAO4P,UAAa,eAAiB,IAAGpP,EAAO8J,aACrE,GAAI9J,EAAO6M,KAAM,CACf,GAAIrN,EAAO8U,UAAW,OACtB5B,EAAY/J,SAASnJ,EAAO+T,aAAaxE,aAAa,2BAA4B,IAC9E/O,EAAOgL,eAEPoM,EAAe5X,EAAO8X,aAAe9L,EAAgB,GACrD4L,EAAe5X,EAAOqK,OAAOxR,OAASmH,EAAO8X,aAAe9L,EAAgB,GAE5EhM,EAAO6W,UACPe,EAAe5X,EAAOuR,cACpBxP,EAAgB6H,EAAW,GAAEiO,8BAA0C3E,OAAe,IAGxF3W,GAAS,KACPyD,EAAOuV,QAAQqC,EAAa,KAG9B5X,EAAOuV,QAAQqC,GAERA,EAAe5X,EAAOqK,OAAOxR,OAASmT,GAC/ChM,EAAO6W,UACPe,EAAe5X,EAAOuR,cACpBxP,EAAgB6H,EAAW,GAAEiO,8BAA0C3E,OAAe,IAGxF3W,GAAS,KACPyD,EAAOuV,QAAQqC,EAAa,KAG9B5X,EAAOuV,QAAQqC,EAEnB,MACE5X,EAAOuV,QAAQqC,EAEnB,GCzCA,IAAAvK,EAAe,CACb0K,WCHa,SAAoBC,GACjC,MAAMhY,EAASzE,MACTiF,OAAEA,EAAMoJ,SAAEA,GAAa5J,EAC7B,IAAKQ,EAAO6M,MAASrN,EAAOkK,SAAWlK,EAAOQ,OAAO0J,QAAQC,QAAU,OAExDpI,EAAgB6H,EAAW,IAAGpJ,EAAO8J,4BAE7C3R,SAAQ,CAACgE,EAAIyL,KAClBzL,EAAG7C,aAAa,0BAA2BsO,EAAM,IAGnDpI,EAAO6W,QAAQ,CAAEmB,iBAAgB3C,UAAW7U,EAAOgL,oBAAiB5M,EAAY,QAClF,EDREiY,QENa,SAQP7R,GAAA,IARwBgT,eAC9BA,EAAczC,QACdA,GAAU,EAAIF,UACdA,EAASjB,aACTA,EAAY6D,iBACZA,EAAgB5D,aAChBA,EAAY6D,aACZA,QACD,IAAAlT,EAAG,GAAEA,EACJ,MAAMhF,EAASzE,KACf,IAAKyE,EAAOQ,OAAO6M,KAAM,OACzBrN,EAAOuI,KAAK,iBAEZ,MAAM8B,OAAEA,EAAMwL,eAAEA,EAAcD,eAAEA,EAAchM,SAAEA,EAAQpJ,OAAEA,GAAWR,EAKrE,GAHAA,EAAO6V,gBAAiB,EACxB7V,EAAO4V,gBAAiB,EAEpB5V,EAAOkK,SAAW1J,EAAO0J,QAAQC,QAanC,OAZIoL,IACG/U,EAAOgL,gBAAuC,IAArBxL,EAAOoO,UAE1B5N,EAAOgL,gBAAkBxL,EAAOoO,UAAY5N,EAAOwL,cAC5DhM,EAAOuV,QAAQvV,EAAOkK,QAAQG,OAAOxR,OAASmH,EAAOoO,UAAW,GAAG,GAAO,GACjEpO,EAAOoO,YAAcpO,EAAOwK,SAAS3R,OAAS,GACvDmH,EAAOuV,QAAQvV,EAAOkK,QAAQsD,aAAc,GAAG,GAAO,GAJtDxN,EAAOuV,QAAQvV,EAAOkK,QAAQG,OAAOxR,OAAQ,GAAG,GAAO,IAO3DmH,EAAO6V,eAAiBA,EACxB7V,EAAO4V,eAAiBA,OACxB5V,EAAOuI,KAAK,WAId,MAAMyD,EACqB,SAAzBxL,EAAOwL,cACHhM,EAAO0W,uBACPvV,KAAKoM,KAAKvP,WAAWwC,EAAOwL,cAAe,KACjD,IAAI8L,EAAetX,EAAOsX,cAAgB9L,EACtC8L,EAAetX,EAAOsM,gBAAmB,IAC3CgL,GAAgBtX,EAAOsM,eAAkBgL,EAAetX,EAAOsM,gBAEjE9M,EAAO8X,aAAeA,EAEtB,MAAMK,EAAuB,GACvBC,EAAsB,GAE5B,IAAI3I,EAAczP,EAAOyP,iBAEO,IAArBwI,EACTA,EAAmBjY,EAAOuR,cACxBvR,EAAOqK,OAAOpL,QAAQtC,GAAOA,EAAG0F,UAAU0M,SAAS,yBAAwB,IAG7EU,EAAcwI,EAGhB,MAAMI,EAAuB,SAAdhD,IAAyBA,EAClCiD,EAAuB,SAAdjD,IAAyBA,EAExC,IAAIkD,EAAkB,EAClBC,EAAiB,EAErB,GAAIP,EAAmBH,EAAc,CACnCS,EAAkBpX,KAAKC,IAAI0W,EAAeG,EAAkBzX,EAAOsM,gBACnE,IAAK,IAAIhO,EAAI,EAAGA,EAAIgZ,EAAeG,EAAkBnZ,GAAK,EAAG,CAC3D,MAAMsJ,EAAQtJ,EAAIqC,KAAKwL,MAAM7N,EAAIuL,EAAOxR,QAAUwR,EAAOxR,OACzDsf,EAAqBtU,KAAKwG,EAAOxR,OAASuP,EAAQ,EACpD,CACF,MAAO,GAAI6P,EAAyCjY,EAAOqK,OAAOxR,OAAwB,EAAfif,EAAkB,CAC3FU,EAAiBrX,KAAKC,IACpB6W,GAAoBjY,EAAOqK,OAAOxR,OAAwB,EAAfif,GAC3CtX,EAAOsM,gBAET,IAAK,IAAIhO,EAAI,EAAGA,EAAI0Z,EAAgB1Z,GAAK,EAAG,CAC1C,MAAMsJ,EAAQtJ,EAAIqC,KAAKwL,MAAM7N,EAAIuL,EAAOxR,QAAUwR,EAAOxR,OACzDuf,EAAoBvU,KAAKuE,EAC3B,CACF,CAkBA,GAhBIkQ,GACFH,EAAqBxf,SAASyP,IAC5BwB,EAAS6O,QAAQzY,EAAOqK,OAAOjC,GAAO,IAGtCiQ,GACFD,EAAoBzf,SAASyP,IAC3BwB,EAAS8O,OAAO1Y,EAAOqK,OAAOjC,GAAO,IAIzCpI,EAAO2Y,eACHnY,EAAOkO,qBACT1O,EAAO2O,qBAGL4G,EACF,GAAI4C,EAAqBtf,OAAS,GAAKyf,EACrC,QAA8B,IAAnBN,EAAgC,CACzC,MAAMY,EAAwB5Y,EAAOyK,WAAWgF,GAE1CoJ,EADoB7Y,EAAOyK,WAAWgF,EAAc8I,GACzBK,EAC7BV,EACFlY,EAAOoU,aAAapU,EAAOI,UAAYyY,IAEvC7Y,EAAOuV,QAAQ9F,EAAc8I,EAAiB,GAAG,GAAO,GACpDnE,IACFpU,EAAO8Y,QAAQ9Y,EAAOiJ,eAAiB,SAAW,WAAa4P,GAGrE,MACMzE,GACFpU,EAAOqW,YAAY2B,EAAgB,GAAG,GAAO,QAG5C,GAAII,EAAoBvf,OAAS,GAAKwf,EAC3C,QAA8B,IAAnBL,EAAgC,CACzC,MAAMY,EAAwB5Y,EAAOyK,WAAWgF,GAE1CoJ,EADoB7Y,EAAOyK,WAAWgF,EAAc+I,GACzBI,EAC7BV,EACFlY,EAAOoU,aAAapU,EAAOI,UAAYyY,IAEvC7Y,EAAOuV,QAAQ9F,EAAc+I,EAAgB,GAAG,GAAO,GACnDpE,IACFpU,EAAO8Y,QAAQ9Y,EAAOiJ,eAAiB,SAAW,WAAa4P,GAGrE,MACE7Y,EAAOqW,YAAY2B,EAAgB,GAAG,GAAO,GAQnD,GAHAhY,EAAO6V,eAAiBA,EACxB7V,EAAO4V,eAAiBA,EAEpB5V,EAAO+Y,YAAc/Y,EAAO+Y,WAAWC,UAAY3E,EAAc,CACnE,MAAM4E,EAAa,CACjBjB,iBACAzC,SAAS,EACTF,YACAjB,eACA6D,mBACA5D,cAAc,GAEZ9R,MAAMC,QAAQxC,EAAO+Y,WAAWC,SAClChZ,EAAO+Y,WAAWC,QAAQrgB,SAASugB,IAC7BA,EAAE1Y,OAAO6M,MAAM6L,EAAErC,QAAQoC,EAAW,IAG1CjZ,EAAO+Y,WAAWC,mBAAmBhZ,EAAO3H,aAC5C2H,EAAO+Y,WAAWC,QAAQxY,OAAO6M,MAEjCrN,EAAO+Y,WAAWC,QAAQnC,QAAQoC,EAEtC,CAEAjZ,EAAOuI,KAAK,UACd,EFxJE4Q,YGPa,WACb,MAAMnZ,EAASzE,MACT8O,OAAEA,EAAM7J,OAAEA,EAAMoJ,SAAEA,GAAa5J,EACrC,IAAKQ,EAAO6M,MAASrN,EAAOkK,SAAWlK,EAAOQ,OAAO0J,QAAQC,QAAU,OACvEnK,EAAO2Y,eAEP,MAAMS,EAAiB,GACvB/O,EAAO1R,SAASkJ,IACd,MAAMuG,OACgC,IAA7BvG,EAAQwX,iBACuC,EAAlDxX,EAAQ0N,aAAa,2BACrB1N,EAAQwX,iBACdD,EAAehR,GAASvG,CAAO,IAEjCwI,EAAO1R,SAASkJ,IACdA,EAAQyX,gBAAgB,0BAA0B,IAEpDF,EAAezgB,SAASkJ,IACtB+H,EAAS8O,OAAO7W,EAAQ,IAE1B7B,EAAO2Y,eACP3Y,EAAOuV,QAAQvV,EAAOkT,UAAW,EACnC,GCLe,SAASqG,EAAa/R,GACnC,MAAMxH,EAASzE,KACTV,EAAWF,IACX2B,EAASF,IAEToM,EAAOxI,EAAOwZ,gBACpBhR,EAAKiR,QAAQ5V,KAAK2D,GAClB,MAAMhH,OAAEA,EAAMsY,QAAEA,EAAO3O,QAAEA,GAAYnK,EACrC,IAAKmK,EAAS,OACd,IAAK3J,EAAOkZ,eAAuC,UAAtBlS,EAAMmS,YAAyB,OAE5D,GAAI3Z,EAAO8U,WAAatU,EAAOuU,+BAC7B,QAEG/U,EAAO8U,WAAatU,EAAOiL,SAAWjL,EAAO6M,MAChDrN,EAAO6W,UAET,IAAI7S,EAAIwD,EACJxD,EAAE4V,gBAAe5V,EAAIA,EAAE4V,eAC3B,IAAIC,EAAW7V,EAAExL,OAEjB,GAAiC,YAA7BgI,EAAOsZ,oBACJ9Z,EAAOU,UAAUqO,SAAS8K,GAAW,OAE5C,GAAI,UAAW7V,GAAiB,IAAZA,EAAE+V,MAAa,OACnC,GAAI,WAAY/V,GAAKA,EAAEgW,OAAS,EAAG,OACnC,GAAIxR,EAAKyR,WAAazR,EAAK0R,QAAS,OAGpC,MAAMC,IAAyB3Z,EAAO4Z,gBAA4C,KAA1B5Z,EAAO4Z,eAEzDC,EAAY7S,EAAM8S,aAAe9S,EAAM8S,eAAiB9S,EAAM+S,KAChEJ,GAAwBnW,EAAExL,QAAUwL,EAAExL,OAAOgiB,YAAcH,IAC7DR,EAAWQ,EAAU,IAGvB,MAAMI,EAAoBja,EAAOia,kBAC7Bja,EAAOia,kBACN,IAAGja,EAAO4Z,iBACTM,KAAoB1W,EAAExL,SAAUwL,EAAExL,OAAOgiB,YAG/C,GACEha,EAAOma,YACND,EAzDL,SAAwBzY,EAAU2Y,GAUhC,YAVoC,IAAJA,MAAOrf,MACvC,SAASsf,EAAcle,GACrB,IAAKA,GAAMA,IAAOhC,KAAiBgC,IAAOP,IAAa,OAAO,KAC1DO,EAAGme,eAAcne,EAAKA,EAAGme,cAC7B,MAAMC,EAAQpe,EAAGkX,QAAQ5R,GACzB,OAAK8Y,GAAUpe,EAAGqe,YAGXD,GAASF,EAAcle,EAAGqe,cAAc5gB,MAFtC,IAGX,CACOygB,CAAcD,EACvB,CA+CQK,CAAeR,EAAmBZ,GAClCA,EAAShG,QAAQ4G,IAGrB,YADAza,EAAOkb,YAAa,GAItB,GAAI1a,EAAO2a,eACJtB,EAAShG,QAAQrT,EAAO2a,cAAe,OAG9CrC,EAAQsC,SAAWpX,EAAEqX,MACrBvC,EAAQwC,SAAWtX,EAAEuX,MACrB,MAAMC,EAAS1C,EAAQsC,SACjBK,EAAS3C,EAAQwC,SAIjBI,EAAqBlb,EAAOkb,oBAAsBlb,EAAOmb,sBACzDC,EAAqBpb,EAAOob,oBAAsBpb,EAAOqb,sBAC/D,GACEH,IACCF,GAAUI,GAAsBJ,GAAUlf,EAAOwf,WAAaF,GAC/D,CACA,GAA2B,YAAvBF,EAGF,OAFAlU,EAAMuU,gBAIV,CAEAzjB,OAAO+Q,OAAOb,EAAM,CAClByR,WAAW,EACXC,SAAS,EACT8B,qBAAqB,EACrBC,iBAAard,EACbsd,iBAAatd,IAGfka,EAAQ0C,OAASA,EACjB1C,EAAQ2C,OAASA,EACjBjT,EAAK2T,eAAiB1f,IACtBuD,EAAOkb,YAAa,EACpBlb,EAAO8I,aACP9I,EAAOoc,oBAAiBxd,EACpB4B,EAAOkX,UAAY,IAAGlP,EAAK6T,oBAAqB,GACpD,IAAIN,GAAiB,EACjBlC,EAAS3X,QAAQsG,EAAK8T,qBACxBP,GAAiB,EACS,WAAtBlC,EAASzgB,WACXoP,EAAKyR,WAAY,IAInBpf,EAAS3B,eACT2B,EAAS3B,cAAcgJ,QAAQsG,EAAK8T,oBACpCzhB,EAAS3B,gBAAkB2gB,GAE3Bhf,EAAS3B,cAAcC,OAGzB,MAAMojB,EACJR,GAAkB/b,EAAOwc,gBAAkBhc,EAAOic,0BAEjDjc,EAAOkc,gCAAiCH,GACxC1C,EAAS8C,mBAEV3Y,EAAE+X,iBAGF/b,EAAOQ,OAAOoc,UACd5c,EAAOQ,OAAOoc,SAASzS,SACvBnK,EAAO4c,UACP5c,EAAO8U,YACNtU,EAAOiL,SAERzL,EAAO4c,SAASrD,eAElBvZ,EAAOuI,KAAK,aAAcvE,EAC5B,CC1Ie,SAAS6Y,EAAYrV,GAClC,MAAM3M,EAAWF,IACXqF,EAASzE,KACTiN,EAAOxI,EAAOwZ,iBACdhZ,OAAEA,EAAMsY,QAAEA,EAAShP,aAAcC,EAAGI,QAAEA,GAAYnK,EACxD,IAAKmK,EAAS,OACd,IAAK3J,EAAOkZ,eAAuC,UAAtBlS,EAAMmS,YAAyB,OAE5D,IAAI3V,EAAIwD,EAER,GADIxD,EAAE4V,gBAAe5V,EAAIA,EAAE4V,gBACtBpR,EAAKyR,UAIR,YAHIzR,EAAK0T,aAAe1T,EAAKyT,aAC3Bjc,EAAOuI,KAAK,oBAAqBvE,IAKrC,MAAM8Y,EAAetU,EAAKiR,QAAQsD,WAAWC,GAAaA,EAASC,YAAcjZ,EAAEiZ,YAC/EH,GAAgB,IAAGtU,EAAKiR,QAAQqD,GAAgB9Y,GACpD,MAAMkZ,EAAc1U,EAAKiR,QAAQ5gB,OAAS,EAAI2P,EAAKiR,QAAQ,GAAKzV,EAC1DqX,EAAQ6B,EAAY7B,MACpBE,EAAQ2B,EAAY3B,MAE1B,GAAIvX,EAAEmZ,wBAGJ,OAFArE,EAAQ0C,OAASH,OACjBvC,EAAQ2C,OAASF,GAGnB,IAAKvb,EAAOwc,eAeV,OAdKxY,EAAExL,OAAO0J,QAAQsG,EAAK8T,qBACzBtc,EAAOkb,YAAa,QAElB1S,EAAKyR,YACP3hB,OAAO+Q,OAAOyP,EAAS,CACrB0C,OAAQH,EACRI,OAAQF,EACR6B,MAAOpd,EAAO8Y,QAAQsC,SACtBiC,MAAOrd,EAAO8Y,QAAQwC,SACtBF,SAAUC,EACVC,SAAUC,IAEZ/S,EAAK2T,eAAiB1f,MAI1B,GAAI+D,EAAO8c,sBAAwB9c,EAAO6M,KACxC,GAAIrN,EAAOkJ,cAET,GACGqS,EAAQzC,EAAQ2C,QAAUzb,EAAOI,WAAaJ,EAAO8Q,gBACrDyK,EAAQzC,EAAQ2C,QAAUzb,EAAOI,WAAaJ,EAAOsQ,eAItD,OAFA9H,EAAKyR,WAAY,OACjBzR,EAAK0R,SAAU,QAGZ,GACJmB,EAAQvC,EAAQ0C,QAAUxb,EAAOI,WAAaJ,EAAO8Q,gBACrDuK,EAAQvC,EAAQ0C,QAAUxb,EAAOI,WAAaJ,EAAOsQ,eAEtD,OAGJ,GAAIzV,EAAS3B,eACP8K,EAAExL,SAAWqC,EAAS3B,eAAiB8K,EAAExL,OAAO0J,QAAQsG,EAAK8T,mBAG/D,OAFA9T,EAAK0R,SAAU,OACfla,EAAOkb,YAAa,GAOxB,GAHI1S,EAAKwT,qBACPhc,EAAOuI,KAAK,YAAavE,GAEvBA,EAAEuZ,eAAiBvZ,EAAEuZ,cAAc1kB,OAAS,EAAG,OAEnDigB,EAAQsC,SAAWC,EACnBvC,EAAQwC,SAAWC,EAEnB,MAAMiC,EAAQ1E,EAAQsC,SAAWtC,EAAQ0C,OACnCiC,EAAQ3E,EAAQwC,SAAWxC,EAAQ2C,OACzC,GAAIzb,EAAOQ,OAAOkX,WAAavW,KAAKuc,KAAKF,GAAS,EAAIC,GAAS,GAAKzd,EAAOQ,OAAOkX,UAChF,OAEF,QAAgC,IAArBlP,EAAKyT,YAA6B,CAC3C,IAAI0B,EAED3d,EAAOiJ,gBAAkB6P,EAAQwC,WAAaxC,EAAQ2C,QACtDzb,EAAOkJ,cAAgB4P,EAAQsC,WAAatC,EAAQ0C,OAErDhT,EAAKyT,aAAc,EAGfuB,EAAQA,EAAQC,EAAQA,GAAS,KACnCE,EAA6D,IAA/Cxc,KAAKyc,MAAMzc,KAAK0L,IAAI4Q,GAAQtc,KAAK0L,IAAI2Q,IAAiBrc,KAAKK,GACzEgH,EAAKyT,YAAcjc,EAAOiJ,eACtB0U,EAAand,EAAOmd,WACpB,GAAKA,EAAand,EAAOmd,WAGnC,CASA,GARInV,EAAKyT,aACPjc,EAAOuI,KAAK,oBAAqBvE,QAEH,IAArBwE,EAAK0T,cACVpD,EAAQsC,WAAatC,EAAQ0C,QAAU1C,EAAQwC,WAAaxC,EAAQ2C,SACtEjT,EAAK0T,aAAc,IAIrB1T,EAAKyT,aACJjc,EAAO6d,MAAQ7d,EAAOQ,OAAOqd,MAAQ7d,EAAOQ,OAAOqd,KAAK1T,SAAW3B,EAAKiR,QAAQ5gB,OAAS,EAG1F,YADA2P,EAAKyR,WAAY,GAGnB,IAAKzR,EAAK0T,YACR,OAEFlc,EAAOkb,YAAa,GACf1a,EAAOiL,SAAWzH,EAAE8Z,YACvB9Z,EAAE+X,iBAEAvb,EAAOud,2BAA6Bvd,EAAOwd,QAC7Cha,EAAEia,kBAGJ,IAAIpF,EAAO7Y,EAAOiJ,eAAiBuU,EAAQC,EACvCS,EAAcle,EAAOiJ,eACrB6P,EAAQsC,SAAWtC,EAAQqF,UAC3BrF,EAAQwC,SAAWxC,EAAQsF,UAE3B5d,EAAO6d,iBACTxF,EAAO1X,KAAK0L,IAAIgM,IAAS9O,EAAM,GAAK,GACpCmU,EAAc/c,KAAK0L,IAAIqR,IAAgBnU,EAAM,GAAK,IAEpD+O,EAAQD,KAAOA,EAEfA,GAAQrY,EAAO8d,WACXvU,IACF8O,GAAQA,EACRqF,GAAeA,GAGjB,MAAMK,EAAuBve,EAAOwe,iBACpCxe,EAAOoc,eAAiBvD,EAAO,EAAI,OAAS,OAC5C7Y,EAAOwe,iBAAmBN,EAAc,EAAI,OAAS,OAErD,MAAMO,EAASze,EAAOQ,OAAO6M,OAAS7M,EAAOiL,QAE7C,IAAKjD,EAAK0R,QAAS,CAMjB,GALIuE,GACFze,EAAO6W,QAAQ,CAAExB,UAAWrV,EAAOoc,iBAErC5T,EAAKkW,eAAiB1e,EAAOtD,eAC7BsD,EAAOqP,cAAc,GACjBrP,EAAO8U,UAAW,CACpB,MAAM6J,EAAM,IAAIriB,OAAOhB,YAAY,gBAAiB,CAClDsjB,SAAS,EACTd,YAAY,IAEd9d,EAAOU,UAAUme,cAAcF,EACjC,CACAnW,EAAKsW,qBAAsB,GAEvBte,EAAOue,aAAyC,IAA1B/e,EAAO4V,iBAAqD,IAA1B5V,EAAO6V,gBACjE7V,EAAOgf,eAAc,GAEvBhf,EAAOuI,KAAK,kBAAmBvE,EACjC,CACA,IAAIib,EAEFzW,EAAK0R,SACLqE,IAAyBve,EAAOwe,kBAChCC,GACAtd,KAAK0L,IAAIgM,IAAS,IAGlB7Y,EAAO6W,QAAQ,CAAExB,UAAWrV,EAAOoc,eAAgBhI,cAAc,IACjE6K,GAAY,GAEdjf,EAAOuI,KAAK,aAAcvE,GAC1BwE,EAAK0R,SAAU,EAEf1R,EAAK2L,iBAAmB0E,EAAOrQ,EAAKkW,eAEpC,IAAIQ,GAAsB,EACtBC,EAAkB3e,EAAO2e,gBA0E7B,GAzEI3e,EAAO8c,sBACT6B,EAAkB,GAEhBtG,EAAO,GAEP4F,IACCQ,GACDzW,EAAK2L,kBACF3T,EAAOgL,eAAiBxL,EAAOsQ,eAAiBtQ,EAAOkE,KAAO,EAAIlE,EAAOsQ,iBAE5EtQ,EAAO6W,QAAQ,CAAExB,UAAW,OAAQjB,cAAc,EAAM6D,iBAAkB,IAExEzP,EAAK2L,iBAAmBnU,EAAOsQ,iBACjC4O,GAAsB,EAClB1e,EAAO4e,aACT5W,EAAK2L,iBACHnU,EAAOsQ,eACP,IACEtQ,EAAOsQ,eAAiB9H,EAAKkW,eAAiB7F,IAASsG,KAGtDtG,EAAO,IAEd4F,IACCQ,GACDzW,EAAK2L,kBACF3T,EAAOgL,eAAiBxL,EAAO8Q,eAAiB9Q,EAAOkE,KAAO,EAAIlE,EAAO8Q,iBAE5E9Q,EAAO6W,QAAQ,CACbxB,UAAW,OACXjB,cAAc,EACd6D,iBACEjY,EAAOqK,OAAOxR,QACY,SAAzB2H,EAAOwL,cACJhM,EAAO0W,uBACPvV,KAAKoM,KAAKvP,WAAWwC,EAAOwL,cAAe,QAGjDxD,EAAK2L,iBAAmBnU,EAAO8Q,iBACjCoO,GAAsB,EAClB1e,EAAO4e,aACT5W,EAAK2L,iBACHnU,EAAO8Q,eACP,GACC9Q,EAAO8Q,eAAiBtI,EAAKkW,eAAiB7F,IAASsG,KAK5DD,IACFlb,EAAEmZ,yBAA0B,IAK3Bnd,EAAO4V,gBACkB,SAA1B5V,EAAOoc,gBACP5T,EAAK2L,iBAAmB3L,EAAKkW,iBAE7BlW,EAAK2L,iBAAmB3L,EAAKkW,iBAG5B1e,EAAO6V,gBACkB,SAA1B7V,EAAOoc,gBACP5T,EAAK2L,iBAAmB3L,EAAKkW,iBAE7BlW,EAAK2L,iBAAmB3L,EAAKkW,gBAE1B1e,EAAO6V,gBAAmB7V,EAAO4V,iBACpCpN,EAAK2L,iBAAmB3L,EAAKkW,gBAI3Ble,EAAOkX,UAAY,EAAG,CACxB,KAAIvW,KAAK0L,IAAIgM,GAAQrY,EAAOkX,WAAalP,EAAK6T,oBAa5C,YADA7T,EAAK2L,iBAAmB3L,EAAKkW,gBAX7B,IAAKlW,EAAK6T,mBAQR,OAPA7T,EAAK6T,oBAAqB,EAC1BvD,EAAQ0C,OAAS1C,EAAQsC,SACzBtC,EAAQ2C,OAAS3C,EAAQwC,SACzB9S,EAAK2L,iBAAmB3L,EAAKkW,oBAC7B5F,EAAQD,KAAO7Y,EAAOiJ,eAClB6P,EAAQsC,SAAWtC,EAAQ0C,OAC3B1C,EAAQwC,SAAWxC,EAAQ2C,OAOrC,CAEKjb,EAAO6e,eAAgB7e,EAAOiL,WAIhCjL,EAAOoc,UAAYpc,EAAOoc,SAASzS,SAAWnK,EAAO4c,UACtDpc,EAAOkO,uBAEP1O,EAAO+S,oBACP/S,EAAO8R,uBAEL9R,EAAOQ,OAAOoc,UAAYpc,EAAOoc,SAASzS,SAAWnK,EAAO4c,UAC9D5c,EAAO4c,SAASC,cAGlB7c,EAAO2Q,eAAenI,EAAK2L,kBAE3BnU,EAAOoU,aAAa5L,EAAK2L,kBAC3B,CCxSe,SAASmL,EAAW9X,GACjC,MAAMxH,EAASzE,KACTiN,EAAOxI,EAAOwZ,gBACdsD,EAAetU,EAAKiR,QAAQsD,WAAWC,GAAaA,EAASC,YAAczV,EAAMyV,YAIvF,GAHIH,GAAgB,GAClBtU,EAAKiR,QAAQpR,OAAOyU,EAAc,GAEhC,CAAC,gBAAiB,aAAc,gBAAgBvW,SAASiB,EAAM+X,MAAO,CAGxE,KADiB,kBAAf/X,EAAM+X,OAA6Bvf,EAAOuE,QAAQ6B,UAAYpG,EAAOuE,QAAQqC,YAE7E,MAEJ,CAEA,MAAMpG,OAAEA,EAAMsY,QAAEA,EAAShP,aAAcC,EAAGU,WAAEA,EAAUN,QAAEA,GAAYnK,EACpE,IAAKmK,EAAS,OACd,IAAK3J,EAAOkZ,eAAuC,UAAtBlS,EAAMmS,YAAyB,OAE5D,IAAI3V,EAAIwD,EAMR,GALIxD,EAAE4V,gBAAe5V,EAAIA,EAAE4V,eACvBpR,EAAKwT,qBACPhc,EAAOuI,KAAK,WAAYvE,GAE1BwE,EAAKwT,qBAAsB,GACtBxT,EAAKyR,UAMR,OALIzR,EAAK0R,SAAW1Z,EAAOue,YACzB/e,EAAOgf,eAAc,GAEvBxW,EAAK0R,SAAU,OACf1R,EAAK0T,aAAc,GAKnB1b,EAAOue,YACPvW,EAAK0R,SACL1R,EAAKyR,aACsB,IAA1Bja,EAAO4V,iBAAqD,IAA1B5V,EAAO6V,iBAE1C7V,EAAOgf,eAAc,GAIvB,MAAMQ,EAAe/iB,IACfgjB,EAAWD,EAAehX,EAAK2T,eAGrC,GAAInc,EAAOkb,WAAY,CACrB,MAAMwE,EAAW1b,EAAEuW,MAASvW,EAAEsW,cAAgBtW,EAAEsW,eAChDta,EAAO4T,mBAAoB8L,GAAYA,EAAS,IAAO1b,EAAExL,QACzDwH,EAAOuI,KAAK,YAAavE,GACrByb,EAAW,KAAOD,EAAehX,EAAKmX,cAAgB,KACxD3f,EAAOuI,KAAK,wBAAyBvE,EAEzC,CAOA,GALAwE,EAAKmX,cAAgBljB,IACrBF,GAAS,KACFyD,EAAOsH,YAAWtH,EAAOkb,YAAa,EAAI,KAI9C1S,EAAKyR,YACLzR,EAAK0R,UACLla,EAAOoc,gBACS,IAAjBtD,EAAQD,MACRrQ,EAAK2L,mBAAqB3L,EAAKkW,eAK/B,OAHAlW,EAAKyR,WAAY,EACjBzR,EAAK0R,SAAU,OACf1R,EAAK0T,aAAc,GAOrB,IAAI0D,EAOJ,GAXApX,EAAKyR,WAAY,EACjBzR,EAAK0R,SAAU,EACf1R,EAAK0T,aAAc,EAIjB0D,EADEpf,EAAO6e,aACItV,EAAM/J,EAAOI,WAAaJ,EAAOI,WAEhCoI,EAAK2L,iBAGjB3T,EAAOiL,QACT,OAGF,GAAIzL,EAAOQ,OAAOoc,UAAYpc,EAAOoc,SAASzS,QAE5C,YADAnK,EAAO4c,SAAS0C,WAAW,CAAEM,eAK/B,IAAIC,EAAY,EACZnS,EAAY1N,EAAO0K,gBAAgB,GACvC,IACE,IAAI5L,EAAI,EACRA,EAAI2L,EAAW5R,OACfiG,GAAKA,EAAI0B,EAAOuM,mBAAqB,EAAIvM,EAAOsM,eAChD,CACA,MAAM6J,EAAY7X,EAAI0B,EAAOuM,mBAAqB,EAAI,EAAIvM,EAAOsM,oBACxB,IAA9BrC,EAAW3L,EAAI6X,GACpBiJ,GAAcnV,EAAW3L,IAAM8gB,EAAanV,EAAW3L,EAAI6X,KAC7DkJ,EAAY/gB,EACZ4O,EAAYjD,EAAW3L,EAAI6X,GAAalM,EAAW3L,IAE5C8gB,GAAcnV,EAAW3L,KAClC+gB,EAAY/gB,EACZ4O,EAAYjD,EAAWA,EAAW5R,OAAS,GAAK4R,EAAWA,EAAW5R,OAAS,GAEnF,CAEA,IAAIinB,EAAmB,KACnBC,EAAkB,KAClBvf,EAAOuW,SACL/W,EAAO+Q,YACTgP,EACE/f,EAAOQ,OAAO0J,SAAWlK,EAAOQ,OAAO0J,QAAQC,SAAWnK,EAAOkK,QAC7DlK,EAAOkK,QAAQG,OAAOxR,OAAS,EAC/BmH,EAAOqK,OAAOxR,OAAS,EACpBmH,EAAOgR,QAChB8O,EAAmB,IAIvB,MAAME,GAASJ,EAAanV,EAAWoV,IAAcnS,EAC/CiJ,EAAYkJ,EAAYrf,EAAOuM,mBAAqB,EAAI,EAAIvM,EAAOsM,eACzE,GAAI2S,EAAWjf,EAAOyf,aAAc,CAElC,IAAKzf,EAAO0f,WAEV,YADAlgB,EAAOuV,QAAQvV,EAAOyP,aAGM,SAA1BzP,EAAOoc,iBACL4D,GAASxf,EAAO2f,gBAClBngB,EAAOuV,QAAQ/U,EAAOuW,QAAU/W,EAAOgR,MAAQ8O,EAAmBD,EAAYlJ,GAC3E3W,EAAOuV,QAAQsK,IAEQ,SAA1B7f,EAAOoc,iBACL4D,EAAQ,EAAIxf,EAAO2f,gBACrBngB,EAAOuV,QAAQsK,EAAYlJ,GAEP,OAApBoJ,GACAC,EAAQ,GACR7e,KAAK0L,IAAImT,GAASxf,EAAO2f,gBAEzBngB,EAAOuV,QAAQwK,GAEf/f,EAAOuV,QAAQsK,GAGrB,KAAO,CAEL,IAAKrf,EAAO4f,YAEV,YADApgB,EAAOuV,QAAQvV,EAAOyP,aAItBzP,EAAOqgB,aACNrc,EAAExL,SAAWwH,EAAOqgB,WAAWC,QAAUtc,EAAExL,SAAWwH,EAAOqgB,WAAWE,QAQhEvc,EAAExL,SAAWwH,EAAOqgB,WAAWC,OACxCtgB,EAAOuV,QAAQsK,EAAYlJ,GAE3B3W,EAAOuV,QAAQsK,IATe,SAA1B7f,EAAOoc,gBACTpc,EAAOuV,QAA6B,OAArBuK,EAA4BA,EAAmBD,EAAYlJ,GAE9C,SAA1B3W,EAAOoc,gBACTpc,EAAOuV,QAA4B,OAApBwK,EAA2BA,EAAkBF,GAOlE,CACF,CCjLA,IAAIW,EACW,SAASC,IACtB,MAAMzgB,EAASzE,MAETiF,OAAEA,EAAM7D,GAAEA,GAAOqD,EAEvB,GAAIrD,GAAyB,IAAnBA,EAAGyH,YAAmB,OAG5B5D,EAAOyL,aACTjM,EAAO0gB,gBAIT,MAAM9K,eAAEA,EAAcC,eAAEA,EAAcrL,SAAEA,GAAaxK,EAE/CiK,EAAYjK,EAAOkK,SAAWlK,EAAOQ,OAAO0J,QAAQC,QAG1DnK,EAAO4V,gBAAiB,EACxB5V,EAAO6V,gBAAiB,EAExB7V,EAAO8I,aACP9I,EAAOsJ,eAEPtJ,EAAO8R,sBACP,MAAM6O,EAAgB1W,GAAazJ,EAAO6M,OAEd,SAAzB7M,EAAOwL,eAA4BxL,EAAOwL,cAAgB,KAC3DhM,EAAOgR,OACNhR,EAAO+Q,aACP/Q,EAAOQ,OAAOgL,gBACdmV,EAIG3gB,EAAOQ,OAAO6M,OAASpD,EACzBjK,EAAOqW,YAAYrW,EAAOkT,UAAW,GAAG,GAAO,GAE/ClT,EAAOuV,QAAQvV,EAAOyP,YAAa,GAAG,GAAO,GAL/CzP,EAAOuV,QAAQvV,EAAOqK,OAAOxR,OAAS,EAAG,GAAG,GAAO,GASjDmH,EAAO4gB,UAAY5gB,EAAO4gB,SAASC,SAAW7gB,EAAO4gB,SAASE,SAChEhlB,aAAa0kB,GACbA,EAAU3kB,YAAW,KACfmE,EAAO4gB,UAAY5gB,EAAO4gB,SAASC,SAAW7gB,EAAO4gB,SAASE,QAChE9gB,EAAO4gB,SAASG,QAClB,GACC,MAGL/gB,EAAO6V,eAAiBA,EACxB7V,EAAO4V,eAAiBA,EAEpB5V,EAAOQ,OAAOgO,eAAiBhE,IAAaxK,EAAOwK,UACrDxK,EAAOyO,eAEX,CC1De,SAASuS,EAAQhd,GAC9B,MAAMhE,EAASzE,KACVyE,EAAOmK,UACPnK,EAAOkb,aACNlb,EAAOQ,OAAOygB,eAAejd,EAAE+X,iBAC/B/b,EAAOQ,OAAO0gB,0BAA4BlhB,EAAO8U,YACnD9Q,EAAEia,kBACFja,EAAEmd,6BAGR,CCVe,SAASC,IACtB,MAAMphB,EAASzE,MACTmF,UAAEA,EAASoJ,aAAEA,EAAYK,QAAEA,GAAYnK,EAC7C,IAAKmK,EAAS,OAad,IAAImK,EAZJtU,EAAOyU,kBAAoBzU,EAAOI,UAC9BJ,EAAOiJ,eACTjJ,EAAOI,WAAaM,EAAUsC,WAE9BhD,EAAOI,WAAaM,EAAUoC,UAGP,IAArB9C,EAAOI,YAAiBJ,EAAOI,UAAY,GAE/CJ,EAAO+S,oBACP/S,EAAO8R,sBAGP,MAAMjB,EAAiB7Q,EAAO8Q,eAAiB9Q,EAAOsQ,eAEpDgE,EADqB,IAAnBzD,EACY,GAEC7Q,EAAOI,UAAYJ,EAAOsQ,gBAAkBO,EAEzDyD,IAAgBtU,EAAOkB,UACzBlB,EAAO2Q,eAAe7G,GAAgB9J,EAAOI,UAAYJ,EAAOI,WAGlEJ,EAAOuI,KAAK,eAAgBvI,EAAOI,WAAW,EAChD,CC5BO,MAAMihB,EAAuB,CAACrhB,EAAQshB,KAC3C,IAAKthB,GAAUA,EAAOsH,YAActH,EAAOQ,OAAQ,OACnD,MACMqB,EAAUyf,EAAQzN,QADK7T,EAAO4P,UAAa,eAAiB,IAAG5P,EAAOQ,OAAO8J,cAEnF,GAAIzI,EAAS,CACX,MAAM0f,EAAS1f,EAAQxI,cAAe,IAAG2G,EAAOQ,OAAOghB,sBACnDD,GAAQA,EAAOtS,QACrB,GCLa,SAASwS,EAAOzd,GAE7Bqd,EADe9lB,KACcyI,EAAExL,QADhB+C,KAERsN,QACT,CCIA,IAAI6Y,GAAqB,EACzB,SAASC,IAAqB,CAE9B,MAAM1a,EAAS,CAACjH,EAAQuH,KACtB,MAAM1M,EAAWF,KACX6F,OAAEA,EAAM7D,GAAEA,EAAE+D,UAAEA,EAASyE,OAAEA,GAAWnF,EACpC4hB,IAAYphB,EAAOwd,OACnB6D,EAAuB,OAAXta,EAAkB,mBAAqB,sBACnDua,EAAeva,EAGrB5K,EAAGklB,GAAW,cAAe7hB,EAAOuZ,aAAc,CAAEwI,SAAS,IAC7DlnB,EAASgnB,GAAW,cAAe7hB,EAAO6c,YAAa,CAAEkF,SAAS,EAAOH,YACzE/mB,EAASgnB,GAAW,YAAa7hB,EAAOsf,WAAY,CAAEyC,SAAS,IAC/DlnB,EAASgnB,GAAW,gBAAiB7hB,EAAOsf,WAAY,CAAEyC,SAAS,IACnElnB,EAASgnB,GAAW,aAAc7hB,EAAOsf,WAAY,CAAEyC,SAAS,IAChElnB,EAASgnB,GAAW,eAAgB7hB,EAAOsf,WAAY,CAAEyC,SAAS,KAG9DvhB,EAAOygB,eAAiBzgB,EAAO0gB,2BACjCvkB,EAAGklB,GAAW,QAAS7hB,EAAOghB,SAAS,GAErCxgB,EAAOiL,SACT/K,EAAUmhB,GAAW,SAAU7hB,EAAOohB,UAIpC5gB,EAAOwhB,qBACThiB,EAAO8hB,GACL3c,EAAOC,KAAOD,EAAOE,QACjB,0CACA,wBACJob,GACA,GAGFzgB,EAAO8hB,GAAc,iBAAkBrB,GAAU,GAInD9jB,EAAGklB,GAAW,OAAQ7hB,EAAOyhB,OAAQ,CAAEG,SAAS,GAAO,EChDzD,MAAMK,EAAgB,CAACjiB,EAAQQ,IACtBR,EAAO2L,MAAQnL,EAAOmL,MAAQnL,EAAOmL,KAAKC,KAAO,EC2B1D,IC9BAsW,EAAe,CACbC,MAAM,EACN9M,UAAW,aACXgJ,gBAAgB,EAChBvE,kBAAmB,UACnB3D,aAAc,EACd1V,MAAO,IACPgL,SAAS,EACTuW,sBAAsB,EACtBI,gBAAgB,EAChBpE,QAAQ,EACRqE,gBAAgB,EAChBlY,SAAS,EACTmS,kBAAmB,wDAGnB/W,MAAO,KACPE,OAAQ,KAGRsP,gCAAgC,EAGhC/Z,UAAW,KACXsnB,IAAK,KAGL5G,oBAAoB,EACpBE,mBAAoB,GAGpB/J,YAAY,EAGZ5E,gBAAgB,EAGhBiH,kBAAkB,EAGlBlH,OAAQ,QAGRf,iBAAarN,EACb2jB,gBAAiB,SAGjBtX,aAAc,EACde,cAAe,EACfc,eAAgB,EAChBC,mBAAoB,EACpB0J,oBAAoB,EACpBjL,gBAAgB,EAChBqC,sBAAsB,EACtBjD,mBAAoB,EACpBE,kBAAmB,EACnByI,qBAAqB,EACrBrF,0BAA0B,EAG1BM,eAAe,EAGfjC,cAAc,EAGd+R,WAAY,EACZX,WAAY,GACZjE,eAAe,EACf0G,aAAa,EACbF,YAAY,EACZC,gBAAiB,GACjBF,aAAc,IACdZ,cAAc,EACd7C,gBAAgB,EAChB9E,UAAW,EACXqG,0BAA0B,EAC1BtB,0BAA0B,EAC1BC,+BAA+B,EAC/BY,qBAAqB,EAGrBkF,mBAAmB,EAGnBpD,YAAY,EACZD,gBAAiB,IAGjBzQ,qBAAqB,EAGrBqQ,YAAY,EAGZkC,eAAe,EACfC,0BAA0B,EAC1BjN,qBAAqB,EAGrB5G,MAAM,EACNyK,aAAc,KACdlB,qBAAqB,EAGrBG,QAAQ,EAGRlB,gBAAgB,EAChBD,gBAAgB,EAChBuF,aAAc,KACdR,WAAW,EACXP,eAAgB,oBAChBK,kBAAmB,KAGnBgI,kBAAkB,EAElBzT,wBAAyB,GAGzBH,uBAAwB,UACxBvE,WAAY,eACZ2H,iBAAkB,sBAClB/B,kBAAmB,uBACnBgC,eAAgB,oBAChBC,eAAgB,oBAChBuQ,aAAc,iBACdlB,mBAAoB,wBAGpB7N,oBAAoB,EAGpBgP,cAAc,GCpID,SAASC,EAAmBpiB,EAAQqiB,GACjD,OAAO,SAAsBzqB,QAAG,IAAHA,MAAM,IACjC,MAAM0qB,EAAkBxqB,OAAOI,KAAKN,GAAK,GACnC2qB,EAAe3qB,EAAI0qB,GACG,iBAAjBC,GAA8C,OAAjBA,GAKtC,CAAC,aAAc,aAAc,aAAa7jB,QAAQ4jB,IAAoB,IAC1C,IAA5BtiB,EAAOsiB,KAEPtiB,EAAOsiB,GAAmB,CAAEE,MAAM,IAE9BF,KAAmBtiB,GAAU,YAAauiB,IAIhB,IAA5BviB,EAAOsiB,KACTtiB,EAAOsiB,GAAmB,CAAE3Y,SAAS,IAEA,iBAA5B3J,EAAOsiB,IAAmC,YAAatiB,EAAOsiB,KACvEtiB,EAAOsiB,GAAiB3Y,SAAU,GAE/B3J,EAAOsiB,KAAkBtiB,EAAOsiB,GAAmB,CAAE3Y,SAAS,IACnE5R,EAAOsqB,EAAkBzqB,IAVvBG,EAAOsqB,EAAkBzqB,IAVzBG,EAAOsqB,EAAkBzqB,E,CAsB/B,CCMA,MAAM6qB,EAAa,CACjBlc,gBACA8B,SACAzI,YACA8iB,WCnCa,CACb7T,cCLa,SAAuB9O,EAAU8T,GAC9C,MAAMrU,EAASzE,KAEVyE,EAAOQ,OAAOiL,UACjBzL,EAAOU,UAAU7G,MAAMspB,mBAAsB,GAAE5iB,OAGjDP,EAAOuI,KAAK,gBAAiBhI,EAAU8T,EACzC,EDFEyB,gBEJa,SAAyBnB,EAAqBU,QAAT,IAAZV,OAAe,GACrD,MAAM3U,EAASzE,MACTiF,OAAEA,GAAWR,EACfQ,EAAOiL,UACPjL,EAAOqR,YACT7R,EAAOkP,mBAGTkG,EAAe,CAAEpV,SAAQ2U,eAAcU,YAAWC,KAAM,UAC1D,EFJES,cGLa,SAAuBpB,EAAqBU,QAAT,IAAZV,OAAe,GACnD,MAAM3U,EAASzE,MACTiF,OAAEA,GAAWR,EACnBA,EAAO8U,WAAY,EACftU,EAAOiL,UACXzL,EAAOqP,cAAc,GAErB+F,EAAe,CAAEpV,SAAQ2U,eAAcU,YAAWC,KAAM,QAC1D,GJ8BEpJ,QACAmB,OACA0R,WKvCa,CACbC,cCJa,SAAuBoE,GACpC,MAAMpjB,EAASzE,KACf,IACGyE,EAAOQ,OAAOkZ,eACd1Z,EAAOQ,OAAOgO,eAAiBxO,EAAOqjB,UACvCrjB,EAAOQ,OAAOiL,QAEd,OACF,MAAM9O,EAAyC,cAApCqD,EAAOQ,OAAOsZ,kBAAoC9Z,EAAOrD,GAAKqD,EAAOU,UAC5EV,EAAO4P,YACT5P,EAAOsjB,qBAAsB,GAE/B3mB,EAAG9C,MAAM0pB,OAAS,OAClB5mB,EAAG9C,MAAM0pB,OAASH,EAAS,WAAa,OACpCpjB,EAAO4P,WACT5T,uBAAsB,KACpBgE,EAAOsjB,qBAAsB,CAAK,GAGxC,EDdEE,gBELa,WACb,MAAMxjB,EAASzE,KACVyE,EAAOQ,OAAOgO,eAAiBxO,EAAOqjB,UAAarjB,EAAOQ,OAAOiL,UAGlEzL,EAAO4P,YACT5P,EAAOsjB,qBAAsB,GAE/BtjB,EAA2C,cAApCA,EAAOQ,OAAOsZ,kBAAoC,KAAO,aAAajgB,MAAM0pB,OAAS,GACxFvjB,EAAO4P,WACT5T,uBAAsB,KACpBgE,EAAOsjB,qBAAsB,CAAK,IAGxC,G,OZoEe,CACbG,aA9BF,WACE,MAAMzjB,EAASzE,KACTV,EAAWF,KACX6F,OAAEA,GAAWR,EAEnBA,EAAOuZ,aAAeA,EAAamK,KAAK1jB,GACxCA,EAAO6c,YAAcA,EAAY6G,KAAK1jB,GACtCA,EAAOsf,WAAaA,EAAWoE,KAAK1jB,GAEhCQ,EAAOiL,UACTzL,EAAOohB,SAAWA,EAASsC,KAAK1jB,IAGlCA,EAAOghB,QAAUA,EAAQ0C,KAAK1jB,GAC9BA,EAAOyhB,OAASA,EAAOiC,KAAK1jB,GAEvB0hB,IACH7mB,EAAS7B,iBAAiB,aAAc2oB,GACxCD,GAAqB,GAGvBza,EAAOjH,EAAQ,KACjB,EASE2jB,aAPF,WAEE1c,EADe1L,KACA,MACjB,GKpCE0Q,YQzCa,CAAEyU,cZGF,WACb,MAAM1gB,EAASzE,MACT2X,UAAEA,EAASQ,YAAEA,EAAWlT,OAAEA,EAAM7D,GAAEA,GAAOqD,EACzCiM,EAAczL,EAAOyL,YAC3B,IAAKA,GAAgBA,GAAmD,IAApC3T,OAAOI,KAAKuT,GAAapT,OAAe,OAG5E,MAAM+qB,EAAa5jB,EAAO6jB,cAAc5X,EAAajM,EAAOQ,OAAO+hB,gBAAiBviB,EAAOrD,IAE3F,IAAKinB,GAAc5jB,EAAO8jB,oBAAsBF,EAAY,OAE5D,MACMG,GADuBH,KAAc3X,EAAcA,EAAY2X,QAAchlB,IAClCoB,EAAOgkB,eAClDC,EAAchC,EAAcjiB,EAAQQ,GACpC0jB,EAAajC,EAAcjiB,EAAQ+jB,GAEnCI,EAAa3jB,EAAO2J,QAEtB8Z,IAAgBC,GAClBvnB,EAAG0F,UAAU4M,OACV,GAAEzO,EAAOqO,6BACT,GAAErO,EAAOqO,qCAEZ7O,EAAOokB,yBACGH,GAAeC,IACzBvnB,EAAG0F,UAAUC,IAAK,GAAE9B,EAAOqO,+BAExBkV,EAAiBpY,KAAK0Y,MAAuC,WAA/BN,EAAiBpY,KAAK0Y,OACnDN,EAAiBpY,KAAK0Y,MAA6B,WAArB7jB,EAAOmL,KAAK0Y,OAE5C1nB,EAAG0F,UAAUC,IAAK,GAAE9B,EAAOqO,qCAE7B7O,EAAOokB,wBAIT,CAAC,aAAc,aAAc,aAAazrB,SAAS0K,IACjD,MAAMihB,EAAmB9jB,EAAO6C,IAAS7C,EAAO6C,GAAM8G,QAChDoa,EAAkBR,EAAiB1gB,IAAS0gB,EAAiB1gB,GAAM8G,QACrEma,IAAqBC,GACvBvkB,EAAOqD,GAAMmhB,WAEVF,GAAoBC,GACvBvkB,EAAOqD,GAAMohB,QACf,IAGF,MAAMC,EACJX,EAAiB1O,WAAa0O,EAAiB1O,YAAc7U,EAAO6U,UAChEsP,EACJnkB,EAAO6M,OAAS0W,EAAiB/X,gBAAkBxL,EAAOwL,eAAiB0Y,GAEzEA,GAAoBhR,GACtB1T,EAAO4kB,kBAETrsB,EAAOyH,EAAOQ,OAAQujB,GAEtB,MAAMc,EAAY7kB,EAAOQ,OAAO2J,QAEhC7R,OAAO+Q,OAAOrJ,EAAQ,CACpBwc,eAAgBxc,EAAOQ,OAAOgc,eAC9B5G,eAAgB5V,EAAOQ,OAAOoV,eAC9BC,eAAgB7V,EAAOQ,OAAOqV,iBAG5BsO,IAAeU,EACjB7kB,EAAOwkB,WACGL,GAAcU,GACxB7kB,EAAOykB,SAGTzkB,EAAO8jB,kBAAoBF,EAE3B5jB,EAAOuI,KAAK,oBAAqBwb,GAE7BY,GAAejR,IACjB1T,EAAOmZ,cACPnZ,EAAO+X,WAAW7E,GAClBlT,EAAOsJ,gBAGTtJ,EAAOuI,KAAK,aAAcwb,EAC5B,EYrFgCF,cCDjB,SAAuB5X,EAAa2O,EAAiBkK,GAClE,QADqD,IAAJlK,MAAO,WACnD3O,GAAyB,cAAT2O,IAAyBkK,EAAc,OAC5D,IAAIlB,GAAa,EAEjB,MAAMtnB,EAASF,IACT2oB,EAAyB,WAATnK,EAAoBte,EAAO0oB,YAAcF,EAAY9b,aAErEic,EAAS3sB,OAAOI,KAAKuT,GAAa3O,KAAK4nB,IAC3C,GAAqB,iBAAVA,GAA6C,IAAvBA,EAAMhmB,QAAQ,KAAY,CACzD,MAAMimB,EAAWnnB,WAAWknB,EAAME,OAAO,IAEzC,MAAO,CAAEC,MADKN,EAAgBI,EACdD,QAClB,CACA,MAAO,CAAEG,MAAOH,EAAOA,QAAO,IAGhCD,EAAOK,MAAK,CAAC/nB,EAAGgoB,IAAMpc,SAAS5L,EAAE8nB,MAAO,IAAMlc,SAASoc,EAAEF,MAAO,MAChE,IAAK,IAAIvmB,EAAI,EAAGA,EAAImmB,EAAOpsB,OAAQiG,GAAK,EAAG,CACzC,MAAMomB,MAAEA,EAAKG,MAAEA,GAAUJ,EAAOnmB,GACnB,WAAT8b,EACEte,EAAOP,WAAY,eAAcspB,QAAYnjB,UAC/C0hB,EAAasB,GAENG,GAASP,EAAY/b,cAC9B6a,EAAasB,EAEjB,CACA,OAAOtB,GAAc,KACvB,G,cZAe,CAAEnV,cA9BjB,WACE,MAAMzO,EAASzE,MACP8nB,SAAUmC,EAAShlB,OAAEA,GAAWR,GAClC4K,mBAAEA,GAAuBpK,EAE/B,GAAIoK,EAAoB,CACtB,MAAM4G,EAAiBxR,EAAOqK,OAAOxR,OAAS,EACxC4sB,EACJzlB,EAAOyK,WAAW+G,GAClBxR,EAAO0K,gBAAgB8G,GACF,EAArB5G,EACF5K,EAAOqjB,SAAWrjB,EAAOkE,KAAOuhB,CAClC,MACEzlB,EAAOqjB,SAAsC,IAA3BrjB,EAAOwK,SAAS3R,QAEN,IAA1B2H,EAAOoV,iBACT5V,EAAO4V,gBAAkB5V,EAAOqjB,WAEJ,IAA1B7iB,EAAOqV,iBACT7V,EAAO6V,gBAAkB7V,EAAOqjB,UAG9BmC,GAAaA,IAAcxlB,EAAOqjB,WACpCrjB,EAAOgR,OAAQ,GAEbwU,IAAcxlB,EAAOqjB,UACvBrjB,EAAOuI,KAAKvI,EAAOqjB,SAAW,OAAS,SAE3C,GGkBEjhB,QU3Ca,CAAEsjB,WCaF,WACb,MAAM1lB,EAASzE,MACToqB,WAAEA,EAAUnlB,OAAEA,EAAMuJ,IAAEA,EAAGpN,GAAEA,EAAEwI,OAAEA,GAAWnF,EAE1C4lB,EApBR,SAAwBC,EAASC,GAC/B,MAAMC,EAAgB,GAYtB,OAXAF,EAAQltB,SAASqtB,IACK,iBAATA,EACT1tB,OAAOI,KAAKstB,GAAMrtB,SAASgtB,IACrBK,EAAKL,IACPI,EAAcliB,KAAKiiB,EAASH,EAC9B,IAEuB,iBAATK,GAChBD,EAAcliB,KAAKiiB,EAASE,EAC9B,IAEKD,CACT,CAMmBE,CAAe,CAC9B,cACAzlB,EAAO6U,UACP,CAAE,YAAarV,EAAOQ,OAAOoc,UAAYpc,EAAOoc,SAASzS,SACzD,CAAE+b,WAAc1lB,EAAOqR,YACvB,CAAE9H,IAAOA,GACT,CAAE4B,KAAQnL,EAAOmL,MAAQnL,EAAOmL,KAAKC,KAAO,GAC5C,CAAE,cAAepL,EAAOmL,MAAQnL,EAAOmL,KAAKC,KAAO,GAA0B,WAArBpL,EAAOmL,KAAK0Y,MACpE,CAAEhf,QAAWF,EAAOE,SACpB,CAAED,IAAOD,EAAOC,KAChB,CAAE,WAAY5E,EAAOiL,SACrB,CAAE0a,SAAY3lB,EAAOiL,SAAWjL,EAAOgL,gBACvC,CAAE,iBAAkBhL,EAAOkO,sBAC1BlO,EAAOqO,wBACV8W,EAAW9hB,QAAQ+hB,GACnBjpB,EAAG0F,UAAUC,OAAOqjB,GACpB3lB,EAAOokB,sBACT,EDlC6BgC,cEHd,WACb,MACMzpB,GAAEA,EAAEgpB,WAAEA,GADGpqB,KAGfoB,EAAG0F,UAAU4M,UAAU0W,GAHRpqB,KAIR6oB,sBACT,IZ2CMiC,EAAmB,GAEzB,MAAMC,EACJjuB,cACE,IAAIsE,EACA6D,EAAO,QAAAqH,EAAAlJ,UAAA9F,OAFEiP,EAAI,IAAAvF,MAAAsF,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAJD,EAAIC,GAAApJ,UAAAoJ,GAIC,IAAhBD,EAAKjP,QACLiP,EAAK,GAAGzP,aACiD,WAAzDC,OAAO6F,UAAUL,SAASM,KAAK0J,EAAK,IAAIzJ,MAAM,GAAI,GAElDmC,EAASsH,EAAK,IAEbnL,EAAI6D,GAAUsH,EAEZtH,IAAQA,EAAS,IAEtBA,EAASjI,EAAO,GAAIiI,GAChB7D,IAAO6D,EAAO7D,KAAI6D,EAAO7D,GAAKA,GAElC,MAAM9B,EAAWF,IAEjB,GACE6F,EAAO7D,IACc,iBAAd6D,EAAO7D,IACd9B,EAASvB,iBAAiBkH,EAAO7D,IAAI9D,OAAS,EAC9C,CACA,MAAM0tB,EAAU,GAMhB,OALA1rB,EAASvB,iBAAiBkH,EAAO7D,IAAIhE,SAASmsB,IAC5C,MAAM0B,EAAYjuB,EAAO,GAAIiI,EAAQ,CAAE7D,GAAImoB,IAC3CyB,EAAQ1iB,KAAK,IAAIyiB,EAAOE,GAAW,IAG9BD,CACT,CAGA,MAAMvmB,EAASzE,KACfyE,EAAOP,YAAa,EACpBO,EAAOqE,QAAUG,IACjBxE,EAAOmF,OAASL,EAAU,CAAE9J,UAAWwF,EAAOxF,YAC9CgF,EAAOuE,QAAU2B,IAEjBlG,EAAOqH,gBAAkB,GACzBrH,EAAOkI,mBAAqB,GAC5BlI,EAAOymB,QAAU,IAAIzmB,EAAO0mB,aACxBlmB,EAAOimB,SAAWlkB,MAAMC,QAAQhC,EAAOimB,UACzCzmB,EAAOymB,QAAQ5iB,QAAQrD,EAAOimB,SAGhC,MAAM5D,EAAmB,GACzB7iB,EAAOymB,QAAQ9tB,SAASguB,IACtBA,EAAI,CACFnmB,SACAR,SACA4mB,aAAchE,EAAmBpiB,EAAQqiB,GACzC7b,GAAIhH,EAAOgH,GAAG0c,KAAK1jB,GACnByH,KAAMzH,EAAOyH,KAAKic,KAAK1jB,GACvB2H,IAAK3H,EAAO2H,IAAI+b,KAAK1jB,GACrBuI,KAAMvI,EAAOuI,KAAKmb,KAAK1jB,IACvB,IAIJ,MAAM6mB,EAAetuB,EAAO,GAAI2pB,EAAUW,GA4G1C,OAzGA7iB,EAAOQ,OAASjI,EAAO,GAAIsuB,EAAcR,EAAkB7lB,GAC3DR,EAAOgkB,eAAiBzrB,EAAO,GAAIyH,EAAOQ,QAC1CR,EAAO8mB,aAAevuB,EAAO,GAAIiI,GAG7BR,EAAOQ,QAAUR,EAAOQ,OAAOwG,IACjC1O,OAAOI,KAAKsH,EAAOQ,OAAOwG,IAAIrO,SAASouB,IACrC/mB,EAAOgH,GAAG+f,EAAW/mB,EAAOQ,OAAOwG,GAAG+f,GAAW,IAGjD/mB,EAAOQ,QAAUR,EAAOQ,OAAOyH,OACjCjI,EAAOiI,MAAMjI,EAAOQ,OAAOyH,OAI7B3P,OAAO+Q,OAAOrJ,EAAQ,CACpBmK,QAASnK,EAAOQ,OAAO2J,QACvBxN,KAGAgpB,WAAY,GAGZtb,OAAQ,GACRI,WAAY,GACZD,SAAU,GACVE,gBAAiB,GAGjBzB,aAAY,IACyB,eAA5BjJ,EAAOQ,OAAO6U,UAEvBnM,WAAU,IAC2B,aAA5BlJ,EAAOQ,OAAO6U,UAIvB5F,YAAa,EACbyD,UAAW,EAGXnC,aAAa,EACbC,OAAO,EAGP5Q,UAAW,EACXqU,kBAAmB,EACnBvT,SAAU,EACV8lB,SAAU,EACVlS,WAAW,EAGXc,eAAgB5V,EAAOQ,OAAOoV,eAC9BC,eAAgB7V,EAAOQ,OAAOqV,eAG9B2D,gBAAiB,CACfS,eAAWrb,EACXsb,aAAStb,EACTod,yBAAqBpd,EACrBud,oBAAgBvd,EAChBqd,iBAAard,EACbuV,sBAAkBvV,EAClB8f,oBAAgB9f,EAChByd,wBAAoBzd,EAEpB0d,kBAAmBtc,EAAOQ,OAAO8b,kBAEjCqD,cAAeljB,IACfwqB,kBAAcroB,EAEdsoB,WAAY,GACZpI,yBAAqBlgB,EACrBsd,iBAAatd,EACb6a,QAAS,IAIXyB,YAAY,EAGZsB,eAAgBxc,EAAOQ,OAAOgc,eAE9B1D,QAAS,CACP0C,OAAQ,EACRC,OAAQ,EACRL,SAAU,EACVE,SAAU,EACVzC,KAAM,GAIRsO,aAAc,GACdC,aAAc,IAGhBpnB,EAAOuI,KAAK,WAGRvI,EAAOQ,OAAO2hB,MAChBniB,EAAOmiB,OAKFniB,CACT,CAEAuR,cAAc1P,GACZ,MAAM+H,SAAEA,EAAQpJ,OAAEA,GAAWjF,KAEvB+V,EAAkBhO,EADTvB,EAAgB6H,EAAW,IAAGpJ,EAAO8J,4BACR,IAC5C,OAAOhH,EAAazB,GAAWyP,CACjC,CAEAqH,eACE,MACM/O,SAAEA,EAAQpJ,OAAEA,GADHjF,UAER8O,OAAStI,EAAgB6H,EAAW,IAAGpJ,EAAO8J,2BACvD,CAEAma,SACE,MAAMzkB,EAASzE,KACXyE,EAAOmK,UACXnK,EAAOmK,SAAU,EACbnK,EAAOQ,OAAOue,YAChB/e,EAAOgf,gBAEThf,EAAOuI,KAAK,UACd,CAEAic,UACE,MAAMxkB,EAASzE,KACVyE,EAAOmK,UACZnK,EAAOmK,SAAU,EACbnK,EAAOQ,OAAOue,YAChB/e,EAAOwjB,kBAETxjB,EAAOuI,KAAK,WACd,CAEA8e,YAAYnmB,EAAUT,GACpB,MAAMT,EAASzE,KACf2F,EAAWC,KAAKE,IAAIF,KAAKC,IAAIF,EAAU,GAAI,GAC3C,MAAMG,EAAMrB,EAAOsQ,eAEbvP,GADMf,EAAO8Q,eACIzP,GAAOH,EAAWG,EACzCrB,EAAO0U,YAAY3T,OAA0B,IAAVN,EAAwB,EAAIA,GAC/DT,EAAO+S,oBACP/S,EAAO8R,qBACT,CAEAsS,uBACE,MAAMpkB,EAASzE,KACf,IAAKyE,EAAOQ,OAAOmiB,eAAiB3iB,EAAOrD,GAAI,OAC/C,MAAM2qB,EAAMtnB,EAAOrD,GAAG4qB,UAAUlqB,MAAM,KAAK4B,QAAQsoB,GAEf,IAAhCA,EAAUroB,QAAQ,WAC0C,IAA5DqoB,EAAUroB,QAAQc,EAAOQ,OAAOqO,0BAGpC7O,EAAOuI,KAAK,oBAAqB+e,EAAI7pB,KAAK,KAC5C,CAEA+pB,gBAAgB3lB,GACd,MAAM7B,EAASzE,KACf,OAAIyE,EAAOsH,UAAkB,GAEtBzF,EAAQ0lB,UACZlqB,MAAM,KACN4B,QAAQsoB,GAEiC,IAAtCA,EAAUroB,QAAQ,iBAC8B,IAAhDqoB,EAAUroB,QAAQc,EAAOQ,OAAO8J,cAGnC7M,KAAK,IACV,CAEAqV,oBACE,MAAM9S,EAASzE,KACf,IAAKyE,EAAOQ,OAAOmiB,eAAiB3iB,EAAOrD,GAAI,OAC/C,MAAM8qB,EAAU,GAChBznB,EAAOqK,OAAO1R,SAASkJ,IACrB,MAAM8jB,EAAa3lB,EAAOwnB,gBAAgB3lB,GAC1C4lB,EAAQ5jB,KAAK,CAAEhC,UAAS8jB,eACxB3lB,EAAOuI,KAAK,cAAe1G,EAAS8jB,EAAW,IAEjD3lB,EAAOuI,KAAK,gBAAiBkf,EAC/B,CAEA/Q,qBAAqBgR,EAAkBC,QAAd,IAAJD,MAAO,gBAAgB,IAALC,OAAQ,GAC7C,MACMnnB,OAAEA,EAAM6J,OAAEA,EAAMI,WAAEA,EAAUC,gBAAEA,EAAiBxG,KAAM2F,EAAU4F,YAAEA,GADxDlU,KAEf,IAAIqsB,EAAM,EACV,GAAIpnB,EAAOgL,eAAgB,CACzB,IACIqc,EADAhc,EAAYxB,EAAOoF,GAAa7C,gBAEpC,IAAK,IAAI9N,EAAI2Q,EAAc,EAAG3Q,EAAIuL,EAAOxR,OAAQiG,GAAK,EAChDuL,EAAOvL,KAAO+oB,IAChBhc,GAAaxB,EAAOvL,GAAG8N,gBACvBgb,GAAO,EACH/b,EAAYhC,IAAYge,GAAY,IAG5C,IAAK,IAAI/oB,EAAI2Q,EAAc,EAAG3Q,GAAK,EAAGA,GAAK,EACrCuL,EAAOvL,KAAO+oB,IAChBhc,GAAaxB,EAAOvL,GAAG8N,gBACvBgb,GAAO,EACH/b,EAAYhC,IAAYge,GAAY,GAG9C,MAEE,GAAa,YAATH,EACF,IAAK,IAAI5oB,EAAI2Q,EAAc,EAAG3Q,EAAIuL,EAAOxR,OAAQiG,GAAK,EAAG,EACnC6oB,EAChBld,EAAW3L,GAAK4L,EAAgB5L,GAAK2L,EAAWgF,GAAe5F,EAC/DY,EAAW3L,GAAK2L,EAAWgF,GAAe5F,KAE5C+d,GAAO,EAEX,MAGA,IAAK,IAAI9oB,EAAI2Q,EAAc,EAAG3Q,GAAK,EAAGA,GAAK,EAAG,CACxB2L,EAAWgF,GAAehF,EAAW3L,GAAK+K,IAE5D+d,GAAO,EAEX,CAGJ,OAAOA,CACT,CAEA/e,SACE,MAAM7I,EAASzE,KACf,IAAKyE,GAAUA,EAAOsH,UAAW,OACjC,MAAMkD,SAAEA,EAAQhK,OAAEA,GAAWR,EAiB7B,SAASoU,IACP,MAAM0T,EAAiB9nB,EAAO8J,cAAmC,EAApB9J,EAAOI,UAAiBJ,EAAOI,UACtE4U,EAAe7T,KAAKE,IACxBF,KAAKC,IAAI0mB,EAAgB9nB,EAAO8Q,gBAChC9Q,EAAOsQ,gBAETtQ,EAAOoU,aAAaY,GACpBhV,EAAO+S,oBACP/S,EAAO8R,qBACT,CACA,IAAIiW,EAzBAvnB,EAAOyL,aACTjM,EAAO0gB,gBAGT,IAAI1gB,EAAOrD,GAAGrD,iBAAiB,qBAAqBX,SAAS2oB,IACvDA,EAAQ0G,UACV3G,EAAqBrhB,EAAQshB,EAC/B,IAGFthB,EAAO8I,aACP9I,EAAOsJ,eACPtJ,EAAO2Q,iBACP3Q,EAAO8R,sBAaH9R,EAAOQ,OAAOoc,UAAY5c,EAAOQ,OAAOoc,SAASzS,SACnDiK,IACIpU,EAAOQ,OAAOqR,YAChB7R,EAAOkP,qBAQP6Y,GAJiC,SAAhC/nB,EAAOQ,OAAOwL,eAA4BhM,EAAOQ,OAAOwL,cAAgB,IACzEhM,EAAOgR,QACNhR,EAAOQ,OAAOgL,eAEFxL,EAAOuV,QAAQvV,EAAOqK,OAAOxR,OAAS,EAAG,GAAG,GAAO,GAEnDmH,EAAOuV,QAAQvV,EAAOyP,YAAa,GAAG,GAAO,GAEvDsY,GACH3T,KAGA5T,EAAOgO,eAAiBhE,IAAaxK,EAAOwK,UAC9CxK,EAAOyO,gBAETzO,EAAOuI,KAAK,SACd,CAEAqc,gBAAgBqD,EAAcC,QAAU,IAAVA,OAAa,GACzC,MAAMloB,EAASzE,KACT4sB,EAAmBnoB,EAAOQ,OAAO6U,UAKvC,OAJK4S,IAEHA,EAAoC,eAArBE,EAAoC,WAAa,cAGhEF,IAAiBE,GACC,eAAjBF,GAAkD,aAAjBA,IAKpCjoB,EAAOrD,GAAG0F,UAAU4M,OAAQ,GAAEjP,EAAOQ,OAAOqO,yBAAyBsZ,KACrEnoB,EAAOrD,GAAG0F,UAAUC,IAAK,GAAEtC,EAAOQ,OAAOqO,yBAAyBoZ,KAClEjoB,EAAOokB,uBAEPpkB,EAAOQ,OAAO6U,UAAY4S,EAE1BjoB,EAAOqK,OAAO1R,SAASkJ,IACA,aAAjBomB,EACFpmB,EAAQhI,MAAM0L,MAAQ,GAEtB1D,EAAQhI,MAAM4L,OAAS,EACzB,IAGFzF,EAAOuI,KAAK,mBACR2f,GAAYloB,EAAO6I,UAlBd7I,CAqBX,CAEAooB,wBAAwB/S,GACtB,MAAMrV,EAASzE,KACVyE,EAAO+J,KAAqB,QAAdsL,IAA0BrV,EAAO+J,KAAqB,QAAdsL,IAC3DrV,EAAO+J,IAAoB,QAAdsL,EACbrV,EAAO8J,aAA2C,eAA5B9J,EAAOQ,OAAO6U,WAA8BrV,EAAO+J,IACrE/J,EAAO+J,KACT/J,EAAOrD,GAAG0F,UAAUC,IAAK,GAAEtC,EAAOQ,OAAOqO,6BACzC7O,EAAOrD,GAAGkE,IAAM,QAEhBb,EAAOrD,GAAG0F,UAAU4M,OAAQ,GAAEjP,EAAOQ,OAAOqO,6BAC5C7O,EAAOrD,GAAGkE,IAAM,OAElBb,EAAO6I,SACT,CAEAwf,MAAMrmB,GACJ,MAAMhC,EAASzE,KACf,GAAIyE,EAAOsoB,QAAS,OAAO,EAG3B,IAAI3rB,EAAKqF,GAAWhC,EAAOQ,OAAO7D,GAIlC,GAHkB,iBAAPA,IACTA,EAAK9B,SAASxB,cAAcsD,KAEzBA,EACH,OAAO,EAGTA,EAAGqD,OAASA,EACRrD,EAAGmF,WACL9B,EAAO4P,WAAY,GAGrB,MAAM2Y,EAAqB,IACjB,KAAIvoB,EAAOQ,OAAOkiB,cAAgB,IAAI8F,OAAOnrB,MAAM,KAAKI,KAAK,OAYvE,IAAIiD,EATe,MACjB,GAAI/D,GAAMA,EAAG6d,YAAc7d,EAAG6d,WAAWnhB,cAAe,CAGtD,OAFYsD,EAAG6d,WAAWnhB,cAAckvB,IAG1C,CACA,OAAOxmB,EAAgBpF,EAAI4rB,KAAsB,EAAE,EAGrCE,GAuBhB,OAtBK/nB,GAAaV,EAAOQ,OAAO6hB,iBAC9B3hB,EAAYhH,EAAc,MAAOsG,EAAOQ,OAAOkiB,cAC/C/lB,EAAG+b,OAAOhY,GACVqB,EAAgBpF,EAAK,IAAGqD,EAAOQ,OAAO8J,cAAc3R,SAASkJ,IAC3DnB,EAAUgY,OAAO7W,EAAQ,KAI7BvJ,OAAO+Q,OAAOrJ,EAAQ,CACpBrD,KACA+D,YACAkJ,SAAU5J,EAAO4P,UAAYjT,EAAK+D,EAClC4nB,SAAS,EAGTve,IAA8B,QAAzBpN,EAAGkE,IAAIwF,eAA6D,QAAlCjD,EAAazG,EAAI,aACxDmN,aAC8B,eAA5B9J,EAAOQ,OAAO6U,YACY,QAAzB1Y,EAAGkE,IAAIwF,eAA6D,QAAlCjD,EAAazG,EAAI,cACtDqN,SAAiD,gBAAvC5G,EAAa1C,EAAW,cAG7B,CACT,CAEAyhB,KAAKxlB,GACH,MAAMqD,EAASzE,KACf,GAAIyE,EAAO0T,YAAa,OAAO1T,EAG/B,OAAgB,IADAA,EAAOqoB,MAAM1rB,KAG7BqD,EAAOuI,KAAK,cAGRvI,EAAOQ,OAAOyL,aAChBjM,EAAO0gB,gBAIT1gB,EAAO0lB,aAGP1lB,EAAO8I,aAGP9I,EAAOsJ,eAEHtJ,EAAOQ,OAAOgO,eAChBxO,EAAOyO,gBAILzO,EAAOQ,OAAOue,YAAc/e,EAAOmK,SACrCnK,EAAOgf,gBAILhf,EAAOQ,OAAO6M,MAAQrN,EAAOkK,SAAWlK,EAAOQ,OAAO0J,QAAQC,QAChEnK,EAAOuV,QACLvV,EAAOQ,OAAO2V,aAAenW,EAAOkK,QAAQsD,aAC5C,EACAxN,EAAOQ,OAAOmT,oBACd,GACA,GAGF3T,EAAOuV,QAAQvV,EAAOQ,OAAO2V,aAAc,EAAGnW,EAAOQ,OAAOmT,oBAAoB,GAAO,GAIrF3T,EAAOQ,OAAO6M,MAChBrN,EAAO+X,aAIT/X,EAAOyjB,eAEP,IAAIzjB,EAAOrD,GAAGrD,iBAAiB,qBAAqBX,SAAS2oB,IACvDA,EAAQ0G,SACV3G,EAAqBrhB,EAAQshB,GAE7BA,EAAQtoB,iBAAiB,QAASgL,IAChCqd,EAAqBrhB,EAAQgE,EAAExL,OAAO,GAE1C,IAIFwH,EAAO0T,aAAc,EAGrB1T,EAAOuI,KAAK,QACZvI,EAAOuI,KAAK,cA/DkBvI,CAkEhC,CAEA0oB,QAAQC,EAAuBC,QAAT,IAAdD,OAAiB,QAAiB,IAAXC,OAAc,GAC3C,MAAM5oB,EAASzE,MACTiF,OAAEA,EAAM7D,GAAEA,EAAE+D,UAAEA,EAAS2J,OAAEA,GAAWrK,EAE1C,YAA6B,IAAlBA,EAAOQ,QAA0BR,EAAOsH,YAInDtH,EAAOuI,KAAK,iBAGZvI,EAAO0T,aAAc,EAGrB1T,EAAO2jB,eAGHnjB,EAAO6M,MACTrN,EAAOmZ,cAILyP,IACF5oB,EAAOomB,gBACPzpB,EAAG2c,gBAAgB,SACnB5Y,EAAU4Y,gBAAgB,SACtBjP,GAAUA,EAAOxR,QACnBwR,EAAO1R,SAASkJ,IACdA,EAAQQ,UAAU4M,OAChBzO,EAAO0P,kBACP1P,EAAOyR,iBACPzR,EAAO0R,eACP1R,EAAO2R,gBAETtQ,EAAQyX,gBAAgB,SACxBzX,EAAQyX,gBAAgB,0BAA0B,KAKxDtZ,EAAOuI,KAAK,WAGZjQ,OAAOI,KAAKsH,EAAOqH,iBAAiB1O,SAASouB,IAC3C/mB,EAAO2H,IAAIof,EAAU,KAGA,IAAnB4B,IACF3oB,EAAOrD,GAAGqD,OAAS,K/CznBzB,SAAqB5H,GACnB,MAAMywB,EAASzwB,EACfE,OAAOI,KAAKmwB,GAAQlwB,SAASC,IAC3B,IACEiwB,EAAOjwB,GAAO,IAEd,CADA,MAAOoL,GACP,CAEF,WACS6kB,EAAOjwB,EAEd,CADA,MAAOoL,GACP,IAGN,C+C4mBM8kB,CAAY9oB,IAEdA,EAAOsH,WAAY,GA9CV,IAiDX,CAEAyhB,sBAAsBC,GACpBzwB,EAAO8tB,EAAkB2C,EAC3B,CAEW3C,8BACT,OAAOA,CACT,CAEWnE,sBACT,OAAOA,CACT,CAEA6G,qBAAqBpC,GACdL,EAAOnoB,UAAUuoB,cAAaJ,EAAOnoB,UAAUuoB,YAAc,IAClE,MAAMD,EAAUH,EAAOnoB,UAAUuoB,YAEd,mBAARC,GAAsBF,EAAQvnB,QAAQynB,GAAO,GACtDF,EAAQ5iB,KAAK8iB,EAEjB,CAEAoC,WAAWE,GACT,OAAI1mB,MAAMC,QAAQymB,IAChBA,EAAOtwB,SAASuwB,GAAM5C,EAAO6C,cAAcD,KACpC5C,IAETA,EAAO6C,cAAcF,GACd3C,EACT,Ea7pBa,SAAS8C,EAA0BppB,EAAQgkB,EAAgBxjB,EAAQ6oB,GAehF,OAdIrpB,EAAOQ,OAAO6hB,gBAChB/pB,OAAOI,KAAK2wB,GAAY1wB,SAASC,IAC/B,IAAK4H,EAAO5H,KAAwB,IAAhB4H,EAAOwiB,KAAe,CACxC,IAAIhhB,EAAUD,EAAgB/B,EAAOrD,GAAK,IAAG0sB,EAAWzwB,MAAQ,GAC3DoJ,IACHA,EAAUtI,EAAc,MAAO2vB,EAAWzwB,IAC1CoJ,EAAQulB,UAAY8B,EAAWzwB,GAC/BoH,EAAOrD,GAAG+b,OAAO1W,IAEnBxB,EAAO5H,GAAOoJ,EACdgiB,EAAeprB,GAAOoJ,CACxB,KAGGxB,CACT,CClBe,SAAS8oB,GAAkBlnB,GACxC,YAD+C,IAAPA,MAAU,IAC1C,IAAGA,EACRomB,OACAhrB,QAAQ,cAAe,QACvBA,QAAQ,KAAM,MACnB,CCLe,SAAS+rB,GAAYlf,GAClC,MAAMrK,EAASzE,MACTiF,OAAEA,EAAMoJ,SAAEA,GAAa5J,EAEzBQ,EAAO6M,MACTrN,EAAOmZ,cAGT,MAAMqQ,EAAiB3nB,IACrB,GAAuB,iBAAZA,EAAsB,CAC/B,MAAM4nB,EAAU5uB,SAASnB,cAAc,OACvC+vB,EAAQC,UAAY7nB,EACpB+H,EAAS8O,OAAO+Q,EAAQ9vB,SAAS,IACjC8vB,EAAQC,UAAY,EACtB,MACE9f,EAAS8O,OAAO7W,EAClB,EAGF,GAAsB,iBAAXwI,GAAuB,WAAYA,EAC5C,IAAK,IAAIvL,EAAI,EAAGA,EAAIuL,EAAOxR,OAAQiG,GAAK,EAClCuL,EAAOvL,IAAI0qB,EAAcnf,EAAOvL,SAGtC0qB,EAAcnf,GAEhBrK,EAAO2Y,eACHnY,EAAO6M,MACTrN,EAAO+X,aAEJvX,EAAOmpB,WAAY3pB,EAAO4P,WAC7B5P,EAAO6I,QAEX,CCjCe,SAAS+gB,GAAavf,GACnC,MAAMrK,EAASzE,MACTiF,OAAEA,EAAMiP,YAAEA,EAAW7F,SAAEA,GAAa5J,EAEtCQ,EAAO6M,MACTrN,EAAOmZ,cAET,IAAInG,EAAiBvD,EAAc,EACnC,MAAMoa,EAAkBhoB,IACtB,GAAuB,iBAAZA,EAAsB,CAC/B,MAAM4nB,EAAU5uB,SAASnB,cAAc,OACvC+vB,EAAQC,UAAY7nB,EACpB+H,EAAS6O,QAAQgR,EAAQ9vB,SAAS,IAClC8vB,EAAQC,UAAY,EACtB,MACE9f,EAAS6O,QAAQ5W,EACnB,EAEF,GAAsB,iBAAXwI,GAAuB,WAAYA,EAAQ,CACpD,IAAK,IAAIvL,EAAI,EAAGA,EAAIuL,EAAOxR,OAAQiG,GAAK,EAClCuL,EAAOvL,IAAI+qB,EAAexf,EAAOvL,IAEvCkU,EAAiBvD,EAAcpF,EAAOxR,MACxC,MACEgxB,EAAexf,GAEjBrK,EAAO2Y,eACHnY,EAAO6M,MACTrN,EAAO+X,aAEJvX,EAAOmpB,WAAY3pB,EAAO4P,WAC7B5P,EAAO6I,SAET7I,EAAOuV,QAAQvC,EAAgB,GAAG,EACpC,CClCe,SAAS8W,GAAS1hB,EAAOiC,GACtC,MAAMrK,EAASzE,MACTiF,OAAEA,EAAMiP,YAAEA,EAAW7F,SAAEA,GAAa5J,EAC1C,IAAI+pB,EAAoBta,EACpBjP,EAAO6M,OACT0c,GAAqB/pB,EAAO8X,aAC5B9X,EAAOmZ,cACPnZ,EAAO2Y,gBAET,MAAMqR,EAAahqB,EAAOqK,OAAOxR,OACjC,GAAIuP,GAAS,EAEX,YADApI,EAAO4pB,aAAavf,GAGtB,GAAIjC,GAAS4hB,EAEX,YADAhqB,EAAOupB,YAAYlf,GAGrB,IAAI2I,EAAiB+W,EAAoB3hB,EAAQ2hB,EAAoB,EAAIA,EAEzE,MAAME,EAAe,GACrB,IAAK,IAAInrB,EAAIkrB,EAAa,EAAGlrB,GAAKsJ,EAAOtJ,GAAK,EAAG,CAC/C,MAAMorB,EAAelqB,EAAOqK,OAAOvL,GACnCorB,EAAajb,SACbgb,EAAarhB,QAAQshB,EACvB,CAEA,GAAsB,iBAAX7f,GAAuB,WAAYA,EAAQ,CACpD,IAAK,IAAIvL,EAAI,EAAGA,EAAIuL,EAAOxR,OAAQiG,GAAK,EAClCuL,EAAOvL,IAAI8K,EAAS8O,OAAOrO,EAAOvL,IAExCkU,EACE+W,EAAoB3hB,EAAQ2hB,EAAoB1f,EAAOxR,OAASkxB,CACpE,MACEngB,EAAS8O,OAAOrO,GAGlB,IAAK,IAAIvL,EAAI,EAAGA,EAAImrB,EAAapxB,OAAQiG,GAAK,EAC5C8K,EAAS8O,OAAOuR,EAAanrB,IAG/BkB,EAAO2Y,eAEHnY,EAAO6M,MACTrN,EAAO+X,aAEJvX,EAAOmpB,WAAY3pB,EAAO4P,WAC7B5P,EAAO6I,SAELrI,EAAO6M,KACTrN,EAAOuV,QAAQvC,EAAiBhT,EAAO8X,aAAc,GAAG,GAExD9X,EAAOuV,QAAQvC,EAAgB,GAAG,EAEtC,CCtDe,SAASmX,GAAYC,GAClC,MAAMpqB,EAASzE,MACTiF,OAAEA,EAAMiP,YAAEA,GAAgBzP,EAEhC,IAAI+pB,EAAoBta,EACpBjP,EAAO6M,OACT0c,GAAqB/pB,EAAO8X,aAC5B9X,EAAOmZ,eAET,IACIkR,EADArX,EAAiB+W,EAGrB,GAA6B,iBAAlBK,GAA8B,WAAYA,EAAe,CAClE,IAAK,IAAItrB,EAAI,EAAGA,EAAIsrB,EAAcvxB,OAAQiG,GAAK,EAC7CurB,EAAgBD,EAActrB,GAC1BkB,EAAOqK,OAAOggB,IAAgBrqB,EAAOqK,OAAOggB,GAAepb,SAC3Dob,EAAgBrX,IAAgBA,GAAkB,GAExDA,EAAiB7R,KAAKC,IAAI4R,EAAgB,EAC5C,MACEqX,EAAgBD,EACZpqB,EAAOqK,OAAOggB,IAAgBrqB,EAAOqK,OAAOggB,GAAepb,SAC3Dob,EAAgBrX,IAAgBA,GAAkB,GACtDA,EAAiB7R,KAAKC,IAAI4R,EAAgB,GAG5ChT,EAAO2Y,eACHnY,EAAO6M,MACTrN,EAAO+X,aAGJvX,EAAOmpB,WAAY3pB,EAAO4P,WAC7B5P,EAAO6I,SAELrI,EAAO6M,KACTrN,EAAOuV,QAAQvC,EAAiBhT,EAAO8X,aAAc,GAAG,GAExD9X,EAAOuV,QAAQvC,EAAgB,GAAG,EAEtC,CCvCe,SAASsX,KACtB,MAAMtqB,EAASzE,KAET6uB,EAAgB,GACtB,IAAK,IAAItrB,EAAI,EAAGA,EAAIkB,EAAOqK,OAAOxR,OAAQiG,GAAK,EAC7CsrB,EAAcvmB,KAAK/E,GAErBkB,EAAOmqB,YAAYC,EACrB,CCRe,SAASG,GAAW/pB,GACjC,MAAMwM,OACJA,EAAMhN,OACNA,EAAMgH,GACNA,EAAEoN,aACFA,EAAY/E,cACZA,EAAamb,gBACbA,EAAeC,YACfA,EAAWC,gBACXA,EAAeC,gBACfA,GACEnqB,EAwCJ,IAAIoqB,EAtCJ5jB,EAAG,cAAc,KACf,GAAIhH,EAAOQ,OAAOwM,SAAWA,EAAQ,OACrChN,EAAO2lB,WAAW9hB,KAAM,GAAE7D,EAAOQ,OAAOqO,yBAAyB7B,KAC7Dyd,GAAeA,KACjBzqB,EAAO2lB,WAAW9hB,KAAM,GAAE7D,EAAOQ,OAAOqO,4BAG1C,MAAMgc,EAAwBL,EAAkBA,IAAoB,GAEpElyB,OAAO+Q,OAAOrJ,EAAOQ,OAAQqqB,GAC7BvyB,OAAO+Q,OAAOrJ,EAAOgkB,eAAgB6G,EAAsB,IAE7D7jB,EAAG,gBAAgB,KACbhH,EAAOQ,OAAOwM,SAAWA,GAC7BoH,GAAc,IAEhBpN,EAAG,iBAAiB,CAAC8jB,EAAIvqB,KACnBP,EAAOQ,OAAOwM,SAAWA,GAC7BqC,EAAc9O,EAAS,IAGzByG,EAAG,iBAAiB,KAClB,GAAIhH,EAAOQ,OAAOwM,SAAWA,GACzB0d,EAAiB,CACnB,IAAKC,IAAoBA,IAAkBI,aAAc,OAEzD/qB,EAAOqK,OAAO1R,SAASkJ,IACrBA,EACGvI,iBACC,gHAEDX,SAASmJ,GAAaA,EAASmN,UAAS,IAG7Cyb,GACF,KAIF1jB,EAAG,iBAAiB,KACdhH,EAAOQ,OAAOwM,SAAWA,IACxBhN,EAAOqK,OAAOxR,SACjB+xB,GAAyB,GAE3B5uB,uBAAsB,KAChB4uB,GAA0B5qB,EAAOqK,QAAUrK,EAAOqK,OAAOxR,SAC3Dub,IACAwW,GAAyB,EAC3B,IACA,GAEN,CC9De,SAASI,GAAaC,EAAcppB,GACjD,MAAMqpB,EAActpB,EAAoBC,GAKxC,OAJIqpB,IAAgBrpB,IAClBqpB,EAAYrxB,MAAMsxB,mBAAqB,SACvCD,EAAYrxB,MAAM,+BAAiC,UAE9CqxB,CACT,CCPe,SAASE,GAKrBrrB,GAAA,IALgDC,OACjDA,EAAMO,SACNA,EAAQ8qB,kBACRA,EAAiBC,UACjBA,GACDvrB,EACC,MAAM0P,YAAEA,GAAgBzP,EAWxB,GAAIA,EAAOQ,OAAO0T,kBAAiC,IAAb3T,EAAgB,CACpD,IACIgrB,EADAC,GAAiB,EAGnBD,EADED,EACoBD,EAEAA,EAAkBpsB,QAAQisB,IAC9C,MAAMvuB,EAAKuuB,EAAY7oB,UAAU0M,SAAS,0BAjB9BpS,KAChB,IAAKA,EAAGiH,cAKN,OAHc5D,EAAOqK,OAAOpL,QACzB4C,GAAYA,EAAQC,UAAYD,EAAQC,WAAanF,EAAG8uB,aACzD,GAGJ,OAAO9uB,EAAGiH,aAAa,EAUf8nB,CAASR,GACTA,EACJ,OAAOlrB,EAAOuR,cAAc5U,KAAQ8S,CAAW,IAGnD8b,EAAoB5yB,SAASgE,IAC3BmH,EAAqBnH,GAAI,KACvB,GAAI6uB,EAAgB,OACpB,IAAKxrB,GAAUA,EAAOsH,UAAW,OACjCkkB,GAAiB,EACjBxrB,EAAO8U,WAAY,EACnB,MAAM6J,EAAM,IAAIriB,OAAOhB,YAAY,gBAAiB,CAClDsjB,SAAS,EACTd,YAAY,IAEd9d,EAAOU,UAAUme,cAAcF,EAAI,GACnC,GAEN,CACF,CC5Ce,SAASgN,GAAanrB,EAAQqB,EAAS3B,GACpD,MAAM0rB,EAAe,uBAAqB1rB,EAAQ,IAAGA,IAAS,IACxD2rB,EAAkBjqB,EAAoBC,GAC5C,IAAIC,EAAW+pB,EAAgBxyB,cAAe,IAAGuyB,KAMjD,OAJK9pB,IACHA,EAAWpI,EAAc,MAAQ,uBAAqBwG,EAAQ,IAAGA,IAAS,KAC1E2rB,EAAgBnT,OAAO5W,IAElBA,CACT,CvBspBAxJ,OAAOI,KAAKuqB,GAAYtqB,SAASmzB,IAC/BxzB,OAAOI,KAAKuqB,EAAW6I,IAAiBnzB,SAASozB,IAC/CzF,EAAOnoB,UAAU4tB,GAAe9I,EAAW6I,GAAgBC,EAAY,GACvE,IAGJzF,EAAO0F,IAAI,CwBtqBI,SAAsCjsB,GAAA,IAAtBC,OAAEA,EAAMgH,GAAEA,EAAEuB,KAAEA,GAAMxI,EACjD,MAAMzD,EAASF,IACf,IAAIutB,EAAW,KACXsC,EAAiB,KAErB,MAAMC,EAAgB,KACflsB,IAAUA,EAAOsH,WAActH,EAAO0T,cAC3CnL,EAAK,gBACLA,EAAK,UAAS,EAqCV4jB,EAA2B,KAC1BnsB,IAAUA,EAAOsH,WAActH,EAAO0T,aAC3CnL,EAAK,oBAAoB,EAG3BvB,EAAG,QAAQ,KACLhH,EAAOQ,OAAO4hB,qBAAmD,IAA1B9lB,EAAO8vB,eAvC7CpsB,IAAUA,EAAOsH,WAActH,EAAO0T,cAC3CiW,EAAW,IAAIyC,gBAAgBvG,IAC7BoG,EAAiB3vB,EAAON,uBAAsB,KAC5C,MAAMuJ,MAAEA,EAAKE,OAAEA,GAAWzF,EAC1B,IAAIqsB,EAAW9mB,EACX6J,EAAY3J,EAChBogB,EAAQltB,SAAQ2zB,IAA6C,IAA5CC,eAAEA,EAAcC,YAAEA,EAAWh0B,OAAEA,GAAQ8zB,EAClD9zB,GAAUA,IAAWwH,EAAOrD,KAChC0vB,EAAWG,EACPA,EAAYjnB,OACXgnB,EAAe,IAAMA,GAAgBE,WAC1Crd,EAAYod,EACRA,EAAY/mB,QACX8mB,EAAe,IAAMA,GAAgBG,UAAS,IAEjDL,IAAa9mB,GAAS6J,IAAc3J,GACtCymB,GACF,GACA,IAEJvC,EAASgD,QAAQ3sB,EAAOrD,MAuBxBL,EAAOtD,iBAAiB,SAAUkzB,GAClC5vB,EAAOtD,iBAAiB,oBAAqBmzB,GAAyB,IAGxEnlB,EAAG,WAAW,KAvBRilB,GACF3vB,EAAOJ,qBAAqB+vB,GAE1BtC,GAAYA,EAASiD,WAAa5sB,EAAOrD,KAC3CgtB,EAASiD,UAAU5sB,EAAOrD,IAC1BgtB,EAAW,MAoBbrtB,EAAOrD,oBAAoB,SAAUizB,GACrC5vB,EAAOrD,oBAAoB,oBAAqBkzB,EAAyB,GAE7E,EC/De,SAAsDpsB,GAAA,IAApCC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,EAAEuB,KAAEA,GAAMxI,EACjE,MAAM8sB,EAAY,GACZvwB,EAASF,IACT0wB,EAAS,SAACt0B,EAAQu0B,QAAO,IAAPA,MAAU,IAChC,MACMpD,EAAW,IADIrtB,EAAO0wB,kBAAoB1wB,EAAO2wB,yBACpBC,IAIjC,GAAIltB,EAAOsjB,oBAAqB,OAChC,GAAyB,IAArB4J,EAAUr0B,OAEZ,YADA0P,EAAK,iBAAkB2kB,EAAU,IAInC,MAAMC,EAAiB,WACrB5kB,EAAK,iBAAkB2kB,EAAU,G,EAG/B5wB,EAAON,sBACTM,EAAON,sBAAsBmxB,GAE7B7wB,EAAOT,WAAWsxB,EAAgB,EACpC,IAGFxD,EAASgD,QAAQn0B,EAAQ,CACvB40B,gBAA0C,IAAvBL,EAAQK,YAAoCL,EAAQK,WACvEC,eAAwC,IAAtBN,EAAQM,WAAmCN,EAAQM,UACrEC,mBAAgD,IAA1BP,EAAQO,eAAuCP,EAAQO,gBAG/ET,EAAUhpB,KAAK8lB,E,EAyBjB/C,EAAa,CACX+C,UAAU,EACV4D,gBAAgB,EAChBC,sBAAsB,IAExBxmB,EAAG,QA5BU,KACX,GAAKhH,EAAOQ,OAAOmpB,SAAnB,CACA,GAAI3pB,EAAOQ,OAAO+sB,eAAgB,CAChC,MAAME,EAAmBhqB,EAAezD,EAAOrD,IAC/C,IAAK,IAAImC,EAAI,EAAGA,EAAI2uB,EAAiB50B,OAAQiG,GAAK,EAChDguB,EAAOW,EAAiB3uB,GAE5B,CAEAguB,EAAO9sB,EAAOrD,GAAI,CAChB0wB,UAAWrtB,EAAOQ,OAAOgtB,uBAI3BV,EAAO9sB,EAAOU,UAAW,CAAE0sB,YAAY,GAbV,CAakB,IAejDpmB,EAAG,WAba,KACd6lB,EAAUl0B,SAASgxB,IACjBA,EAAS+D,YAAY,IAEvBb,EAAUxkB,OAAO,EAAGwkB,EAAUh0B,OAAO,GAUzC,IC9DA,MAAM4tB,GAAU,CCFD,SAAqD1mB,GAAA,IAc9D4tB,GAd0B3tB,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,EAAEuB,KAAEA,GAAMxI,EAChE6mB,EAAa,CACX1c,QAAS,CACPC,SAAS,EACTE,OAAQ,GACRujB,OAAO,EACPC,YAAa,KACbC,eAAgB,KAChBC,sBAAsB,EACtBC,gBAAiB,EACjBC,eAAgB,KAKpB,MAAMpzB,EAAWF,IAEjBqF,EAAOkK,QAAU,CACf0jB,MAAO,GACPM,UAAMtvB,EACNF,QAAIE,EACJyL,OAAQ,GACR8jB,OAAQ,EACR1jB,WAAY,IAGd,MAAMgf,EAAU5uB,EAASnB,cAAc,OAEvC,SAASm0B,EAAY3hB,EAAO9D,GAC1B,MAAM5H,EAASR,EAAOQ,OAAO0J,QAC7B,GAAI1J,EAAOotB,OAAS5tB,EAAOkK,QAAQ0jB,MAAMxlB,GACvC,OAAOpI,EAAOkK,QAAQ0jB,MAAMxlB,GAG9B,IAAIvG,EAkBJ,OAjBIrB,EAAOqtB,aACThsB,EAAUrB,EAAOqtB,YAAYzvB,KAAK4B,EAAQkM,EAAO9D,GAC1B,iBAAZvG,IACT4nB,EAAQC,UAAY7nB,EACpBA,EAAU4nB,EAAQ9vB,SAAS,KAG7BkI,EADS7B,EAAO4P,UACNlW,EAAc,gBAEdA,EAAc,MAAOsG,EAAOQ,OAAO8J,YAE/CzI,EAAQ/H,aAAa,0BAA2BsO,GAC3C5H,EAAOqtB,cACVhsB,EAAQ6nB,UAAYxd,GAGlB1L,EAAOotB,QAAO5tB,EAAOkK,QAAQ0jB,MAAMxlB,GAASvG,GACzCA,CACT,CAEA,SAASgH,EAAOulB,GACd,MAAMpiB,cAAEA,EAAac,eAAEA,EAActB,eAAEA,EAAgB6B,KAAMoR,GAAWze,EAAOQ,QACzEwtB,gBAAEA,EAAeC,eAAEA,GAAmBjuB,EAAOQ,OAAO0J,SAExDgkB,KAAMG,EACN3vB,GAAI4vB,EAAUjkB,OACdA,EACAI,WAAY8jB,EACZJ,OAAQK,GACNxuB,EAAOkK,QACNlK,EAAOQ,OAAOiL,SACjBzL,EAAO+S,oBAGT,MAAMtD,EAAczP,EAAOyP,aAAe,EAE1C,IAAIgf,EAIAhhB,EACAD,EAJqBihB,EAArBzuB,EAAO8J,aAA2B,QACpB9J,EAAOiJ,eAAiB,OAAS,MAI/CuC,GACFiC,EAActM,KAAKwL,MAAMX,EAAgB,GAAKc,EAAiBmhB,EAC/DzgB,EAAerM,KAAKwL,MAAMX,EAAgB,GAAKc,EAAiBkhB,IAEhEvgB,EAAczB,GAAiBc,EAAiB,GAAKmhB,EACrDzgB,GAAgBiR,EAASzS,EAAgBc,GAAkBkhB,GAE7D,IAAIE,EAAOze,EAAcjC,EACrB9O,EAAK+Q,EAAchC,EAClBgR,IACHyP,EAAO/sB,KAAKC,IAAI8sB,EAAM,GACtBxvB,EAAKyC,KAAKE,IAAI3C,EAAI2L,EAAOxR,OAAS,IAEpC,IAAIs1B,GAAUnuB,EAAOyK,WAAWyjB,IAAS,IAAMluB,EAAOyK,WAAW,IAAM,GAkBvE,SAASikB,IACP1uB,EAAOsJ,eACPtJ,EAAO2Q,iBACP3Q,EAAO8R,sBACPvJ,EAAK,gBACP,CAEA,GAxBIkW,GAAUhP,GAAejC,GAC3B0gB,GAAQ1gB,EACHhC,IAAgB2iB,GAAUnuB,EAAOyK,WAAW,KACxCgU,GAAUhP,EAAcjC,IACjC0gB,GAAQ1gB,EACJhC,IAAgB2iB,GAAUnuB,EAAOyK,WAAW,KAGlDnS,OAAO+Q,OAAOrJ,EAAOkK,QAAS,CAC5BgkB,OACAxvB,KACAyvB,SACA1jB,WAAYzK,EAAOyK,WACnB+C,eACAC,gBAUE4gB,IAAiBH,GAAQI,IAAe5vB,IAAO0vB,EAQjD,OAPIpuB,EAAOyK,aAAe8jB,GAAsBJ,IAAWK,GACzDxuB,EAAOqK,OAAO1R,SAASkJ,IACrBA,EAAQhI,MAAM40B,GAAe,GAAEN,KAAU,IAG7CnuB,EAAO2Q,sBACPpI,EAAK,iBAGP,GAAIvI,EAAOQ,OAAO0J,QAAQ4jB,eAkBxB,OAjBA9tB,EAAOQ,OAAO0J,QAAQ4jB,eAAe1vB,KAAK4B,EAAQ,CAChDmuB,SACAD,OACAxvB,KACA2L,OAAS,WACP,MAAMskB,EAAiB,GACvB,IAAK,IAAI7vB,EAAIovB,EAAMpvB,GAAKJ,EAAII,GAAK,EAC/B6vB,EAAe9qB,KAAKwG,EAAOvL,IAE7B,OAAO6vB,C,CALA,UAQP3uB,EAAOQ,OAAO0J,QAAQ6jB,qBACxBW,IAEAnmB,EAAK,kBAIT,MAAMqmB,EAAiB,GACjBC,EAAgB,GAEhBtd,EAAiBnJ,IACrB,IAAIwF,EAAaxF,EAOjB,OANIA,EAAQ,EACVwF,EAAavD,EAAOxR,OAASuP,EACpBwF,GAAcvD,EAAOxR,SAE9B+U,GAA0BvD,EAAOxR,QAE5B+U,CAAU,EAGnB,GAAIwgB,EACFpuB,EAAO4J,SACJtQ,iBAAkB,IAAG0G,EAAOQ,OAAO8J,4BACnC3R,SAASkJ,IACRA,EAAQoN,QAAQ,SAGpB,IAAK,IAAInQ,EAAIuvB,EAAcvvB,GAAKwvB,EAAYxvB,GAAK,EAC/C,GAAIA,EAAIovB,GAAQpvB,EAAIJ,EAAI,CACtB,MAAMkP,EAAa2D,EAAczS,GACjCkB,EAAO4J,SACJtQ,iBACE,IAAG0G,EAAOQ,OAAO8J,uCAAuCsD,8CAAuDA,OAEjHjV,SAASkJ,IACRA,EAAQoN,QAAQ,GAEtB,CAIJ,MAAM6f,EAAWrQ,GAAUpU,EAAOxR,OAAS,EACrCk2B,EAAStQ,EAAyB,EAAhBpU,EAAOxR,OAAawR,EAAOxR,OACnD,IAAK,IAAIiG,EAAIgwB,EAAUhwB,EAAIiwB,EAAQjwB,GAAK,EACtC,GAAIA,GAAKovB,GAAQpvB,GAAKJ,EAAI,CACxB,MAAMkP,EAAa2D,EAAczS,QACP,IAAfwvB,GAA8BF,EACvCS,EAAchrB,KAAK+J,IAEf9O,EAAIwvB,GAAYO,EAAchrB,KAAK+J,GACnC9O,EAAIuvB,GAAcO,EAAe/qB,KAAK+J,GAE9C,CAKF,GAHAihB,EAAcl2B,SAASyP,IACrBpI,EAAO4J,SAAS8O,OAAOmV,EAAYxjB,EAAOjC,GAAQA,GAAO,IAEvDqW,EACF,IAAK,IAAI3f,EAAI8vB,EAAe/1B,OAAS,EAAGiG,GAAK,EAAGA,GAAK,EAAG,CACtD,MAAMsJ,EAAQwmB,EAAe9vB,GAC7BkB,EAAO4J,SAAS6O,QAAQoV,EAAYxjB,EAAOjC,GAAQA,GACrD,MAEAwmB,EAAetJ,MAAK,CAAC/nB,EAAGgoB,IAAMA,EAAIhoB,IAClCqxB,EAAej2B,SAASyP,IACtBpI,EAAO4J,SAAS6O,QAAQoV,EAAYxjB,EAAOjC,GAAQA,GAAO,IAG9DrG,EAAgB/B,EAAO4J,SAAU,+BAA+BjR,SAASkJ,IACvEA,EAAQhI,MAAM40B,GAAe,GAAEN,KAAU,IAE3CO,GACF,CA6EA1nB,EAAG,cAAc,KACf,IAAKhH,EAAOQ,OAAO0J,QAAQC,QAAS,OACpC,IAAI6kB,EACJ,QAAkD,IAAvChvB,EAAO8mB,aAAa5c,QAAQG,OAAwB,CAC7D,MAAMA,EAAS,IAAIrK,EAAO4J,SAASjQ,UAAUsF,QAAQtC,GACnDA,EAAGuF,QAAS,IAAGlC,EAAOQ,OAAO8J,8BAE3BD,GAAUA,EAAOxR,SACnBmH,EAAOkK,QAAQG,OAAS,IAAIA,GAC5B2kB,GAAoB,EACpB3kB,EAAO1R,SAAQ,CAACkJ,EAAS+L,KACvB/L,EAAQ/H,aAAa,0BAA2B8T,GAChD5N,EAAOkK,QAAQ0jB,MAAMhgB,GAAc/L,EACnCA,EAAQoN,QAAQ,IAGtB,CACK+f,IACHhvB,EAAOkK,QAAQG,OAASrK,EAAOQ,OAAO0J,QAAQG,QAGhDrK,EAAO2lB,WAAW9hB,KAAM,GAAE7D,EAAOQ,OAAOqO,iCAExC7O,EAAOQ,OAAOkO,qBAAsB,EACpC1O,EAAOgkB,eAAetV,qBAAsB,EAEvC1O,EAAOQ,OAAO2V,cACjBtN,GACF,IAEF7B,EAAG,gBAAgB,KACZhH,EAAOQ,OAAO0J,QAAQC,UACvBnK,EAAOQ,OAAOiL,UAAYzL,EAAOiW,mBACnCna,aAAa6xB,GACbA,EAAiB9xB,YAAW,KAC1BgN,GAAQ,GACP,MAEHA,IACF,IAEF7B,EAAG,sBAAsB,KAClBhH,EAAOQ,OAAO0J,QAAQC,SACvBnK,EAAOQ,OAAOiL,SAChB/L,EAAeM,EAAOU,UAAW,wBAA0B,GAAEV,EAAOoL,gBACtE,IAGF9S,OAAO+Q,OAAOrJ,EAAOkK,QAAS,CAC5Bqf,YA5HF,SAAqBlf,GACnB,GAAsB,iBAAXA,GAAuB,WAAYA,EAC5C,IAAK,IAAIvL,EAAI,EAAGA,EAAIuL,EAAOxR,OAAQiG,GAAK,EAClCuL,EAAOvL,IAAIkB,EAAOkK,QAAQG,OAAOxG,KAAKwG,EAAOvL,SAGnDkB,EAAOkK,QAAQG,OAAOxG,KAAKwG,GAE7BxB,GAAO,EACT,EAoHE+gB,aAnHF,SAAsBvf,GACpB,MAAMoF,EAAczP,EAAOyP,YAC3B,IAAIuD,EAAiBvD,EAAc,EAC/Bwf,EAAoB,EAExB,GAAI1sB,MAAMC,QAAQ6H,GAAS,CACzB,IAAK,IAAIvL,EAAI,EAAGA,EAAIuL,EAAOxR,OAAQiG,GAAK,EAClCuL,EAAOvL,IAAIkB,EAAOkK,QAAQG,OAAOzB,QAAQyB,EAAOvL,IAEtDkU,EAAiBvD,EAAcpF,EAAOxR,OACtCo2B,EAAoB5kB,EAAOxR,MAC7B,MACEmH,EAAOkK,QAAQG,OAAOzB,QAAQyB,GAEhC,GAAIrK,EAAOQ,OAAO0J,QAAQ0jB,MAAO,CAC/B,MAAMA,EAAQ5tB,EAAOkK,QAAQ0jB,MACvBsB,EAAW,GACjB52B,OAAOI,KAAKk1B,GAAOj1B,SAASw2B,IAC1B,MAAMC,EAAWxB,EAAMuB,GACjBE,EAAgBD,EAAS7f,aAAa,2BACxC8f,GACFD,EAASt1B,aACP,0BACAqP,SAASkmB,EAAe,IAAMJ,GAGlCC,EAAS/lB,SAASgmB,EAAa,IAAMF,GAAqBG,CAAQ,IAEpEpvB,EAAOkK,QAAQ0jB,MAAQsB,CACzB,CACArmB,GAAO,GACP7I,EAAOuV,QAAQvC,EAAgB,EACjC,EAoFEmX,YAnFF,SAAqBC,GACnB,GAAI,MAAOA,EAAyD,OACpE,IAAI3a,EAAczP,EAAOyP,YACzB,GAAIlN,MAAMC,QAAQ4nB,GAChB,IAAK,IAAItrB,EAAIsrB,EAAcvxB,OAAS,EAAGiG,GAAK,EAAGA,GAAK,EAClDkB,EAAOkK,QAAQG,OAAOhC,OAAO+hB,EAActrB,GAAI,GAC3CkB,EAAOQ,OAAO0J,QAAQ0jB,cACjB5tB,EAAOkK,QAAQ0jB,MAAMxD,EAActrB,IAExCsrB,EAActrB,GAAK2Q,IAAaA,GAAe,GACnDA,EAActO,KAAKC,IAAIqO,EAAa,QAGtCzP,EAAOkK,QAAQG,OAAOhC,OAAO+hB,EAAe,GACxCpqB,EAAOQ,OAAO0J,QAAQ0jB,cACjB5tB,EAAOkK,QAAQ0jB,MAAMxD,GAE1BA,EAAgB3a,IAAaA,GAAe,GAChDA,EAActO,KAAKC,IAAIqO,EAAa,GAEtC5G,GAAO,GACP7I,EAAOuV,QAAQ9F,EAAa,EAC9B,EA8DE6a,gBA7DF,WACEtqB,EAAOkK,QAAQG,OAAS,GACpBrK,EAAOQ,OAAO0J,QAAQ0jB,QACxB5tB,EAAOkK,QAAQ0jB,MAAQ,IAEzB/kB,GAAO,GACP7I,EAAOuV,QAAQ,EAAG,EACpB,EAuDE1M,UAEJ,ECtVe,SAAsD9I,GAAA,IAApCC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,EAAEuB,KAAEA,GAAMxI,EACjE,MAAMlF,EAAWF,IACX2B,EAASF,IAYf,SAASkzB,EAAO9nB,GACd,IAAKxH,EAAOmK,QAAS,OAErB,MAAQL,aAAcC,GAAQ/J,EAC9B,IAAIgE,EAAIwD,EACJxD,EAAE4V,gBAAe5V,EAAIA,EAAE4V,eAC3B,MAAM2V,EAAKvrB,EAAEwrB,SAAWxrB,EAAEyrB,SACpBC,EAAa1vB,EAAOQ,OAAOmvB,SAASD,WACpCE,EAAWF,GAAqB,KAAPH,EACzBM,EAAaH,GAAqB,KAAPH,EAC3BO,EAAqB,KAAPP,EACdQ,EAAsB,KAAPR,EACfS,EAAmB,KAAPT,EACZU,EAAqB,KAAPV,EAEpB,IACGvvB,EAAO4V,iBACN5V,EAAOiJ,gBAAkB8mB,GACxB/vB,EAAOkJ,cAAgB+mB,GACxBJ,GAEF,OAAO,EAET,IACG7vB,EAAO6V,iBACN7V,EAAOiJ,gBAAkB6mB,GAAiB9vB,EAAOkJ,cAAgB8mB,GAAcJ,GAEjF,OAAO,EAET,KAAI5rB,EAAEksB,UAAYlsB,EAAEmsB,QAAUnsB,EAAEosB,SAAWpsB,EAAEqsB,SAI3Cx1B,EAAS3B,eACT2B,EAAS3B,cAAcE,WAC4B,UAAlDyB,EAAS3B,cAAcE,SAASiN,eACmB,aAAlDxL,EAAS3B,cAAcE,SAASiN,gBAJpC,CAQA,GACErG,EAAOQ,OAAOmvB,SAASW,iBACtBV,GAAYC,GAAcC,GAAeC,GAAgBC,GAAaC,GACvE,CACA,IAAIM,GAAS,EAEb,GACE9sB,EAAezD,EAAOrD,GAAK,IAAGqD,EAAOQ,OAAO8J,4BAA4BzR,OAAS,GACN,IAA3E4K,EAAezD,EAAOrD,GAAK,IAAGqD,EAAOQ,OAAOyR,oBAAoBpZ,OAEhE,OAGF,MAAM8D,EAAKqD,EAAOrD,GACZ6zB,EAAc7zB,EAAGoM,YACjB0nB,EAAe9zB,EAAGqM,aAClB0nB,EAAcp0B,EAAOwf,WACrB6U,EAAer0B,EAAO0oB,YACtB4L,EAAenuB,EAAc9F,GAC/BoN,IAAK6mB,EAAaztB,MAAQxG,EAAGqG,YACjC,MAAM6tB,EAAc,CAClB,CAACD,EAAaztB,KAAMytB,EAAa1tB,KACjC,CAAC0tB,EAAaztB,KAAOqtB,EAAaI,EAAa1tB,KAC/C,CAAC0tB,EAAaztB,KAAMytB,EAAa1tB,IAAMutB,GACvC,CAACG,EAAaztB,KAAOqtB,EAAaI,EAAa1tB,IAAMutB,IAEvD,IAAK,IAAI3xB,EAAI,EAAGA,EAAI+xB,EAAYh4B,OAAQiG,GAAK,EAAG,CAC9C,MAAMomB,EAAQ2L,EAAY/xB,GAC1B,GAAIomB,EAAM,IAAM,GAAKA,EAAM,IAAMwL,GAAexL,EAAM,IAAM,GAAKA,EAAM,IAAMyL,EAAc,CACzF,GAAiB,IAAbzL,EAAM,IAAyB,IAAbA,EAAM,GAAU,SACtCqL,GAAS,CACX,CACF,CACA,IAAKA,EAAQ,MACf,CACIvwB,EAAOiJ,iBACL2mB,GAAYC,GAAcC,GAAeC,KACvC/rB,EAAE+X,eAAgB/X,EAAE+X,iBACnB/X,EAAE8sB,aAAc,KAEjBjB,GAAcE,KAAkBhmB,IAAU6lB,GAAYE,IAAgB/lB,IAC1E/J,EAAOuW,cACHqZ,GAAYE,KAAiB/lB,IAAU8lB,GAAcE,IAAiBhmB,IAC1E/J,EAAOgX,eAEL4Y,GAAYC,GAAcG,GAAaC,KACrCjsB,EAAE+X,eAAgB/X,EAAE+X,iBACnB/X,EAAE8sB,aAAc,IAEnBjB,GAAcI,IAAajwB,EAAOuW,aAClCqZ,GAAYI,IAAWhwB,EAAOgX,aAEpCzO,EAAK,WAAYgnB,EArDjB,CAuDF,CACA,SAAS9K,IACHzkB,EAAO2vB,SAASxlB,UACpBtP,EAAS7B,iBAAiB,UAAWs2B,GACrCtvB,EAAO2vB,SAASxlB,SAAU,EAC5B,CACA,SAASqa,IACFxkB,EAAO2vB,SAASxlB,UACrBtP,EAAS5B,oBAAoB,UAAWq2B,GACxCtvB,EAAO2vB,SAASxlB,SAAU,EAC5B,CAnHAnK,EAAO2vB,SAAW,CAChBxlB,SAAS,GAEXyc,EAAa,CACX+I,SAAU,CACRxlB,SAAS,EACTmmB,gBAAgB,EAChBZ,YAAY,KA8GhB1oB,EAAG,QAAQ,KACLhH,EAAOQ,OAAOmvB,SAASxlB,SACzBsa,GACF,IAEFzd,EAAG,WAAW,KACRhH,EAAO2vB,SAASxlB,SAClBqa,GACF,IAGFlsB,OAAO+Q,OAAOrJ,EAAO2vB,SAAU,CAC7BlL,SACAD,WAEJ,ECvIe,SAAwDzkB,GAAA,IAApCC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,EAAEuB,KAAEA,GAAMxI,EACnE,MAAMzD,EAASF,IAmBf,IAAIokB,EAjBJoG,EAAa,CACXmK,WAAY,CACV5mB,SAAS,EACT6mB,gBAAgB,EAChBC,QAAQ,EACRC,aAAa,EACbC,YAAa,EACbC,aAAc,YACdC,eAAgB,KAChBC,cAAe,QAInBtxB,EAAO+wB,WAAa,CAClB5mB,SAAS,GAIX,IACIonB,EADAC,EAAiB/0B,IAErB,MAAMg1B,EAAoB,GA4E1B,SAASC,IACF1xB,EAAOmK,UACZnK,EAAO2xB,cAAe,EACxB,CACA,SAASC,IACF5xB,EAAOmK,UACZnK,EAAO2xB,cAAe,EACxB,CACA,SAASE,EAAcC,GACrB,QACE9xB,EAAOQ,OAAOuwB,WAAWM,gBACzBS,EAASC,MAAQ/xB,EAAOQ,OAAOuwB,WAAWM,oBAO1CrxB,EAAOQ,OAAOuwB,WAAWO,eACzB70B,IAAQ+0B,EAAiBxxB,EAAOQ,OAAOuwB,WAAWO,iBAShDQ,EAASC,OAAS,GAAKt1B,IAAQ+0B,EAAiB,KAgBhDM,EAASzc,UAAY,EACjBrV,EAAOgR,QAAShR,EAAOQ,OAAO6M,MAAUrN,EAAO8U,YACnD9U,EAAOuW,YACPhO,EAAK,SAAUupB,EAASE,MAEfhyB,EAAO+Q,cAAe/Q,EAAOQ,OAAO6M,MAAUrN,EAAO8U,YAChE9U,EAAOgX,YACPzO,EAAK,SAAUupB,EAASE,MAG1BR,GAAiB,IAAIl1B,EAAOX,MAAOsF,WAE5B,IACT,CAcA,SAASquB,EAAO9nB,GACd,IAAIxD,EAAIwD,EACJ0X,GAAsB,EAC1B,IAAKlf,EAAOmK,QAAS,OACrB,MAAM3J,EAASR,EAAOQ,OAAOuwB,WAEzB/wB,EAAOQ,OAAOiL,SAChBzH,EAAE+X,iBAGJ,IAAIlC,EAAW7Z,EAAOrD,GACwB,cAA1CqD,EAAOQ,OAAOuwB,WAAWK,eAC3BvX,EAAWhf,SAASxB,cAAc2G,EAAOQ,OAAOuwB,WAAWK,eAE7D,MAAMa,EAAyBpY,GAAYA,EAAS9K,SAAS/K,EAAExL,QAC/D,IAAKwH,EAAO2xB,eAAiBM,IAA2BzxB,EAAOwwB,eAAgB,OAAO,EAElFhtB,EAAE4V,gBAAe5V,EAAIA,EAAE4V,eAC3B,IAAImY,EAAQ,EACZ,MAAMG,EAAYlyB,EAAO8J,cAAgB,EAAI,EAEvCtB,EAtKR,SAAmBxE,GAMjB,IAAImuB,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EA0DT,MAvDI,WAAYtuB,IACdouB,EAAKpuB,EAAEuuB,QAEL,eAAgBvuB,IAClBouB,GAAMpuB,EAAEwuB,WAAa,KAEnB,gBAAiBxuB,IACnBouB,GAAMpuB,EAAEyuB,YAAc,KAEpB,gBAAiBzuB,IACnBmuB,GAAMnuB,EAAE0uB,YAAc,KAIpB,SAAU1uB,GAAKA,EAAEpH,OAASoH,EAAE2uB,kBAC9BR,EAAKC,EACLA,EAAK,GAGPC,EA7BmB,GA6BdF,EACLG,EA9BmB,GA8BdF,EAED,WAAYpuB,IACdsuB,EAAKtuB,EAAE4uB,QAEL,WAAY5uB,IACdquB,EAAKruB,EAAE6uB,QAGL7uB,EAAEksB,WAAamC,IAEjBA,EAAKC,EACLA,EAAK,IAGFD,GAAMC,IAAOtuB,EAAE8uB,YACE,IAAhB9uB,EAAE8uB,WAEJT,GA/CgB,GAgDhBC,GAhDgB,KAmDhBD,GAlDgB,IAmDhBC,GAnDgB,MAwDhBD,IAAOF,IACTA,EAAKE,EAAK,GAAK,EAAI,GAEjBC,IAAOF,IACTA,EAAKE,EAAK,GAAK,EAAI,GAGd,CACLS,MAAOZ,EACPa,MAAOZ,EACPa,OAAQZ,EACRa,OAAQZ,EAEZ,CA6Ferb,CAAUjT,GAEvB,GAAIxD,EAAO0wB,YACT,GAAIlxB,EAAOiJ,eAAgB,CACzB,KAAI9H,KAAK0L,IAAIrE,EAAKyqB,QAAU9xB,KAAK0L,IAAIrE,EAAK0qB,SACrC,OAAO,EADuCnB,GAASvpB,EAAKyqB,OAASf,CAE5E,KAAO,MAAI/wB,KAAK0L,IAAIrE,EAAK0qB,QAAU/xB,KAAK0L,IAAIrE,EAAKyqB,SAC5C,OAAO,EAD8ClB,GAASvpB,EAAK0qB,MACxD,MAEhBnB,EACE5wB,KAAK0L,IAAIrE,EAAKyqB,QAAU9xB,KAAK0L,IAAIrE,EAAK0qB,SAAW1qB,EAAKyqB,OAASf,GAAa1pB,EAAK0qB,OAGrF,GAAc,IAAVnB,EAAa,OAAO,EAEpBvxB,EAAOywB,SAAQc,GAASA,GAG5B,IAAIoB,EAAYnzB,EAAOtD,eAAiBq1B,EAAQvxB,EAAO2wB,YAkBvD,GAhBIgC,GAAanzB,EAAOsQ,iBAAgB6iB,EAAYnzB,EAAOsQ,gBACvD6iB,GAAanzB,EAAO8Q,iBAAgBqiB,EAAYnzB,EAAO8Q,gBAS3DoO,IAAsBlf,EAAOQ,OAAO6M,QAE9B8lB,IAAcnzB,EAAOsQ,gBAAkB6iB,IAAcnzB,EAAO8Q,gBAE9DoO,GAAuBlf,EAAOQ,OAAOwd,QAAQha,EAAEia,kBAE9Cje,EAAOQ,OAAOoc,UAAa5c,EAAOQ,OAAOoc,SAASzS,QAyChD,CAOL,MAAM2nB,EAAW,CACfzxB,KAAM5D,IACNs1B,MAAO5wB,KAAK0L,IAAIklB,GAChB1c,UAAWlU,KAAKiyB,KAAKrB,IAGjBsB,EACJ9B,GACAO,EAASzxB,KAAOkxB,EAAoBlxB,KAAO,KAC3CyxB,EAASC,OAASR,EAAoBQ,OACtCD,EAASzc,YAAckc,EAAoBlc,UAC7C,IAAKge,EAAmB,CACtB9B,OAAsB3yB,EAEtB,IAAI00B,EAAWtzB,EAAOtD,eAAiBq1B,EAAQvxB,EAAO2wB,YACtD,MAAMjgB,EAAelR,EAAO+Q,YACtBI,EAASnR,EAAOgR,MAqBtB,GAnBIsiB,GAAYtzB,EAAOsQ,iBAAgBgjB,EAAWtzB,EAAOsQ,gBACrDgjB,GAAYtzB,EAAO8Q,iBAAgBwiB,EAAWtzB,EAAO8Q,gBAEzD9Q,EAAOqP,cAAc,GACrBrP,EAAOoU,aAAakf,GACpBtzB,EAAO2Q,iBACP3Q,EAAO+S,oBACP/S,EAAO8R,wBAEDZ,GAAgBlR,EAAO+Q,cAAkBI,GAAUnR,EAAOgR,QAC9DhR,EAAO8R,sBAEL9R,EAAOQ,OAAO6M,MAChBrN,EAAO6W,QAAQ,CACbxB,UAAWyc,EAASzc,UAAY,EAAI,OAAS,OAC7C6C,cAAc,IAIdlY,EAAOQ,OAAOoc,SAAS2W,OAAQ,CAYjCz3B,aAAa0kB,GACbA,OAAU5hB,EACN6yB,EAAkB54B,QAAU,IAC9B44B,EAAkB+B,QAEpB,MAAMC,EAAYhC,EAAkB54B,OAChC44B,EAAkBA,EAAkB54B,OAAS,QAC7C+F,EACE80B,EAAajC,EAAkB,GAErC,GADAA,EAAkB5tB,KAAKiuB,GAErB2B,IACC3B,EAASC,MAAQ0B,EAAU1B,OAASD,EAASzc,YAAcoe,EAAUpe,WAGtEoc,EAAkBppB,OAAO,QACpB,GACLopB,EAAkB54B,QAAU,IAC5Bi5B,EAASzxB,KAAOqzB,EAAWrzB,KAAO,KAClCqzB,EAAW3B,MAAQD,EAASC,OAAS,GACrCD,EAASC,OAAS,EAClB,CAOA,MAAM4B,EAAkB5B,EAAQ,EAAI,GAAM,GAC1CR,EAAsBO,EACtBL,EAAkBppB,OAAO,GACzBmY,EAAUjkB,GAAS,KACjByD,EAAOyX,eAAezX,EAAOQ,OAAOC,OAAO,OAAM7B,EAAW+0B,EAAgB,GAC3E,EACL,CACKnT,IAIHA,EAAUjkB,GAAS,KAEjBg1B,EAAsBO,EACtBL,EAAkBppB,OAAO,GACzBrI,EAAOyX,eAAezX,EAAOQ,OAAOC,OAAO,OAAM7B,EAHzB,GAGoD,GAC3E,KAEP,CASA,GANKy0B,GAAmB9qB,EAAK,SAAUvE,GAGnChE,EAAOQ,OAAOogB,UAAY5gB,EAAOQ,OAAOozB,8BAC1C5zB,EAAO4gB,SAASiT,OAEdP,IAAatzB,EAAOsQ,gBAAkBgjB,IAAatzB,EAAO8Q,eAAgB,OAAO,CACvF,CACF,KA1JgE,CAE9D,MAAMghB,EAAW,CACfzxB,KAAM5D,IACNs1B,MAAO5wB,KAAK0L,IAAIklB,GAChB1c,UAAWlU,KAAKiyB,KAAKrB,GACrBC,IAAKxqB,GAIHiqB,EAAkB54B,QAAU,GAC9B44B,EAAkB+B,QAEpB,MAAMC,EAAYhC,EAAkB54B,OAChC44B,EAAkBA,EAAkB54B,OAAS,QAC7C+F,EAuBJ,GAtBA6yB,EAAkB5tB,KAAKiuB,GAQnB2B,GAEA3B,EAASzc,YAAcoe,EAAUpe,WACjCyc,EAASC,MAAQ0B,EAAU1B,OAC3BD,EAASzxB,KAAOozB,EAAUpzB,KAAO,MAEjCwxB,EAAcC,GAGhBD,EAAcC,GAvGpB,SAAuBA,GACrB,MAAMtxB,EAASR,EAAOQ,OAAOuwB,WAC7B,GAAIe,EAASzc,UAAY,GACvB,GAAIrV,EAAOgR,QAAUhR,EAAOQ,OAAO6M,MAAQ7M,EAAOwwB,eAEhD,OAAO,OAEJ,GAAIhxB,EAAO+Q,cAAgB/Q,EAAOQ,OAAO6M,MAAQ7M,EAAOwwB,eAE7D,OAAO,EAET,OAAO,CACT,CAgGQ8C,CAAchC,GAChB,OAAO,CAEX,CAqHA,OAFI9tB,EAAE+X,eAAgB/X,EAAE+X,iBACnB/X,EAAE8sB,aAAc,GACd,CACT,CAEA,SAAS7pB,EAAOM,GACd,IAAIsS,EAAW7Z,EAAOrD,GACwB,cAA1CqD,EAAOQ,OAAOuwB,WAAWK,eAC3BvX,EAAWhf,SAASxB,cAAc2G,EAAOQ,OAAOuwB,WAAWK,eAE7DvX,EAAStS,GAAQ,aAAcmqB,GAC/B7X,EAAStS,GAAQ,aAAcqqB,GAC/B/X,EAAStS,GAAQ,QAAS+nB,EAC5B,CAEA,SAAS7K,IACP,OAAIzkB,EAAOQ,OAAOiL,SAChBzL,EAAOU,UAAUzH,oBAAoB,QAASq2B,IACvC,IAELtvB,EAAO+wB,WAAW5mB,UACtBlD,EAAO,oBACPjH,EAAO+wB,WAAW5mB,SAAU,GACrB,EACT,CACA,SAASqa,IACP,OAAIxkB,EAAOQ,OAAOiL,SAChBzL,EAAOU,UAAU1H,iBAAiBwO,MAAO8nB,IAClC,KAEJtvB,EAAO+wB,WAAW5mB,UACvBlD,EAAO,uBACPjH,EAAO+wB,WAAW5mB,SAAU,GACrB,EACT,CAEAnD,EAAG,QAAQ,MACJhH,EAAOQ,OAAOuwB,WAAW5mB,SAAWnK,EAAOQ,OAAOiL,SACrD+Y,IAEExkB,EAAOQ,OAAOuwB,WAAW5mB,SAASsa,GAAQ,IAEhDzd,EAAG,WAAW,KACRhH,EAAOQ,OAAOiL,SAChBgZ,IAEEzkB,EAAO+wB,WAAW5mB,SAASqa,GAAS,IAG1ClsB,OAAO+Q,OAAOrJ,EAAO+wB,WAAY,CAC/BtM,SACAD,WAEJ,ECtbe,SAAwDzkB,GAAA,IAApCC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,EAAEuB,KAAEA,GAAMxI,EACnE6mB,EAAa,CACXvG,WAAY,CACVC,OAAQ,KACRC,OAAQ,KAERwT,aAAa,EACbC,cAAe,yBACfC,YAAa,uBACbC,UAAW,qBACXC,wBAAyB,gCAI7Bn0B,EAAOqgB,WAAa,CAClBC,OAAQ,KACRC,OAAQ,MAGV,MAAM6T,EAAqBz3B,IACpB4F,MAAMC,QAAQ7F,KAAKA,EAAK,CAACA,GAAIsC,QAAQ+E,KAAQA,KAC3CrH,GAGT,SAAS03B,EAAM13B,GACb,IAAI23B,EACJ,OAAI33B,GAAoB,iBAAPA,GAAmBqD,EAAO4P,YACzC0kB,EAAMt0B,EAAOrD,GAAG6d,WAAWnhB,cAAcsD,GACrC23B,GAAYA,GAEd33B,IACgB,iBAAPA,IAAiB23B,EAAM,IAAIz5B,SAASvB,iBAAiBqD,KAE9DqD,EAAOQ,OAAOgiB,mBACA,iBAAP7lB,GACP23B,EAAIz7B,OAAS,GAC6B,IAA1CmH,EAAOrD,GAAGrD,iBAAiBqD,GAAI9D,SAE/By7B,EAAMt0B,EAAOrD,GAAGtD,cAAcsD,KAG9BA,IAAO23B,EAAY33B,EAEhB23B,EACT,CAEA,SAASC,EAAS53B,EAAI63B,GACpB,MAAMh0B,EAASR,EAAOQ,OAAO6f,YAC7B1jB,EAAKy3B,EAAkBz3B,IACpBhE,SAAS87B,IACNA,IACFA,EAAMpyB,UAAUmyB,EAAW,MAAQ,aAAah0B,EAAOwzB,cAAc32B,MAAM,MACrD,WAAlBo3B,EAAMC,UAAsBD,EAAMD,SAAWA,GAC7Cx0B,EAAOQ,OAAOgO,eAAiBxO,EAAOmK,SACxCsqB,EAAMpyB,UAAUrC,EAAOqjB,SAAW,MAAQ,UAAU7iB,EAAO0zB,WAE/D,GAEJ,CACA,SAASrrB,IAEP,MAAMyX,OAAEA,EAAMC,OAAEA,GAAWvgB,EAAOqgB,WAClC,GAAIrgB,EAAOQ,OAAO6M,KAGhB,OAFAknB,EAAShU,GAAQ,QACjBgU,EAASjU,GAAQ,GAInBiU,EAAShU,EAAQvgB,EAAO+Q,cAAgB/Q,EAAOQ,OAAOuW,QACtDwd,EAASjU,EAAQtgB,EAAOgR,QAAUhR,EAAOQ,OAAOuW,OAClD,CACA,SAAS4d,EAAY3wB,GACnBA,EAAE+X,mBACE/b,EAAO+Q,aAAgB/Q,EAAOQ,OAAO6M,MAASrN,EAAOQ,OAAOuW,UAChE/W,EAAOgX,YACPzO,EAAK,kBACP,CACA,SAASqsB,EAAY5wB,GACnBA,EAAE+X,mBACE/b,EAAOgR,OAAUhR,EAAOQ,OAAO6M,MAASrN,EAAOQ,OAAOuW,UAC1D/W,EAAOuW,YACPhO,EAAK,kBACP,CACA,SAAS4Z,IACP,MAAM3hB,EAASR,EAAOQ,OAAO6f,WAW7B,GATArgB,EAAOQ,OAAO6f,WAAa+I,EACzBppB,EACAA,EAAOgkB,eAAe3D,WACtBrgB,EAAOQ,OAAO6f,WACd,CACEC,OAAQ,qBACRC,OAAQ,wBAGN/f,EAAO8f,SAAU9f,EAAO+f,OAAS,OAEvC,IAAID,EAAS+T,EAAM7zB,EAAO8f,QACtBC,EAAS8T,EAAM7zB,EAAO+f,QAE1BjoB,OAAO+Q,OAAOrJ,EAAOqgB,WAAY,CAC/BC,SACAC,WAEFD,EAAS8T,EAAkB9T,GAC3BC,EAAS6T,EAAkB7T,GAE3B,MAAMsU,EAAa,CAACl4B,EAAIkE,KAClBlE,GACFA,EAAG3D,iBAAiB,QAAiB,SAAR6H,EAAiB+zB,EAAcD,IAEzD30B,EAAOmK,SAAWxN,GACrBA,EAAG0F,UAAUC,OAAO9B,EAAO0zB,UAAU72B,MAAM,KAC7C,EAGFijB,EAAO3nB,SAASgE,GAAOk4B,EAAWl4B,EAAI,UACtC4jB,EAAO5nB,SAASgE,GAAOk4B,EAAWl4B,EAAI,SACxC,CACA,SAAS+rB,IACP,IAAIpI,OAAEA,EAAMC,OAAEA,GAAWvgB,EAAOqgB,WAChCC,EAAS8T,EAAkB9T,GAC3BC,EAAS6T,EAAkB7T,GAC3B,MAAMuU,EAAgB,CAACn4B,EAAIkE,KACzBlE,EAAG1D,oBAAoB,QAAiB,SAAR4H,EAAiB+zB,EAAcD,GAC/Dh4B,EAAG0F,UAAU4M,UAAUjP,EAAOQ,OAAO6f,WAAW2T,cAAc32B,MAAM,KAAK,EAE3EijB,EAAO3nB,SAASgE,GAAOm4B,EAAcn4B,EAAI,UACzC4jB,EAAO5nB,SAASgE,GAAOm4B,EAAcn4B,EAAI,SAC3C,CAEAqK,EAAG,QAAQ,MACgC,IAArChH,EAAOQ,OAAO6f,WAAWlW,QAE3Bqa,KAEArC,IACAtZ,IACF,IAEF7B,EAAG,+BAA+B,KAChC6B,GAAQ,IAEV7B,EAAG,WAAW,KACZ0hB,GAAS,IAEX1hB,EAAG,kBAAkB,KACnB,IAAIsZ,OAAEA,EAAMC,OAAEA,GAAWvgB,EAAOqgB,WAChCC,EAAS8T,EAAkB9T,GAC3BC,EAAS6T,EAAkB7T,GAC3B,IAAID,KAAWC,GACZthB,QAAQtC,KAASA,IACjBhE,SAASgE,GACRA,EAAG0F,UAAUrC,EAAOmK,QAAU,SAAW,OAAOnK,EAAOQ,OAAO6f,WAAW6T,YAC1E,IAELltB,EAAG,SAAS,CAAC8jB,EAAI9mB,KACf,IAAIsc,OAAEA,EAAMC,OAAEA,GAAWvgB,EAAOqgB,WAChCC,EAAS8T,EAAkB9T,GAC3BC,EAAS6T,EAAkB7T,GAC3B,MAAM1G,EAAW7V,EAAExL,OACnB,GACEwH,EAAOQ,OAAO6f,WAAW0T,cACxBxT,EAAOha,SAASsT,KAChByG,EAAO/Z,SAASsT,GACjB,CACA,GACE7Z,EAAO+0B,YACP/0B,EAAOQ,OAAOu0B,YACd/0B,EAAOQ,OAAOu0B,WAAWC,YACxBh1B,EAAO+0B,WAAWp4B,KAAOkd,GAAY7Z,EAAO+0B,WAAWp4B,GAAGoS,SAAS8K,IAEpE,OACF,IAAIob,EACA3U,EAAOznB,OACTo8B,EAAW3U,EAAO,GAAGje,UAAU0M,SAAS/O,EAAOQ,OAAO6f,WAAW4T,aACxD1T,EAAO1nB,SAChBo8B,EAAW1U,EAAO,GAAGle,UAAU0M,SAAS/O,EAAOQ,OAAO6f,WAAW4T,cAGjE1rB,GADe,IAAb0sB,EACG,iBAEA,kBAEP,IAAI3U,KAAWC,GACZthB,QAAQtC,KAASA,IACjBhE,SAASgE,GAAOA,EAAG0F,UAAU6yB,OAAOl1B,EAAOQ,OAAO6f,WAAW4T,cAClE,KAGF,MAMMzP,EAAU,KACdxkB,EAAOrD,GAAG0F,UAAUC,OAAOtC,EAAOQ,OAAO6f,WAAW8T,wBAAwB92B,MAAM,MAClFqrB,GAAS,EAGXpwB,OAAO+Q,OAAOrJ,EAAOqgB,WAAY,CAC/BoE,OAZa,KACbzkB,EAAOrD,GAAG0F,UAAU4M,UAAUjP,EAAOQ,OAAO6f,WAAW8T,wBAAwB92B,MAAM,MACrF8kB,IACAtZ,GAAQ,EAUR2b,UACA3b,SACAsZ,OACAuG,WAEJ,EC9Me,SAAwD3oB,GAAA,IAApCC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,EAAEuB,KAAEA,GAAMxI,EACnE,MAAMo1B,EAAM,oBAsCZ,IAAIC,EArCJxO,EAAa,CACXmO,WAAY,CACVp4B,GAAI,KACJ04B,cAAe,OACfL,WAAW,EACXjB,aAAa,EACbuB,aAAc,KACdC,kBAAmB,KACnBC,eAAgB,KAChBC,aAAc,KACdC,qBAAqB,EACrBnW,KAAM,UACNoW,gBAAgB,EAChBC,mBAAoB,EACpBC,sBAAwBC,GAAWA,EACnCC,oBAAsBD,GAAWA,EACjCE,YAAc,GAAEb,WAChBc,kBAAoB,GAAEd,kBACtBe,cAAgB,GAAEf,KAClBgB,aAAe,GAAEhB,YACjBiB,WAAa,GAAEjB,UACflB,YAAc,GAAEkB,WAChBkB,qBAAuB,GAAElB,qBACzBmB,yBAA2B,GAAEnB,yBAC7BoB,eAAiB,GAAEpB,cACnBjB,UAAY,GAAEiB,SACdqB,gBAAkB,GAAErB,eACpBsB,cAAgB,GAAEtB,aAClBuB,wBAA0B,GAAEvB,gBAIhCn1B,EAAO+0B,WAAa,CAClBp4B,GAAI,KACJg6B,QAAS,IAIX,IAAIC,EAAqB,EAEzB,MAAMxC,EAAqBz3B,IACpB4F,MAAMC,QAAQ7F,KAAKA,EAAK,CAACA,GAAIsC,QAAQ+E,KAAQA,KAC3CrH,GAGT,SAASk6B,IACP,OACG72B,EAAOQ,OAAOu0B,WAAWp4B,KACzBqD,EAAO+0B,WAAWp4B,IAClB4F,MAAMC,QAAQxC,EAAO+0B,WAAWp4B,KAAuC,IAAhCqD,EAAO+0B,WAAWp4B,GAAG9D,MAEjE,CAEA,SAASi+B,EAAeC,EAAUzD,GAChC,MAAM2C,kBAAEA,GAAsBj2B,EAAOQ,OAAOu0B,WACvCgC,IACLA,EAAWA,GAAyB,SAAbzD,EAAsB,WAAa,QAArC,qBAEnByD,EAAS10B,UAAUC,IAAK,GAAE2zB,KAAqB3C,MAC/CyD,EAAWA,GAAyB,SAAbzD,EAAsB,WAAa,QAArC,oBAEnByD,EAAS10B,UAAUC,IAAK,GAAE2zB,KAAqB3C,KAAYA,KAGjE,CAEA,SAAS0D,EAAchzB,GACrB,MAAM+yB,EAAW/yB,EAAExL,OAAOqb,QAAQyV,GAAkBtpB,EAAOQ,OAAOu0B,WAAWiB,cAC7E,IAAKe,EACH,OAEF/yB,EAAE+X,iBACF,MAAM3T,EAAQ9E,EAAayzB,GAAY/2B,EAAOQ,OAAOsM,eACrD,GAAI9M,EAAOQ,OAAO6M,KAAM,CACtB,GAAIrN,EAAOkT,YAAc9K,EAAO,QAC5BA,EAAQpI,EAAO8X,cAAgB1P,EAAQpI,EAAOqK,OAAOxR,OAASmH,EAAO8X,eACvE9X,EAAO6W,QAAQ,CACbxB,UAAWjN,EAAQpI,EAAO8X,aAAe,OAAS,OAClDG,iBAAkB7P,EAClBmN,SAAS,IAIbvV,EAAOqW,YAAYjO,EACrB,MACEpI,EAAOuV,QAAQnN,EAEnB,CAEA,SAASS,IAEP,MAAMkB,EAAM/J,EAAO+J,IACbvJ,EAASR,EAAOQ,OAAOu0B,WAC7B,GAAI8B,IAAwB,OAE5B,IAGI91B,EAHApE,EAAKqD,EAAO+0B,WAAWp4B,GAC3BA,EAAKy3B,EAAkBz3B,GAGvB,MAAM4N,EACJvK,EAAOkK,SAAWlK,EAAOQ,OAAO0J,QAAQC,QACpCnK,EAAOkK,QAAQG,OAAOxR,OACtBmH,EAAOqK,OAAOxR,OACdo+B,EAAQj3B,EAAOQ,OAAO6M,KACxBlM,KAAKoM,KAAKhD,EAAevK,EAAOQ,OAAOsM,gBACvC9M,EAAOwK,SAAS3R,OAYpB,GAVEkI,EADEf,EAAOQ,OAAO6M,KAEdrN,EAAOQ,OAAOsM,eAAiB,EAC3B3L,KAAKwL,MAAM3M,EAAOkT,UAAYlT,EAAOQ,OAAOsM,gBAC5C9M,EAAOkT,eACwB,IAArBlT,EAAOoO,UACbpO,EAAOoO,UAEPpO,EAAOyP,aAAe,EAIhB,YAAhBjP,EAAO+e,MACPvf,EAAO+0B,WAAW4B,SAClB32B,EAAO+0B,WAAW4B,QAAQ99B,OAAS,EACnC,CACA,MAAM89B,EAAU32B,EAAO+0B,WAAW4B,QAClC,IAAIO,EACA3f,EACA4f,EA6BJ,GA5BI32B,EAAOm1B,iBACTP,EAAanxB,EAAiB0yB,EAAQ,GAAI32B,EAAOiJ,eAAiB,QAAU,UAAU,GACtFtM,EAAGhE,SAAS87B,IACVA,EAAM56B,MAAMmG,EAAOiJ,eAAiB,QAAU,UAC5CmsB,GAAc50B,EAAOo1B,mBAAqB,GADe,IAEvD,IAGFp1B,EAAOo1B,mBAAqB,QAA8Bh3B,IAAzBoB,EAAOiT,gBAC1C2jB,GAAsB71B,GAAWf,EAAOiT,eAAiB,GACrD2jB,EAAqBp2B,EAAOo1B,mBAAqB,EACnDgB,EAAqBp2B,EAAOo1B,mBAAqB,EACxCgB,EAAqB,IAC9BA,EAAqB,IAGzBM,EAAa/1B,KAAKC,IAAIL,EAAU61B,EAAoB,GACpDrf,EAAY2f,GAAc/1B,KAAKE,IAAIs1B,EAAQ99B,OAAQ2H,EAAOo1B,oBAAsB,GAChFuB,GAAY5f,EAAY2f,GAAc,GAExCP,EAAQh+B,SAASo+B,IACfA,EAAS10B,UAAU4M,UACd,CAAC,GAAI,QAAS,aAAc,QAAS,aAAc,SAAS3R,KAC5D85B,GAAY,GAAE52B,EAAOy1B,oBAAoBmB,MAE7C,IAGCz6B,EAAG9D,OAAS,EACd89B,EAAQh+B,SAAS0+B,IACf,MAAMC,EAAch0B,EAAa+zB,GAC7BC,IAAgBv2B,GAClBs2B,EAAOh1B,UAAUC,IAAI9B,EAAOy1B,mBAE1Bz1B,EAAOm1B,iBACL2B,GAAeJ,GAAcI,GAAe/f,GAC9C8f,EAAOh1B,UAAUC,IAAK,GAAE9B,EAAOy1B,0BAE7BqB,IAAgBJ,GAClBJ,EAAeO,EAAQ,QAErBC,IAAgB/f,GAClBuf,EAAeO,EAAQ,QAE3B,QAEG,CACL,MAAMA,EAASV,EAAQ51B,GAKvB,GAJIs2B,GACFA,EAAOh1B,UAAUC,IAAI9B,EAAOy1B,mBAG1Bz1B,EAAOm1B,eAAgB,CACzB,MAAM4B,EAAuBZ,EAAQO,GAC/BM,EAAsBb,EAAQpf,GACpC,IAAK,IAAIzY,EAAIo4B,EAAYp4B,GAAKyY,EAAWzY,GAAK,EACxC63B,EAAQ73B,IACV63B,EAAQ73B,GAAGuD,UAAUC,IAAK,GAAE9B,EAAOy1B,0BAIvCa,EAAeS,EAAsB,QACrCT,EAAeU,EAAqB,OACtC,CACF,CACA,GAAIh3B,EAAOm1B,eAAgB,CACzB,MAAM8B,EAAuBt2B,KAAKE,IAAIs1B,EAAQ99B,OAAQ2H,EAAOo1B,mBAAqB,GAC5E8B,GACHtC,EAAaqC,EAAuBrC,GAAc,EAAI+B,EAAW/B,EAC9D3G,EAAa1kB,EAAM,QAAU,OACnC4sB,EAAQh+B,SAAS0+B,IACfA,EAAOx9B,MAAMmG,EAAOiJ,eAAiBwlB,EAAa,OAAU,GAAEiJ,KAAiB,GAEnF,CACF,CACA/6B,EAAGhE,SAAQ,CAAC87B,EAAOkD,KASjB,GARoB,aAAhBn3B,EAAO+e,OACTkV,EAAMn7B,iBAAiBgwB,GAAkB9oB,EAAO21B,eAAex9B,SAASi/B,IACtEA,EAAWC,YAAcr3B,EAAOq1B,sBAAsB90B,EAAU,EAAE,IAEpE0zB,EAAMn7B,iBAAiBgwB,GAAkB9oB,EAAO41B,aAAaz9B,SAASm/B,IACpEA,EAAQD,YAAcr3B,EAAOu1B,oBAAoBkB,EAAM,KAGvC,gBAAhBz2B,EAAO+e,KAAwB,CACjC,IAAIwY,EAEFA,EADEv3B,EAAOk1B,oBACc11B,EAAOiJ,eAAiB,WAAa,aAErCjJ,EAAOiJ,eAAiB,aAAe,WAEhE,MAAM+uB,GAASj3B,EAAU,GAAKk2B,EAC9B,IAAIgB,EAAS,EACTC,EAAS,EACgB,eAAzBH,EACFE,EAASD,EAETE,EAASF,EAEXvD,EACGn7B,iBAAiBgwB,GAAkB9oB,EAAO61B,uBAC1C19B,SAASw/B,IACRA,EAAWt+B,MAAMsD,UAAa,6BAA4B86B,aAAkBC,KAC5EC,EAAWt+B,MAAMspB,mBAAsB,GAAEnjB,EAAOQ,OAAOC,SAAS,GAEtE,CACoB,WAAhBD,EAAO+e,MAAqB/e,EAAOi1B,cACrChB,EAAM/K,UAAYlpB,EAAOi1B,aAAaz1B,EAAQe,EAAU,EAAGk2B,GACxC,IAAfU,GAAkBpvB,EAAK,mBAAoBksB,KAE5B,IAAfkD,GAAkBpvB,EAAK,mBAAoBksB,GAC/ClsB,EAAK,mBAAoBksB,IAEvBz0B,EAAOQ,OAAOgO,eAAiBxO,EAAOmK,SACxCsqB,EAAMpyB,UAAUrC,EAAOqjB,SAAW,MAAQ,UAAU7iB,EAAO0zB,UAC7D,GAEJ,CACA,SAASkE,IAEP,MAAM53B,EAASR,EAAOQ,OAAOu0B,WAC7B,GAAI8B,IAAwB,OAC5B,MAAMtsB,EACJvK,EAAOkK,SAAWlK,EAAOQ,OAAO0J,QAAQC,QACpCnK,EAAOkK,QAAQG,OAAOxR,OACtBmH,EAAOqK,OAAOxR,OAEpB,IAAI8D,EAAKqD,EAAO+0B,WAAWp4B,GAC3BA,EAAKy3B,EAAkBz3B,GACvB,IAAI07B,EAAiB,GACrB,GAAoB,YAAhB73B,EAAO+e,KAAoB,CAC7B,IAAI+Y,EAAkBt4B,EAAOQ,OAAO6M,KAChClM,KAAKoM,KAAKhD,EAAevK,EAAOQ,OAAOsM,gBACvC9M,EAAOwK,SAAS3R,OAElBmH,EAAOQ,OAAOoc,UACd5c,EAAOQ,OAAOoc,SAASzS,SACvBmuB,EAAkB/tB,IAElB+tB,EAAkB/tB,GAEpB,IAAK,IAAIzL,EAAI,EAAGA,EAAIw5B,EAAiBx5B,GAAK,EACpC0B,EAAO80B,aACT+C,GAAkB73B,EAAO80B,aAAal3B,KAAK4B,EAAQlB,EAAG0B,EAAOw1B,aAE7DqC,GAAmB,IAAG73B,EAAO60B,wBAAwB70B,EAAOw1B,kBAAkBx1B,EAAO60B,gBAG3F,CACoB,aAAhB70B,EAAO+e,OAEP8Y,EADE73B,EAAOg1B,eACQh1B,EAAOg1B,eAAep3B,KAAK4B,EAAQQ,EAAO21B,aAAc31B,EAAO41B,YAG7E,gBAAe51B,EAAO21B,wCAEP31B,EAAO41B,uBAGT,gBAAhB51B,EAAO+e,OAEP8Y,EADE73B,EAAO+0B,kBACQ/0B,EAAO+0B,kBAAkBn3B,KAAK4B,EAAQQ,EAAO61B,sBAE5C,gBAAe71B,EAAO61B,iCAI5C15B,EAAGhE,SAAS87B,IACU,WAAhBj0B,EAAO+e,OACTkV,EAAM/K,UAAY2O,GAAkB,IAElB,YAAhB73B,EAAO+e,OACTvf,EAAO+0B,WAAW4B,QAAU,IACvBlC,EAAMn7B,iBAAiBgwB,GAAkB9oB,EAAOw1B,eAEvD,IAEkB,WAAhBx1B,EAAO+e,MACThX,EAAK,mBAAoB5L,EAAG,GAEhC,CACA,SAASwlB,IACPniB,EAAOQ,OAAOu0B,WAAa3L,EACzBppB,EACAA,EAAOgkB,eAAe+Q,WACtB/0B,EAAOQ,OAAOu0B,WACd,CAAEp4B,GAAI,sBAER,MAAM6D,EAASR,EAAOQ,OAAOu0B,WAC7B,IAAKv0B,EAAO7D,GAAI,OAChB,IAAIA,EACqB,iBAAd6D,EAAO7D,IAAmBqD,EAAO4P,YAC1CjT,EAAKqD,EAAOrD,GAAG6d,WAAWnhB,cAAcmH,EAAO7D,KAE5CA,GAA2B,iBAAd6D,EAAO7D,KACvBA,EAAK,IAAI9B,SAASvB,iBAAiBkH,EAAO7D,MAEvCA,IACHA,EAAK6D,EAAO7D,IAETA,GAAoB,IAAdA,EAAG9D,SAGZmH,EAAOQ,OAAOgiB,mBACO,iBAAdhiB,EAAO7D,IACd4F,MAAMC,QAAQ7F,IACdA,EAAG9D,OAAS,IAEZ8D,EAAK,IAAIqD,EAAOrD,GAAGrD,iBAAiBkH,EAAO7D,KAEvCA,EAAG9D,OAAS,IACd8D,EAAKA,EAAGsC,QAAQw1B,GACVhxB,EAAegxB,EAAO,WAAW,KAAOz0B,EAAOrD,KAElD,KAGH4F,MAAMC,QAAQ7F,IAAqB,IAAdA,EAAG9D,SAAc8D,EAAKA,EAAG,IAElDrE,OAAO+Q,OAAOrJ,EAAO+0B,WAAY,CAC/Bp4B,OAGFA,EAAKy3B,EAAkBz3B,GACvBA,EAAGhE,SAAS87B,IACU,YAAhBj0B,EAAO+e,MAAsB/e,EAAOw0B,WACtCP,EAAMpyB,UAAUC,IAAI9B,EAAO+1B,gBAG7B9B,EAAMpyB,UAAUC,IAAI9B,EAAO01B,cAAgB11B,EAAO+e,MAClDkV,EAAMpyB,UAAUC,IAAItC,EAAOiJ,eAAiBzI,EAAOg2B,gBAAkBh2B,EAAOi2B,eAExD,YAAhBj2B,EAAO+e,MAAsB/e,EAAOm1B,iBACtClB,EAAMpyB,UAAUC,IAAK,GAAE9B,EAAO01B,gBAAgB11B,EAAO+e,gBACrDqX,EAAqB,EACjBp2B,EAAOo1B,mBAAqB,IAC9Bp1B,EAAOo1B,mBAAqB,IAGZ,gBAAhBp1B,EAAO+e,MAA0B/e,EAAOk1B,qBAC1CjB,EAAMpyB,UAAUC,IAAI9B,EAAO81B,0BAGzB91B,EAAOw0B,WACTP,EAAMz7B,iBAAiB,QAASg+B,GAG7Bh3B,EAAOmK,SACVsqB,EAAMpyB,UAAUC,IAAI9B,EAAO0zB,UAC7B,IAEJ,CAEA,SAASxL,IACP,MAAMloB,EAASR,EAAOQ,OAAOu0B,WAC7B,GAAI8B,IAAwB,OAC5B,IAAIl6B,EAAKqD,EAAO+0B,WAAWp4B,GACvBA,IACFA,EAAKy3B,EAAkBz3B,GACvBA,EAAGhE,SAAS87B,IACVA,EAAMpyB,UAAU4M,OAAOzO,EAAOyzB,aAC9BQ,EAAMpyB,UAAU4M,OAAOzO,EAAO01B,cAAgB11B,EAAO+e,MACrDkV,EAAMpyB,UAAU4M,OACdjP,EAAOiJ,eAAiBzI,EAAOg2B,gBAAkBh2B,EAAOi2B,eAEtDj2B,EAAOw0B,WACTP,EAAMx7B,oBAAoB,QAAS+9B,EACrC,KAIAh3B,EAAO+0B,WAAW4B,SACpB32B,EAAO+0B,WAAW4B,QAAQh+B,SAAS87B,GACjCA,EAAMpyB,UAAU4M,OAAOzO,EAAOy1B,oBAEpC,CAEAjvB,EAAG,QAAQ,MACgC,IAArChH,EAAOQ,OAAOu0B,WAAW5qB,QAE3Bqa,KAEArC,IACAiW,IACAvvB,IACF,IAEF7B,EAAG,qBAAqB,UACU,IAArBhH,EAAOoO,WAChBvF,GACF,IAEF7B,EAAG,mBAAmB,KACpB6B,GAAQ,IAEV7B,EAAG,wBAAwB,KACzBoxB,IACAvvB,GAAQ,IAEV7B,EAAG,WAAW,KACZ0hB,GAAS,IAEX1hB,EAAG,kBAAkB,KACnB,IAAIrK,GAAEA,GAAOqD,EAAO+0B,WAChBp4B,IACFA,EAAKy3B,EAAkBz3B,GACvBA,EAAGhE,SAAS87B,GACVA,EAAMpyB,UAAUrC,EAAOmK,QAAU,SAAW,OAAOnK,EAAOQ,OAAOu0B,WAAWb,aAEhF,IAEFltB,EAAG,eAAe,KAChB6B,GAAQ,IAEV7B,EAAG,SAAS,CAAC8jB,EAAI9mB,KACf,MAAM6V,EAAW7V,EAAExL,OACnB,IAAImE,GAAEA,GAAOqD,EAAO+0B,WAEpB,GADKxyB,MAAMC,QAAQ7F,KAAKA,EAAK,CAACA,GAAIsC,QAAQ+C,KAAcA,KAEtDhC,EAAOQ,OAAOu0B,WAAWp4B,IACzBqD,EAAOQ,OAAOu0B,WAAWhB,aACzBp3B,GACAA,EAAG9D,OAAS,IACXghB,EAASxX,UAAU0M,SAAS/O,EAAOQ,OAAOu0B,WAAWiB,aACtD,CACA,GACEh2B,EAAOqgB,aACLrgB,EAAOqgB,WAAWC,QAAUzG,IAAa7Z,EAAOqgB,WAAWC,QAC1DtgB,EAAOqgB,WAAWE,QAAU1G,IAAa7Z,EAAOqgB,WAAWE,QAE9D,OACF,MAAM0U,EAAWt4B,EAAG,GAAG0F,UAAU0M,SAAS/O,EAAOQ,OAAOu0B,WAAWd,aAEjE1rB,GADe,IAAb0sB,EACG,iBAEA,kBAEPt4B,EAAGhE,SAAS87B,GAAUA,EAAMpyB,UAAU6yB,OAAOl1B,EAAOQ,OAAOu0B,WAAWd,cACxE,KAGF,MAcMzP,EAAU,KACdxkB,EAAOrD,GAAG0F,UAAUC,IAAItC,EAAOQ,OAAOu0B,WAAW2B,yBACjD,IAAI/5B,GAAEA,GAAOqD,EAAO+0B,WAChBp4B,IACFA,EAAKy3B,EAAkBz3B,GACvBA,EAAGhE,SAAS87B,GAAUA,EAAMpyB,UAAUC,IAAItC,EAAOQ,OAAOu0B,WAAW2B,4BAErEhO,GAAS,EAGXpwB,OAAO+Q,OAAOrJ,EAAO+0B,WAAY,CAC/BtQ,OAzBa,KACbzkB,EAAOrD,GAAG0F,UAAU4M,OAAOjP,EAAOQ,OAAOu0B,WAAW2B,yBACpD,IAAI/5B,GAAEA,GAAOqD,EAAO+0B,WAChBp4B,IACFA,EAAKy3B,EAAkBz3B,GACvBA,EAAGhE,SAAS87B,GACVA,EAAMpyB,UAAU4M,OAAOjP,EAAOQ,OAAOu0B,WAAW2B,4BAGpDvU,IACAiW,IACAvvB,GAAQ,EAeR2b,UACA4T,SACAvvB,SACAsZ,OACAuG,WAEJ,ECrfe,SAAuD3oB,GAAA,IAApCC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,EAAEuB,KAAEA,GAAMxI,EAClE,MAAMlF,EAAWF,IAEjB,IAGI49B,EACAC,EACAC,EACAC,EANAze,GAAY,EACZuG,EAAU,KACVmY,EAAc,KA0BlB,SAASvkB,IACP,IAAKpU,EAAOQ,OAAOo4B,UAAUj8B,KAAOqD,EAAO44B,UAAUj8B,GAAI,OACzD,MAAMi8B,UAAEA,EAAW9uB,aAAcC,GAAQ/J,GACnC64B,OAAEA,EAAMl8B,GAAEA,GAAOi8B,EACjBp4B,EAASR,EAAOQ,OAAOo4B,UACvB13B,EAAWlB,EAAOQ,OAAO6M,KAAOrN,EAAOiR,aAAejR,EAAOkB,SAEnE,IAAI43B,EAAUN,EACVO,GAAUN,EAAYD,GAAYt3B,EAClC6I,GACFgvB,GAAUA,EACNA,EAAS,GACXD,EAAUN,EAAWO,EACrBA,EAAS,IACCA,EAASP,EAAWC,IAC9BK,EAAUL,EAAYM,IAEfA,EAAS,GAClBD,EAAUN,EAAWO,EACrBA,EAAS,GACAA,EAASP,EAAWC,IAC7BK,EAAUL,EAAYM,GAEpB/4B,EAAOiJ,gBACT4vB,EAAOh/B,MAAMsD,UAAa,eAAc47B,aACxCF,EAAOh/B,MAAM0L,MAAS,GAAEuzB,QAExBD,EAAOh/B,MAAMsD,UAAa,oBAAmB47B,UAC7CF,EAAOh/B,MAAM4L,OAAU,GAAEqzB,OAEvBt4B,EAAOw4B,OACTl9B,aAAa0kB,GACb7jB,EAAG9C,MAAMo/B,QAAU,EACnBzY,EAAU3kB,YAAW,KACnBc,EAAG9C,MAAMo/B,QAAU,EACnBt8B,EAAG9C,MAAMspB,mBAAqB,OAAO,GACpC,KAEP,CAKA,SAASra,IACP,IAAK9I,EAAOQ,OAAOo4B,UAAUj8B,KAAOqD,EAAO44B,UAAUj8B,GAAI,OAEzD,MAAMi8B,UAAEA,GAAc54B,GAChB64B,OAAEA,EAAMl8B,GAAEA,GAAOi8B,EAEvBC,EAAOh/B,MAAM0L,MAAQ,GACrBszB,EAAOh/B,MAAM4L,OAAS,GACtBgzB,EAAYz4B,EAAOiJ,eAAiBtM,EAAGyH,YAAczH,EAAG+S,aAExDgpB,EACE14B,EAAOkE,MACNlE,EAAOoL,YACNpL,EAAOQ,OAAOoK,oBACb5K,EAAOQ,OAAOgL,eAAiBxL,EAAOwK,SAAS,GAAK,IAEvDguB,EADuC,SAArCx4B,EAAOQ,OAAOo4B,UAAUJ,SACfC,EAAYC,EAEZvvB,SAASnJ,EAAOQ,OAAOo4B,UAAUJ,SAAU,IAGpDx4B,EAAOiJ,eACT4vB,EAAOh/B,MAAM0L,MAAS,GAAEizB,MAExBK,EAAOh/B,MAAM4L,OAAU,GAAE+yB,MAIzB77B,EAAG9C,MAAMq/B,QADPR,GAAW,EACM,OAEA,GAEjB14B,EAAOQ,OAAOo4B,UAAUI,OAC1Br8B,EAAG9C,MAAMo/B,QAAU,GAGjBj5B,EAAOQ,OAAOgO,eAAiBxO,EAAOmK,SACxCyuB,EAAUj8B,GAAG0F,UAAUrC,EAAOqjB,SAAW,MAAQ,UAAUrjB,EAAOQ,OAAOo4B,UAAU1E,UAEvF,CACA,SAASiF,EAAmBn1B,GAC1B,OAAOhE,EAAOiJ,eAAiBjF,EAAEo1B,QAAUp1B,EAAEq1B,OAC/C,CACA,SAASC,EAAgBt1B,GACvB,MAAM40B,UAAEA,EAAW9uB,aAAcC,GAAQ/J,GACnCrD,GAAEA,GAAOi8B,EAEf,IAAIW,EACJA,GACGJ,EAAmBn1B,GAClBvB,EAAc9F,GAAIqD,EAAOiJ,eAAiB,OAAS,QACjC,OAAjBsvB,EAAwBA,EAAeC,EAAW,KACpDC,EAAYD,GACfe,EAAgBp4B,KAAKC,IAAID,KAAKE,IAAIk4B,EAAe,GAAI,GACjDxvB,IACFwvB,EAAgB,EAAIA,GAGtB,MAAMjG,EACJtzB,EAAOsQ,gBAAkBtQ,EAAO8Q,eAAiB9Q,EAAOsQ,gBAAkBipB,EAE5Ev5B,EAAO2Q,eAAe2iB,GACtBtzB,EAAOoU,aAAakf,GACpBtzB,EAAO+S,oBACP/S,EAAO8R,qBACT,CACA,SAAS0nB,EAAYx1B,GACnB,MAAMxD,EAASR,EAAOQ,OAAOo4B,WACvBA,UAAEA,EAASl4B,UAAEA,GAAcV,GAC3BrD,GAAEA,EAAEk8B,OAAEA,GAAWD,EACvB3e,GAAY,EACZse,EACEv0B,EAAExL,SAAWqgC,EACTM,EAAmBn1B,GACnBA,EAAExL,OAAOmK,wBAAwB3C,EAAOiJ,eAAiB,OAAS,OAClE,KACNjF,EAAE+X,iBACF/X,EAAEia,kBAEFvd,EAAU7G,MAAMspB,mBAAqB,QACrC0V,EAAOh/B,MAAMspB,mBAAqB,QAClCmW,EAAgBt1B,GAEhBlI,aAAa68B,GAEbh8B,EAAG9C,MAAMspB,mBAAqB,MAC1B3iB,EAAOw4B,OACTr8B,EAAG9C,MAAMo/B,QAAU,GAEjBj5B,EAAOQ,OAAOiL,UAChBzL,EAAOU,UAAU7G,MAAM,oBAAsB,QAE/C0O,EAAK,qBAAsBvE,EAC7B,CACA,SAASy1B,EAAWz1B,GAClB,MAAM40B,UAAEA,EAASl4B,UAAEA,GAAcV,GAC3BrD,GAAEA,EAAEk8B,OAAEA,GAAWD,EAElB3e,IACDjW,EAAE+X,eAAgB/X,EAAE+X,iBACnB/X,EAAE8sB,aAAc,EACrBwI,EAAgBt1B,GAChBtD,EAAU7G,MAAMspB,mBAAqB,MACrCxmB,EAAG9C,MAAMspB,mBAAqB,MAC9B0V,EAAOh/B,MAAMspB,mBAAqB,MAClC5a,EAAK,oBAAqBvE,GAC5B,CACA,SAAS01B,EAAU11B,GACjB,MAAMxD,EAASR,EAAOQ,OAAOo4B,WACvBA,UAAEA,EAASl4B,UAAEA,GAAcV,GAC3BrD,GAAEA,GAAOi8B,EAEV3e,IACLA,GAAY,EACRja,EAAOQ,OAAOiL,UAChBzL,EAAOU,UAAU7G,MAAM,oBAAsB,GAC7C6G,EAAU7G,MAAMspB,mBAAqB,IAEnC3iB,EAAOw4B,OACTl9B,aAAa68B,GACbA,EAAcp8B,GAAS,KACrBI,EAAG9C,MAAMo/B,QAAU,EACnBt8B,EAAG9C,MAAMspB,mBAAqB,OAAO,GACpC,MAEL5a,EAAK,mBAAoBvE,GACrBxD,EAAOm5B,eACT35B,EAAOyX,iBAEX,CAEA,SAASxQ,EAAOM,GACd,MAAMqxB,UAAEA,EAASp4B,OAAEA,GAAWR,EACxBrD,EAAKi8B,EAAUj8B,GACrB,IAAKA,EAAI,OACT,MAAMnE,EAASmE,EACTi9B,IAAiBp5B,EAAOiiB,kBAAmB,CAAEV,SAAS,EAAOH,SAAS,GACtEiY,IAAkBr5B,EAAOiiB,kBAAmB,CAAEV,SAAS,EAAMH,SAAS,GAC5E,IAAKppB,EAAQ,OACb,MAAMshC,EAAyB,OAAXvyB,EAAkB,mBAAqB,sBAC3D/O,EAAOshC,GAAa,cAAeN,EAAaI,GAChD/+B,EAASi/B,GAAa,cAAeL,EAAYG,GACjD/+B,EAASi/B,GAAa,YAAaJ,EAAWG,EAChD,CAUA,SAAS1X,IACP,MAAMyW,UAAEA,EAAWj8B,GAAIo9B,GAAa/5B,EACpCA,EAAOQ,OAAOo4B,UAAYxP,EACxBppB,EACAA,EAAOgkB,eAAe4U,UACtB54B,EAAOQ,OAAOo4B,UACd,CAAEj8B,GAAI,qBAER,MAAM6D,EAASR,EAAOQ,OAAOo4B,UAC7B,IAAKp4B,EAAO7D,GAAI,OAEhB,IAAIA,EAsBAk8B,EArBqB,iBAAdr4B,EAAO7D,IAAmBqD,EAAO4P,YAC1CjT,EAAKqD,EAAOrD,GAAG6d,WAAWnhB,cAAcmH,EAAO7D,KAE5CA,GAA2B,iBAAd6D,EAAO7D,GAEbA,IACVA,EAAK6D,EAAO7D,IAFZA,EAAK9B,EAASvB,iBAAiBkH,EAAO7D,IAMtCqD,EAAOQ,OAAOgiB,mBACO,iBAAdhiB,EAAO7D,IACdA,EAAG9D,OAAS,GACoC,IAAhDkhC,EAASzgC,iBAAiBkH,EAAO7D,IAAI9D,SAErC8D,EAAKo9B,EAAS1gC,cAAcmH,EAAO7D,KAEjCA,EAAG9D,OAAS,IAAG8D,EAAKA,EAAG,IAE3BA,EAAG0F,UAAUC,IAAItC,EAAOiJ,eAAiBzI,EAAOg2B,gBAAkBh2B,EAAOi2B,eAGrE95B,IACFk8B,EAASl8B,EAAGtD,cAAe,IAAG2G,EAAOQ,OAAOo4B,UAAUoB,aACjDnB,IACHA,EAASn/B,EAAc,MAAOsG,EAAOQ,OAAOo4B,UAAUoB,WACtDr9B,EAAG+b,OAAOmgB,KAIdvgC,OAAO+Q,OAAOuvB,EAAW,CACvBj8B,KACAk8B,WAGEr4B,EAAOy5B,WAtDNj6B,EAAOQ,OAAOo4B,UAAUj8B,IAAOqD,EAAO44B,UAAUj8B,IACrDsK,EAAO,MAyDHtK,GACFA,EAAG0F,UAAUrC,EAAOmK,QAAU,SAAW,OAAOnK,EAAOQ,OAAOo4B,UAAU1E,UAE5E,CACA,SAASxL,IACP,MAAMloB,EAASR,EAAOQ,OAAOo4B,UACvBj8B,EAAKqD,EAAO44B,UAAUj8B,GACxBA,GACFA,EAAG0F,UAAU4M,OAAOjP,EAAOiJ,eAAiBzI,EAAOg2B,gBAAkBh2B,EAAOi2B,eA9DzEz2B,EAAOQ,OAAOo4B,UAAUj8B,IAAOqD,EAAO44B,UAAUj8B,IACrDsK,EAAO,MAiET,CAvRA2f,EAAa,CACXgS,UAAW,CACTj8B,GAAI,KACJ67B,SAAU,OACVQ,MAAM,EACNiB,WAAW,EACXN,eAAe,EACfzF,UAAW,wBACX8F,UAAW,wBACXE,uBAAwB,4BACxB1D,gBAAkB,8BAClBC,cAAgB,+BAIpBz2B,EAAO44B,UAAY,CACjBj8B,GAAI,KACJk8B,OAAQ,MAwQV7xB,EAAG,QAAQ,MAC+B,IAApChH,EAAOQ,OAAOo4B,UAAUzuB,QAE1Bqa,KAEArC,IACArZ,IACAsL,IACF,IAEFpN,EAAG,4CAA4C,KAC7C8B,GAAY,IAEd9B,EAAG,gBAAgB,KACjBoN,GAAc,IAEhBpN,EAAG,iBAAiB,CAAC8jB,EAAIvqB,MA9OzB,SAAuBA,GAChBP,EAAOQ,OAAOo4B,UAAUj8B,IAAOqD,EAAO44B,UAAUj8B,KACrDqD,EAAO44B,UAAUC,OAAOh/B,MAAMspB,mBAAsB,GAAE5iB,MACxD,CA4OE8O,CAAc9O,EAAS,IAEzByG,EAAG,kBAAkB,KACnB,MAAMrK,GAAEA,GAAOqD,EAAO44B,UAClBj8B,GACFA,EAAG0F,UAAUrC,EAAOmK,QAAU,SAAW,OAAOnK,EAAOQ,OAAOo4B,UAAU1E,UAC1E,IAEFltB,EAAG,WAAW,KACZ0hB,GAAS,IAGX,MAUMlE,EAAU,KACdxkB,EAAOrD,GAAG0F,UAAUC,IAAItC,EAAOQ,OAAOo4B,UAAUsB,wBAC5Cl6B,EAAO44B,UAAUj8B,IACnBqD,EAAO44B,UAAUj8B,GAAG0F,UAAUC,IAAItC,EAAOQ,OAAOo4B,UAAUsB,wBAE5DxR,GAAS,EAGXpwB,OAAO+Q,OAAOrJ,EAAO44B,UAAW,CAC9BnU,OAnBa,KACbzkB,EAAOrD,GAAG0F,UAAU4M,OAAOjP,EAAOQ,OAAOo4B,UAAUsB,wBAC/Cl6B,EAAO44B,UAAUj8B,IACnBqD,EAAO44B,UAAUj8B,GAAG0F,UAAU4M,OAAOjP,EAAOQ,OAAOo4B,UAAUsB,wBAE/D/X,IACArZ,IACAsL,GAAc,EAadoQ,UACA1b,aACAsL,eACA+N,OACAuG,WAEJ,EC7Ve,SAAgD3oB,GAAA,IAA9BC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,GAAIjH,EAC3D6mB,EAAa,CACXuT,SAAU,CACRhwB,SAAS,KAIb,MAAMiwB,EAAe,CAACz9B,EAAIuE,KACxB,MAAM6I,IAAEA,GAAQ/J,EAEVkyB,EAAYnoB,GAAO,EAAI,EAEvBswB,EAAI19B,EAAG4S,aAAa,yBAA2B,IACrD,IAAIgF,EAAI5X,EAAG4S,aAAa,0BACpBiF,EAAI7X,EAAG4S,aAAa,0BACxB,MAAMyoB,EAAQr7B,EAAG4S,aAAa,8BACxB0pB,EAAUt8B,EAAG4S,aAAa,gCAC1B+qB,EAAS39B,EAAG4S,aAAa,+BAwB/B,GAtBIgF,GAAKC,GACPD,EAAIA,GAAK,IACTC,EAAIA,GAAK,KACAxU,EAAOiJ,gBAChBsL,EAAI8lB,EACJ7lB,EAAI,MAEJA,EAAI6lB,EACJ9lB,EAAI,KAIJA,EADEA,EAAErV,QAAQ,MAAQ,EACbiK,SAASoL,EAAG,IAAMrT,EAAWgxB,EAA/B,IAEE3d,EAAIrT,EAAWgxB,EAAjB,KAGL1d,EADEA,EAAEtV,QAAQ,MAAQ,EACbiK,SAASqL,EAAG,IAAMtT,EAApB,IAEEsT,EAAItT,EAAN,KAGH,MAAO+3B,EAA6C,CACtD,MAAMsB,EAAiBtB,GAAWA,EAAU,IAAM,EAAI93B,KAAK0L,IAAI3L,IAC/DvE,EAAG9C,MAAMo/B,QAAUsB,CACrB,CACA,IAAIp9B,EAAa,eAAcoX,MAAMC,UACrC,GAAI,MAAOwjB,EAAyC,CAElD76B,GAAc,UADO66B,GAASA,EAAQ,IAAM,EAAI72B,KAAK0L,IAAI3L,MAE3D,CACA,GAAIo5B,SAAiBA,EAA2C,CAE9Dn9B,GAAc,WADQm9B,EAASp5B,GAAY,OAE7C,CACAvE,EAAG9C,MAAMsD,UAAYA,CAAS,EAG1BiX,EAAe,KACnB,MAAMzX,GAAEA,EAAE0N,OAAEA,EAAMnJ,SAAEA,EAAQsJ,SAAEA,GAAaxK,EAC3C+B,EACEpF,EACA,4IACAhE,SAAS87B,IACT2F,EAAa3F,EAAOvzB,EAAS,IAG/BmJ,EAAO1R,SAAQ,CAACkJ,EAAS+L,KACvB,IAAIyC,EAAgBxO,EAAQX,SACxBlB,EAAOQ,OAAOsM,eAAiB,GAAqC,SAAhC9M,EAAOQ,OAAOwL,gBACpDqE,GAAiBlP,KAAKoM,KAAKK,EAAa,GAAK1M,GAAYsJ,EAAS3R,OAAS,IAE7EwX,EAAgBlP,KAAKE,IAAIF,KAAKC,IAAIiP,GAAgB,GAAI,GACtDxO,EACGvI,iBACC,2KAEDX,SAAS87B,IACR2F,EAAa3F,EAAOpkB,EAAc,GAClC,GACJ,EAeJrJ,EAAG,cAAc,KACVhH,EAAOQ,OAAO25B,SAAShwB,UAC5BnK,EAAOQ,OAAOkO,qBAAsB,EACpC1O,EAAOgkB,eAAetV,qBAAsB,EAAI,IAElD1H,EAAG,QAAQ,KACJhH,EAAOQ,OAAO25B,SAAShwB,SAC5BiK,GAAc,IAEhBpN,EAAG,gBAAgB,KACZhH,EAAOQ,OAAO25B,SAAShwB,SAC5BiK,GAAc,IAEhBpN,EAAG,iBAAiB,CAACwzB,EAASj6B,KACvBP,EAAOQ,OAAO25B,SAAShwB,SA1BR,SAAC5J,QAAQ,IAARA,MAAWP,EAAOQ,OAAOC,OAC9C,MAAM9D,GAAEA,GAAOqD,EACfrD,EAAGrD,iBACD,4IACAX,SAAS8hC,IACT,IAAIC,EACFvxB,SAASsxB,EAAWlrB,aAAa,iCAAkC,KAAOhP,EAC3D,IAAbA,IAAgBm6B,EAAmB,GACvCD,EAAW5gC,MAAMspB,mBAAsB,GAAEuX,KAAoB,G,CAmB/DrrB,CAAc9O,EAAS,GAE3B,ECzGe,SAAkDR,GAAA,IAApCC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,EAAEuB,KAAEA,GAAMxI,EAC7D,MAAMzD,EAASF,IACfwqB,EAAa,CACX/I,KAAM,CACJ1T,SAAS,EACTwwB,SAAU,EACVxV,SAAU,EACV+P,QAAQ,EACR0F,eAAgB,wBAChBC,iBAAkB,yBAItB76B,EAAO6d,KAAO,CACZ1T,SAAS,GAGX,IAEI2wB,EACAC,EAHAC,EAAe,EACfC,GAAY,EAGhB,MAAMxhB,EAAU,GACVyhB,EAAU,CACdr5B,aAASjD,EACTu8B,gBAAYv8B,EACZw8B,iBAAax8B,EACb0iB,aAAS1iB,EACTy8B,iBAAaz8B,EACb+7B,SAAU,GAENW,EAAQ,CACZrhB,eAAWrb,EACXsb,aAAStb,EACTwc,cAAUxc,EACV0c,cAAU1c,EACV28B,UAAM38B,EACN48B,UAAM58B,EACN68B,UAAM78B,EACN88B,UAAM98B,EACN2G,WAAO3G,EACP6G,YAAQ7G,EACR4c,YAAQ5c,EACR6c,YAAQ7c,EACR+8B,aAAc,GACdC,eAAgB,IAEZ5U,EAAW,CACfzS,OAAG3V,EACH4V,OAAG5V,EACHi9B,mBAAej9B,EACfk9B,mBAAel9B,EACfm9B,cAAUn9B,GAGZ,IAAIo5B,EAAQ,EAeZ,SAASgE,IACP,GAAIviB,EAAQ5gB,OAAS,EAAG,OAAO,EAC/B,MAAMojC,EAAKxiB,EAAQ,GAAG4B,MAChB6gB,EAAKziB,EAAQ,GAAG8B,MAChB4gB,EAAK1iB,EAAQ,GAAG4B,MAChB+gB,EAAK3iB,EAAQ,GAAG8B,MAEtB,OADiBpa,KAAKuc,MAAMye,EAAKF,IAAO,GAAKG,EAAKF,IAAO,EAE3D,CAgBA,SAASG,EAAiBr4B,GACxB,MAAM6T,EAJC7X,EAAO4P,UAAa,eAAiB,IAAG5P,EAAOQ,OAAO8J,aAK7D,QAAItG,EAAExL,OAAO0J,QAAQ2V,IACjB7X,EAAOqK,OAAOpL,QAAQ4C,GAAYA,EAAQkN,SAAS/K,EAAExL,UAASK,OAAS,CAE7E,CAeA,SAASyjC,EAAet4B,GAItB,GAHsB,UAAlBA,EAAE2V,aACJF,EAAQpR,OAAO,EAAGoR,EAAQ5gB,SAEvBwjC,EAAiBr4B,GAAI,OAC1B,MAAMxD,EAASR,EAAOQ,OAAOqd,KAI7B,GAHAid,GAAqB,EACrBC,GAAmB,EACnBthB,EAAQ5V,KAAKG,KACTyV,EAAQ5gB,OAAS,GAArB,CAMA,GAHAiiC,GAAqB,EACrBI,EAAQqB,WAAaP,KAEhBd,EAAQr5B,QAAS,CACpBq5B,EAAQr5B,QAAUmC,EAAExL,OAAOqb,QAAS,IAAG7T,EAAOQ,OAAO8J,4BAChD4wB,EAAQr5B,UAASq5B,EAAQr5B,QAAU7B,EAAOqK,OAAOrK,EAAOyP,cAE7D,IAAI6R,EAAU4Z,EAAQr5B,QAAQxI,cAAe,IAAGmH,EAAOo6B,kBAWvD,GAVItZ,IACFA,EAAUA,EAAQhoB,iBAAiB,kDAAkD,IAEvF4hC,EAAQ5Z,QAAUA,EAEhB4Z,EAAQG,YADN/Z,EACoB7d,EAAey3B,EAAQ5Z,QAAU,IAAG9gB,EAAOo6B,kBAAkB,QAE7Dh8B,GAGnBs8B,EAAQG,YAEX,YADAH,EAAQ5Z,aAAU1iB,GAIpBs8B,EAAQP,SAAWO,EAAQG,YAAY9rB,aAAa,qBAAuB/O,EAAOm6B,QACpF,CACA,GAAIO,EAAQ5Z,QAAS,CACnB,MAAOkb,EAASC,GAxEpB,WACE,GAAIhjB,EAAQ5gB,OAAS,EAAG,MAAO,CAAE0b,EAAG,KAAMC,EAAG,MAC7C,MAAM9R,EAAMw4B,EAAQ5Z,QAAQ3e,wBAC5B,MAAO,EACJ8W,EAAQ,GAAG4B,OAAS5B,EAAQ,GAAG4B,MAAQ5B,EAAQ,GAAG4B,OAAS,EAAI3Y,EAAI6R,GAAKymB,GAExEvhB,EAAQ,GAAG8B,OAAS9B,EAAQ,GAAG8B,MAAQ9B,EAAQ,GAAG8B,OAAS,EAAI7Y,EAAI8R,GAAKwmB,EAE7E,CAgE+B0B,GAC3BxB,EAAQ5Z,QAAQznB,MAAM8iC,gBAAmB,GAAEH,OAAaC,MACxDvB,EAAQ5Z,QAAQznB,MAAMspB,mBAAqB,KAC7C,CACA8X,GAAY,CA/BZ,CAgCF,CACA,SAAS2B,EAAgB54B,GACvB,IAAKq4B,EAAiBr4B,GAAI,OAC1B,MAAMxD,EAASR,EAAOQ,OAAOqd,KACvBA,EAAO7d,EAAO6d,KACdf,EAAerD,EAAQsD,WAAWC,GAAaA,EAASC,YAAcjZ,EAAEiZ,YAC1EH,GAAgB,IAAGrD,EAAQqD,GAAgB9Y,GAE3CyV,EAAQ5gB,OAAS,IAGrBkiC,GAAmB,EACnBG,EAAQ2B,UAAYb,IAEfd,EAAQ5Z,UAIbzD,EAAKma,MAASkD,EAAQ2B,UAAY3B,EAAQqB,WAAcvB,EACpDnd,EAAKma,MAAQkD,EAAQP,WACvB9c,EAAKma,MAAQkD,EAAQP,SAAW,GAAK9c,EAAKma,MAAQkD,EAAQP,SAAW,IAAM,IAEzE9c,EAAKma,MAAQx3B,EAAO2kB,WACtBtH,EAAKma,MAAQx3B,EAAO2kB,SAAW,GAAK3kB,EAAO2kB,SAAWtH,EAAKma,MAAQ,IAAM,IAG3EkD,EAAQ5Z,QAAQznB,MAAMsD,UAAa,4BAA2B0gB,EAAKma,UACrE,CACA,SAAS8E,EAAa94B,GACpB,IAAKq4B,EAAiBr4B,GAAI,OAC1B,GAAsB,UAAlBA,EAAE2V,aAAsC,eAAX3V,EAAEub,KAAuB,OAE1D,MAAM/e,EAASR,EAAOQ,OAAOqd,KACvBA,EAAO7d,EAAO6d,KACdf,EAAerD,EAAQsD,WAAWC,GAAaA,EAASC,YAAcjZ,EAAEiZ,YAC1EH,GAAgB,GAAGrD,EAAQpR,OAAOyU,EAAc,GAE/Cge,GAAuBC,IAI5BD,GAAqB,EACrBC,GAAmB,EACdG,EAAQ5Z,UAEbzD,EAAKma,MAAQ72B,KAAKC,IAAID,KAAKE,IAAIwc,EAAKma,MAAOkD,EAAQP,UAAWn6B,EAAO2kB,UACrE+V,EAAQ5Z,QAAQznB,MAAMspB,mBAAsB,GAAEnjB,EAAOQ,OAAOC,UAC5Dy6B,EAAQ5Z,QAAQznB,MAAMsD,UAAa,4BAA2B0gB,EAAKma,SACnEgD,EAAend,EAAKma,MACpBiD,GAAY,EACO,IAAfpd,EAAKma,QAAakD,EAAQr5B,aAAUjD,IAC1C,CAUA,SAASie,EAAY7Y,GACnB,IAAKq4B,EAAiBr4B,KAtHxB,SAAkCA,GAChC,MAAM/B,EAAY,IAAGjC,EAAOQ,OAAOqd,KAAK+c,iBACxC,QAAI52B,EAAExL,OAAO0J,QAAQD,IAEnB,IAAIjC,EAAOrD,GAAGrD,iBAAiB2I,IAAWhD,QAAQ6lB,GAChDA,EAAY/V,SAAS/K,EAAExL,UACvBK,OAAS,CAIf,CA4G+BkkC,CAAyB/4B,GAAI,OAC1D,MAAM6Z,EAAO7d,EAAO6d,KACpB,IAAKqd,EAAQ5Z,QAAS,OAEtB,GADAthB,EAAOkb,YAAa,GACfogB,EAAMrhB,YAAcihB,EAAQr5B,QAAS,OAErCy5B,EAAMphB,UACTohB,EAAM/1B,MAAQ21B,EAAQ5Z,QAAQld,YAC9Bk3B,EAAM71B,OAASy1B,EAAQ5Z,QAAQ5R,aAC/B4rB,EAAM9f,OAAS9e,EAAaw+B,EAAQG,YAAa,MAAQ,EACzDC,EAAM7f,OAAS/e,EAAaw+B,EAAQG,YAAa,MAAQ,EACzDH,EAAQC,WAAaD,EAAQr5B,QAAQuC,YACrC82B,EAAQE,YAAcF,EAAQr5B,QAAQ6N,aACtCwrB,EAAQG,YAAYxhC,MAAMspB,mBAAqB,OAGjD,MAAM6Z,EAAc1B,EAAM/1B,MAAQsY,EAAKma,MACjCiF,EAAe3B,EAAM71B,OAASoY,EAAKma,MAEzC,KAAIgF,EAAc9B,EAAQC,YAAc8B,EAAe/B,EAAQE,aAA/D,CAUA,GARAE,EAAMC,KAAOp6B,KAAKE,IAAI65B,EAAQC,WAAa,EAAI6B,EAAc,EAAG,GAChE1B,EAAMG,MAAQH,EAAMC,KACpBD,EAAME,KAAOr6B,KAAKE,IAAI65B,EAAQE,YAAc,EAAI6B,EAAe,EAAG,GAClE3B,EAAMI,MAAQJ,EAAME,KAEpBF,EAAMM,eAAernB,EAAIkF,EAAQ5gB,OAAS,EAAI4gB,EAAQ,GAAG4B,MAAQrX,EAAEqX,MACnEigB,EAAMM,eAAepnB,EAAIiF,EAAQ5gB,OAAS,EAAI4gB,EAAQ,GAAG8B,MAAQvX,EAAEuX,OAE9D+f,EAAMphB,UAAY+gB,EAAW,CAChC,GACEj7B,EAAOiJ,iBACL9H,KAAKwL,MAAM2uB,EAAMC,QAAUp6B,KAAKwL,MAAM2uB,EAAM9f,SAC5C8f,EAAMM,eAAernB,EAAI+mB,EAAMK,aAAapnB,GAC3CpT,KAAKwL,MAAM2uB,EAAMG,QAAUt6B,KAAKwL,MAAM2uB,EAAM9f,SAC3C8f,EAAMM,eAAernB,EAAI+mB,EAAMK,aAAapnB,GAGhD,YADA+mB,EAAMrhB,WAAY,GAGpB,IACGja,EAAOiJ,iBACN9H,KAAKwL,MAAM2uB,EAAME,QAAUr6B,KAAKwL,MAAM2uB,EAAM7f,SAC5C6f,EAAMM,eAAepnB,EAAI8mB,EAAMK,aAAannB,GAC3CrT,KAAKwL,MAAM2uB,EAAMI,QAAUv6B,KAAKwL,MAAM2uB,EAAM7f,SAC3C6f,EAAMM,eAAepnB,EAAI8mB,EAAMK,aAAannB,GAGhD,YADA8mB,EAAMrhB,WAAY,EAGtB,CACIjW,EAAE8Z,YACJ9Z,EAAE+X,iBAEJ/X,EAAEia,kBAEFqd,EAAMphB,SAAU,EAChBohB,EAAMlgB,SAAWkgB,EAAMM,eAAernB,EAAI+mB,EAAMK,aAAapnB,EAAI+mB,EAAM9f,OACvE8f,EAAMhgB,SAAWggB,EAAMM,eAAepnB,EAAI8mB,EAAMK,aAAannB,EAAI8mB,EAAM7f,OAEnE6f,EAAMlgB,SAAWkgB,EAAMC,OACzBD,EAAMlgB,SAAWkgB,EAAMC,KAAO,GAAKD,EAAMC,KAAOD,EAAMlgB,SAAW,IAAM,IAErEkgB,EAAMlgB,SAAWkgB,EAAMG,OACzBH,EAAMlgB,SAAWkgB,EAAMG,KAAO,GAAKH,EAAMlgB,SAAWkgB,EAAMG,KAAO,IAAM,IAGrEH,EAAMhgB,SAAWggB,EAAME,OACzBF,EAAMhgB,SAAWggB,EAAME,KAAO,GAAKF,EAAME,KAAOF,EAAMhgB,SAAW,IAAM,IAErEggB,EAAMhgB,SAAWggB,EAAMI,OACzBJ,EAAMhgB,SAAWggB,EAAMI,KAAO,GAAKJ,EAAMhgB,SAAWggB,EAAMI,KAAO,IAAM,IAIpE1U,EAAS6U,gBAAe7U,EAAS6U,cAAgBP,EAAMM,eAAernB,GACtEyS,EAAS8U,gBAAe9U,EAAS8U,cAAgBR,EAAMM,eAAepnB,GACtEwS,EAAS+U,WAAU/U,EAAS+U,SAAWpgC,KAAKc,OACjDuqB,EAASzS,GACN+mB,EAAMM,eAAernB,EAAIyS,EAAS6U,gBAAkBlgC,KAAKc,MAAQuqB,EAAS+U,UAAY,EACzF/U,EAASxS,GACN8mB,EAAMM,eAAepnB,EAAIwS,EAAS8U,gBAAkBngC,KAAKc,MAAQuqB,EAAS+U,UAAY,EACrF56B,KAAK0L,IAAIyuB,EAAMM,eAAernB,EAAIyS,EAAS6U,eAAiB,IAAG7U,EAASzS,EAAI,GAC5EpT,KAAK0L,IAAIyuB,EAAMM,eAAepnB,EAAIwS,EAAS8U,eAAiB,IAAG9U,EAASxS,EAAI,GAChFwS,EAAS6U,cAAgBP,EAAMM,eAAernB,EAC9CyS,EAAS8U,cAAgBR,EAAMM,eAAepnB,EAC9CwS,EAAS+U,SAAWpgC,KAAKc,MAEzBy+B,EAAQG,YAAYxhC,MAAMsD,UAAa,eAAcm+B,EAAMlgB,eAAekgB,EAAMhgB,eArEJ,CAsE9E,CAyCA,SAAS4hB,IACP,MAAMrf,EAAO7d,EAAO6d,KAChBqd,EAAQr5B,SAAW7B,EAAOiT,gBAAkBjT,EAAOyP,cACjDyrB,EAAQ5Z,UACV4Z,EAAQ5Z,QAAQznB,MAAMsD,UAAY,+BAEhC+9B,EAAQG,cACVH,EAAQG,YAAYxhC,MAAMsD,UAAY,sBAGxC0gB,EAAKma,MAAQ,EACbgD,EAAe,EACfE,EAAQr5B,aAAUjD,EAClBs8B,EAAQ5Z,aAAU1iB,EAClBs8B,EAAQG,iBAAcz8B,EAE1B,CAEA,SAASu+B,EAAOn5B,GACd,MAAM6Z,EAAO7d,EAAO6d,KACdrd,EAASR,EAAOQ,OAAOqd,KAE7B,IAAKqd,EAAQr5B,QAAS,CAChBmC,GAAKA,EAAExL,SACT0iC,EAAQr5B,QAAUmC,EAAExL,OAAOqb,QAAS,IAAG7T,EAAOQ,OAAO8J,6BAElD4wB,EAAQr5B,UACP7B,EAAOQ,OAAO0J,SAAWlK,EAAOQ,OAAO0J,QAAQC,SAAWnK,EAAOkK,QACnEgxB,EAAQr5B,QAAUE,EAChB/B,EAAO4J,SACN,IAAG5J,EAAOQ,OAAOyR,oBAClB,GAEFipB,EAAQr5B,QAAU7B,EAAOqK,OAAOrK,EAAOyP,cAG3C,IAAI6R,EAAU4Z,EAAQr5B,QAAQxI,cAAe,IAAGmH,EAAOo6B,kBACnDtZ,IACFA,EAAUA,EAAQhoB,iBAAiB,kDAAkD,IAEvF4hC,EAAQ5Z,QAAUA,EAEhB4Z,EAAQG,YADN/Z,EACoB7d,EAAey3B,EAAQ5Z,QAAU,IAAG9gB,EAAOo6B,kBAAkB,QAE7Dh8B,CAE1B,CACA,IAAKs8B,EAAQ5Z,UAAY4Z,EAAQG,YAAa,OAQ9C,IAAI+B,EACAC,EACAC,EACAC,EACA/f,EACAC,EACA+f,EACAC,EACAC,EACAC,EACAX,EACAC,EACAW,EACAC,EACAC,EACAC,EACA5C,EACAC,EAxBAp7B,EAAOQ,OAAOiL,UAChBzL,EAAOU,UAAU7G,MAAM8H,SAAW,SAClC3B,EAAOU,UAAU7G,MAAMmkC,YAAc,QAGvC9C,EAAQr5B,QAAQQ,UAAUC,IAAK,GAAE9B,EAAOq6B,yBAqBJ,IAAzBS,EAAMK,aAAapnB,GAAqBvQ,GACjDo5B,EAASp5B,EAAEqX,MACXgiB,EAASr5B,EAAEuX,QAEX6hB,EAAS9B,EAAMK,aAAapnB,EAC5B8oB,EAAS/B,EAAMK,aAAannB,GAG9B,MAAMypB,EAA8B,iBAANj6B,EAAiBA,EAAI,KAC9B,IAAjBg3B,GAAsBiD,IACxBb,OAASx+B,EACTy+B,OAASz+B,GAGXif,EAAKma,MACHiG,GAAkB/C,EAAQG,YAAY9rB,aAAa,qBAAuB/O,EAAOm6B,SACnFK,EACEiD,GAAkB/C,EAAQG,YAAY9rB,aAAa,qBAAuB/O,EAAOm6B,UAC/E32B,GAAwB,IAAjBg3B,GAAsBiD,GAmC/BT,EAAa,EACbC,EAAa,IAnCbtC,EAAaD,EAAQr5B,QAAQuC,YAC7Bg3B,EAAcF,EAAQr5B,QAAQ6N,aAC9B4tB,EAAU76B,EAAcy4B,EAAQr5B,SAASsB,KAAO7G,EAAO2G,QACvDs6B,EAAU96B,EAAcy4B,EAAQr5B,SAASqB,IAAM5G,EAAOyG,QACtDya,EAAQ8f,EAAUnC,EAAa,EAAIiC,EACnC3f,EAAQ8f,EAAUnC,EAAc,EAAIiC,EAEpCK,EAAaxC,EAAQ5Z,QAAQld,YAC7Bu5B,EAAczC,EAAQ5Z,QAAQ5R,aAC9BstB,EAAcU,EAAa7f,EAAKma,MAChCiF,EAAeU,EAAc9f,EAAKma,MAElC4F,EAAgBz8B,KAAKE,IAAI85B,EAAa,EAAI6B,EAAc,EAAG,GAC3Da,EAAgB18B,KAAKE,IAAI+5B,EAAc,EAAI6B,EAAe,EAAG,GAC7Da,GAAiBF,EACjBG,GAAiBF,EAEjBL,EAAahgB,EAAQK,EAAKma,MAC1ByF,EAAahgB,EAAQI,EAAKma,MAEtBwF,EAAaI,IACfJ,EAAaI,GAEXJ,EAAaM,IACfN,EAAaM,GAGXL,EAAaI,IACfJ,EAAaI,GAEXJ,EAAaM,IACfN,EAAaM,IAMjB7C,EAAQG,YAAYxhC,MAAMspB,mBAAqB,QAC/C+X,EAAQG,YAAYxhC,MAAMsD,UAAa,eAAcqgC,QAAiBC,SACtEvC,EAAQ5Z,QAAQznB,MAAMspB,mBAAqB,QAC3C+X,EAAQ5Z,QAAQznB,MAAMsD,UAAa,4BAA2B0gB,EAAKma,QACrE,CACA,SAASkG,IACP,MAAMrgB,EAAO7d,EAAO6d,KACdrd,EAASR,EAAOQ,OAAOqd,KAE7B,IAAKqd,EAAQr5B,QAAS,CAChB7B,EAAOQ,OAAO0J,SAAWlK,EAAOQ,OAAO0J,QAAQC,SAAWnK,EAAOkK,QACnEgxB,EAAQr5B,QAAUE,EAAgB/B,EAAO4J,SAAW,IAAG5J,EAAOQ,OAAOyR,oBAAoB,GAEzFipB,EAAQr5B,QAAU7B,EAAOqK,OAAOrK,EAAOyP,aAEzC,IAAI6R,EAAU4Z,EAAQr5B,QAAQxI,cAAe,IAAGmH,EAAOo6B,kBACnDtZ,IACFA,EAAUA,EAAQhoB,iBAAiB,kDAAkD,IAEvF4hC,EAAQ5Z,QAAUA,EAEhB4Z,EAAQG,YADN/Z,EACoB7d,EAAey3B,EAAQ5Z,QAAU,IAAG9gB,EAAOo6B,kBAAkB,QAE7Dh8B,CAE1B,CACKs8B,EAAQ5Z,SAAY4Z,EAAQG,cAC7Br7B,EAAOQ,OAAOiL,UAChBzL,EAAOU,UAAU7G,MAAM8H,SAAW,GAClC3B,EAAOU,UAAU7G,MAAMmkC,YAAc,IAEvCngB,EAAKma,MAAQ,EACbgD,EAAe,EACfE,EAAQG,YAAYxhC,MAAMspB,mBAAqB,QAC/C+X,EAAQG,YAAYxhC,MAAMsD,UAAY,qBACtC+9B,EAAQ5Z,QAAQznB,MAAMspB,mBAAqB,QAC3C+X,EAAQ5Z,QAAQznB,MAAMsD,UAAY,8BAElC+9B,EAAQr5B,QAAQQ,UAAU4M,OAAQ,GAAEzO,EAAOq6B,oBAC3CK,EAAQr5B,aAAUjD,EACpB,CAGA,SAASu/B,EAAWn6B,GAClB,MAAM6Z,EAAO7d,EAAO6d,KAEhBA,EAAKma,OAAwB,IAAfna,EAAKma,MAErBkG,IAGAf,EAAOn5B,EAEX,CAEA,SAASo6B,IAOP,MAAO,CAAEvE,kBANe75B,EAAOQ,OAAOiiB,kBAClC,CAAEV,SAAS,EAAMH,SAAS,GAKJyc,2BAHQr+B,EAAOQ,OAAOiiB,kBAC5C,CAAEV,SAAS,EAAOH,SAAS,GAGjC,CAGA,SAAS6C,IACP,MAAM5G,EAAO7d,EAAO6d,KACpB,GAAIA,EAAK1T,QAAS,OAClB0T,EAAK1T,SAAU,EACf,MAAM0vB,gBAAEA,EAAewE,0BAAEA,GAA8BD,IAIvDp+B,EAAOU,UAAU1H,iBAAiB,cAAesjC,EAAgBzC,GACjE75B,EAAOU,UAAU1H,iBAAiB,cAAe4jC,EAAiByB,GAClE,CAAC,YAAa,gBAAiB,cAAc1lC,SAASouB,IACpD/mB,EAAOU,UAAU1H,iBAAiB+tB,EAAW+V,EAAcjD,EAAgB,IAI7E75B,EAAOU,UAAU1H,iBAAiB,cAAe6jB,EAAawhB,EAChE,CACA,SAAS7Z,IACP,MAAM3G,EAAO7d,EAAO6d,KACpB,IAAKA,EAAK1T,QAAS,OACnB0T,EAAK1T,SAAU,EAEf,MAAM0vB,gBAAEA,EAAewE,0BAAEA,GAA8BD,IAGvDp+B,EAAOU,UAAUzH,oBAAoB,cAAeqjC,EAAgBzC,GACpE75B,EAAOU,UAAUzH,oBAAoB,cAAe2jC,EAAiByB,GACrE,CAAC,YAAa,gBAAiB,cAAc1lC,SAASouB,IACpD/mB,EAAOU,UAAUzH,oBAAoB8tB,EAAW+V,EAAcjD,EAAgB,IAIhF75B,EAAOU,UAAUzH,oBAAoB,cAAe4jB,EAAawhB,EACnE,CA1gBA/lC,OAAOgmC,eAAet+B,EAAO6d,KAAM,QAAS,CAC1C0gB,IAAG,IACMvG,EAETwG,IAAInZ,GACF,GAAI2S,IAAU3S,EAAO,CACnB,MAAM/D,EAAU4Z,EAAQ5Z,QAClBzf,EAAUq5B,EAAQr5B,QACxB0G,EAAK,aAAc8c,EAAO/D,EAASzf,EACrC,CACAm2B,EAAQ3S,CACV,IAigBFre,EAAG,QAAQ,KACLhH,EAAOQ,OAAOqd,KAAK1T,SACrBsa,GACF,IAEFzd,EAAG,WAAW,KACZwd,GAAS,IAEXxd,EAAG,cAAc,CAAC8jB,EAAI9mB,KACfhE,EAAO6d,KAAK1T,SA5XnB,SAAsBnG,GACpB,MAAMmB,EAASnF,EAAOmF,OACjB+1B,EAAQ5Z,UACTga,EAAMrhB,YACN9U,EAAOE,SAAWrB,EAAE8Z,YAAY9Z,EAAE+X,iBACtCuf,EAAMrhB,WAAY,EAClBqhB,EAAMK,aAAapnB,EAAIvQ,EAAEqX,MACzBigB,EAAMK,aAAannB,EAAIxQ,EAAEuX,OAC3B,CAqXEhC,CAAavV,EAAE,IAEjBgD,EAAG,YAAY,CAAC8jB,EAAI9mB,KACbhE,EAAO6d,KAAK1T,SA5RnB,WACE,MAAM0T,EAAO7d,EAAO6d,KACpB,IAAKqd,EAAQ5Z,QAAS,OACtB,IAAKga,EAAMrhB,YAAcqhB,EAAMphB,QAG7B,OAFAohB,EAAMrhB,WAAY,OAClBqhB,EAAMphB,SAAU,GAGlBohB,EAAMrhB,WAAY,EAClBqhB,EAAMphB,SAAU,EAChB,IAAIukB,EAAoB,IACpBC,EAAoB,IACxB,MAAMC,EAAoB3X,EAASzS,EAAIkqB,EACjCG,EAAetD,EAAMlgB,SAAWujB,EAChCE,EAAoB7X,EAASxS,EAAIkqB,EACjCI,EAAexD,EAAMhgB,SAAWujB,EAGnB,IAAf7X,EAASzS,IACXkqB,EAAoBt9B,KAAK0L,KAAK+xB,EAAetD,EAAMlgB,UAAY4L,EAASzS,IACvD,IAAfyS,EAASxS,IACXkqB,EAAoBv9B,KAAK0L,KAAKiyB,EAAexD,EAAMhgB,UAAY0L,EAASxS,IAC1E,MAAMuqB,EAAmB59B,KAAKC,IAAIq9B,EAAmBC,GAErDpD,EAAMlgB,SAAWwjB,EACjBtD,EAAMhgB,SAAWwjB,EAGjB,MAAM9B,EAAc1B,EAAM/1B,MAAQsY,EAAKma,MACjCiF,EAAe3B,EAAM71B,OAASoY,EAAKma,MACzCsD,EAAMC,KAAOp6B,KAAKE,IAAI65B,EAAQC,WAAa,EAAI6B,EAAc,EAAG,GAChE1B,EAAMG,MAAQH,EAAMC,KACpBD,EAAME,KAAOr6B,KAAKE,IAAI65B,EAAQE,YAAc,EAAI6B,EAAe,EAAG,GAClE3B,EAAMI,MAAQJ,EAAME,KACpBF,EAAMlgB,SAAWja,KAAKC,IAAID,KAAKE,IAAIi6B,EAAMlgB,SAAUkgB,EAAMG,MAAOH,EAAMC,MACtED,EAAMhgB,SAAWna,KAAKC,IAAID,KAAKE,IAAIi6B,EAAMhgB,SAAUggB,EAAMI,MAAOJ,EAAME,MAEtEN,EAAQG,YAAYxhC,MAAMspB,mBAAsB,GAAE4b,MAClD7D,EAAQG,YAAYxhC,MAAMsD,UAAa,eAAcm+B,EAAMlgB,eAAekgB,EAAMhgB,eAClF,CAsPEgE,EAAa,IAEftY,EAAG,aAAa,CAAC8jB,EAAI9mB,MAEhBhE,EAAO8U,WACR9U,EAAOQ,OAAOqd,KAAK1T,SACnBnK,EAAO6d,KAAK1T,SACZnK,EAAOQ,OAAOqd,KAAKqX,QAEnBiJ,EAAWn6B,EACb,IAEFgD,EAAG,iBAAiB,KACdhH,EAAO6d,KAAK1T,SAAWnK,EAAOQ,OAAOqd,KAAK1T,SAC5C+yB,GACF,IAEFl2B,EAAG,eAAe,KACZhH,EAAO6d,KAAK1T,SAAWnK,EAAOQ,OAAOqd,KAAK1T,SAAWnK,EAAOQ,OAAOiL,SACrEyxB,GACF,IAGF5kC,OAAO+Q,OAAOrJ,EAAO6d,KAAM,CACzB4G,SACAD,UACAwa,GAAI7B,EACJ8B,IAAKf,EACLhJ,OAAQiJ,GAEZ,ECpnBe,SAAkDp+B,GAAA,IAA9BC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,GAAIjH,EAa7D,SAASm/B,EAAa3qB,EAAGC,GACvB,MAAM2qB,EAAgB,WACpB,IAAIC,EACAC,EACAC,EACJ,MAAO,CAACC,EAAOroB,KAGb,IAFAmoB,GAAY,EACZD,EAAWG,EAAM1mC,OACVumC,EAAWC,EAAW,GAC3BC,EAASF,EAAWC,GAAa,EAC7BE,EAAMD,IAAUpoB,EAClBmoB,EAAWC,EAEXF,EAAWE,EAGf,OAAOF,CAAQ,CAEnB,CAjBsB,GAwBtB,IAAII,EACAC,EAeJ,OAtBAlkC,KAAKgZ,EAAIA,EACThZ,KAAKiZ,EAAIA,EACTjZ,KAAKgc,UAAYhD,EAAE1b,OAAS,EAO5B0C,KAAKmkC,YAAc,SAAqBvD,GACtC,OAAKA,GAGLsD,EAAKN,EAAa5jC,KAAKgZ,EAAG4nB,GAC1BqD,EAAKC,EAAK,GAKNtD,EAAK5gC,KAAKgZ,EAAEirB,KAAQjkC,KAAKiZ,EAAEirB,GAAMlkC,KAAKiZ,EAAEgrB,KAASjkC,KAAKgZ,EAAEkrB,GAAMlkC,KAAKgZ,EAAEirB,IAAOjkC,KAAKiZ,EAAEgrB,IATvE,C,EAYXjkC,IACT,CAiFA,SAASokC,IACF3/B,EAAO+Y,WAAWC,SACnBhZ,EAAO+Y,WAAW6mB,SACpB5/B,EAAO+Y,WAAW6mB,YAAShhC,SACpBoB,EAAO+Y,WAAW6mB,OAE7B,CA7IAhZ,EAAa,CACX7N,WAAY,CACVC,aAASpa,EACTihC,SAAS,EACTC,GAAI,WAIR9/B,EAAO+Y,WAAa,CAClBC,aAASpa,GAqIXoI,EAAG,cAAc,KACf,GACoB,oBAAX1K,SACsC,iBAArC0D,EAAOQ,OAAOuY,WAAWC,SAC/BhZ,EAAOQ,OAAOuY,WAAWC,mBAAmBxa,aAHhD,CAKE,MAAMuhC,EAAiBllC,SAASxB,cAAc2G,EAAOQ,OAAOuY,WAAWC,SACvE,GAAI+mB,GAAkBA,EAAe//B,OACnCA,EAAO+Y,WAAWC,QAAU+mB,EAAe//B,YACtC,GAAI+/B,EAAgB,CACzB,MAAMC,EAAsBh8B,IAC1BhE,EAAO+Y,WAAWC,QAAUhV,EAAEuuB,OAAO,GACrCvyB,EAAO6I,SACPk3B,EAAe9mC,oBAAoB,OAAQ+mC,EAAmB,EAEhED,EAAe/mC,iBAAiB,OAAQgnC,EAC1C,CAEF,MACAhgC,EAAO+Y,WAAWC,QAAUhZ,EAAOQ,OAAOuY,WAAWC,OAAO,IAE9DhS,EAAG,UAAU,KACX24B,GAAc,IAEhB34B,EAAG,UAAU,KACX24B,GAAc,IAEhB34B,EAAG,kBAAkB,KACnB24B,GAAc,IAEhB34B,EAAG,gBAAgB,CAAC8jB,EAAI1qB,EAAWiU,KAC5BrU,EAAO+Y,WAAWC,SACvBhZ,EAAO+Y,WAAW3E,aAAahU,EAAWiU,EAAa,IAEzDrN,EAAG,iBAAiB,CAAC8jB,EAAIvqB,EAAU8T,KAC5BrU,EAAO+Y,WAAWC,SACvBhZ,EAAO+Y,WAAW1J,cAAc9O,EAAU8T,EAAa,IAGzD/b,OAAO+Q,OAAOrJ,EAAO+Y,WAAY,CAC/B3E,aAvHF,SAAsB6rB,EAAI5rB,GACxB,MAAM6rB,EAAalgC,EAAO+Y,WAAWC,QACrC,IAAIpI,EACAuvB,EACJ,MAAM7Z,EAAStmB,EAAO3H,YACtB,SAAS+nC,EAAuBlnB,GAK9B,MAAM9Y,EAAYJ,EAAO8J,cAAgB9J,EAAOI,UAAYJ,EAAOI,UAC/B,UAAhCJ,EAAOQ,OAAOuY,WAAW+mB,MAlBjC,SAAgC5mB,GACzBlZ,EAAO+Y,WAAW6mB,SACrB5/B,EAAO+Y,WAAW6mB,OAAS5/B,EAAOQ,OAAO6M,KACrC,IAAI6xB,EAAal/B,EAAOyK,WAAYyO,EAAEzO,YACtC,IAAIy0B,EAAal/B,EAAOwK,SAAU0O,EAAE1O,UAE5C,CAaM61B,CAAuBnnB,GAGvBinB,GAAuBngC,EAAO+Y,WAAW6mB,OAAOF,aAAat/B,IAG1D+/B,GAAuD,cAAhCngC,EAAOQ,OAAOuY,WAAW+mB,KACnDlvB,GACGsI,EAAEpI,eAAiBoI,EAAE5I,iBAAmBtQ,EAAO8Q,eAAiB9Q,EAAOsQ,gBAC1E6vB,GAAuB//B,EAAYJ,EAAOsQ,gBAAkBM,EAAasI,EAAE5I,gBAGzEtQ,EAAOQ,OAAOuY,WAAW8mB,UAC3BM,EAAsBjnB,EAAEpI,eAAiBqvB,GAE3CjnB,EAAEvI,eAAewvB,GACjBjnB,EAAE9E,aAAa+rB,EAAqBngC,GACpCkZ,EAAEnG,oBACFmG,EAAEpH,qBACJ,CACA,GAAIvP,MAAMC,QAAQ09B,GAChB,IAAK,IAAIphC,EAAI,EAAGA,EAAIohC,EAAWrnC,OAAQiG,GAAK,EACtCohC,EAAWphC,KAAOuV,GAAgB6rB,EAAWphC,aAAcwnB,GAC7D8Z,EAAuBF,EAAWphC,SAG7BohC,aAAsB5Z,GAAUjS,IAAiB6rB,GAC1DE,EAAuBF,EAE3B,EA+EE7wB,cA9EF,SAAuB9O,EAAU8T,GAC/B,MAAMiS,EAAStmB,EAAO3H,YAChB6nC,EAAalgC,EAAO+Y,WAAWC,QACrC,IAAIla,EACJ,SAASwhC,EAAwBpnB,GAC/BA,EAAE7J,cAAc9O,EAAUP,GACT,IAAbO,IACF2Y,EAAEpD,kBACEoD,EAAE1Y,OAAOqR,YACXtV,GAAS,KACP2c,EAAEhK,kBAAkB,IAGxBpL,EAAqBoV,EAAExY,WAAW,KAC3Bw/B,GACLhnB,EAAEnD,eAAe,IAGvB,CACA,GAAIxT,MAAMC,QAAQ09B,GAChB,IAAKphC,EAAI,EAAGA,EAAIohC,EAAWrnC,OAAQiG,GAAK,EAClCohC,EAAWphC,KAAOuV,GAAgB6rB,EAAWphC,aAAcwnB,GAC7Dga,EAAwBJ,EAAWphC,SAG9BohC,aAAsB5Z,GAAUjS,IAAiB6rB,GAC1DI,EAAwBJ,EAE5B,GAoDF,EC1Le,SAA4CngC,GAAA,IAA9BC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,GAAIjH,EACvD6mB,EAAa,CACX2Z,KAAM,CACJp2B,SAAS,EACTq2B,kBAAmB,sBACnBC,iBAAkB,iBAClBC,iBAAkB,aAClBC,kBAAmB,0BACnBC,iBAAkB,yBAClBC,wBAAyB,wBACzBC,kBAAmB,+BACnBC,iBAAkB,KAClBC,gCAAiC,KACjCC,2BAA4B,KAC5BC,UAAW,QACX/kC,GAAI,QAIR6D,EAAOugC,KAAO,CACZY,SAAS,GAGX,IAAIC,EAAa,KAEjB,SAASC,EAAOC,GACd,MAAMC,EAAeH,EACO,IAAxBG,EAAa1oC,SACjB0oC,EAAa7X,UAAY,GACzB6X,EAAa7X,UAAY4X,EAC3B,CAEA,MAAMlN,EAAqBz3B,IACpB4F,MAAMC,QAAQ7F,KAAKA,EAAK,CAACA,GAAIsC,QAAQ+E,KAAQA,KAC3CrH,GAOT,SAAS6kC,EAAgB7kC,IACvBA,EAAKy3B,EAAkBz3B,IACpBhE,SAAS87B,IACVA,EAAM36B,aAAa,WAAY,IAAI,GAEvC,CACA,SAAS2nC,EAAmB9kC,IAC1BA,EAAKy3B,EAAkBz3B,IACpBhE,SAAS87B,IACVA,EAAM36B,aAAa,WAAY,KAAK,GAExC,CACA,SAAS4nC,EAAU/kC,EAAIglC,IACrBhlC,EAAKy3B,EAAkBz3B,IACpBhE,SAAS87B,IACVA,EAAM36B,aAAa,OAAQ6nC,EAAK,GAEpC,CACA,SAASC,EAAqBjlC,EAAIklC,IAChCllC,EAAKy3B,EAAkBz3B,IACpBhE,SAAS87B,IACVA,EAAM36B,aAAa,uBAAwB+nC,EAAY,GAE3D,CAOA,SAASC,EAAWnlC,EAAIgN,IACtBhN,EAAKy3B,EAAkBz3B,IACpBhE,SAAS87B,IACVA,EAAM36B,aAAa,aAAc6P,EAAM,GAE3C,CAaA,SAASo4B,EAAUplC,IACjBA,EAAKy3B,EAAkBz3B,IACpBhE,SAAS87B,IACVA,EAAM36B,aAAa,iBAAiB,EAAK,GAE7C,CACA,SAASkoC,EAASrlC,IAChBA,EAAKy3B,EAAkBz3B,IACpBhE,SAAS87B,IACVA,EAAM36B,aAAa,iBAAiB,EAAM,GAE9C,CAEA,SAASmoC,EAAkBj+B,GACzB,GAAkB,KAAdA,EAAEwrB,SAAgC,KAAdxrB,EAAEwrB,QAAgB,OAC1C,MAAMhvB,EAASR,EAAOQ,OAAO+/B,KACvB1mB,EAAW7V,EAAExL,OAEjBwH,EAAO+0B,YACP/0B,EAAO+0B,WAAWp4B,KACjBkd,IAAa7Z,EAAO+0B,WAAWp4B,IAAMqD,EAAO+0B,WAAWp4B,GAAGoS,SAAS/K,EAAExL,WAEjEwL,EAAExL,OAAO0J,QAAQonB,GAAkBtpB,EAAOQ,OAAOu0B,WAAWiB,gBAE/Dh2B,EAAOqgB,YAAcrgB,EAAOqgB,WAAWC,QAAUzG,IAAa7Z,EAAOqgB,WAAWC,SAC5EtgB,EAAOgR,QAAUhR,EAAOQ,OAAO6M,MACnCrN,EAAOuW,YAELvW,EAAOgR,MACTqwB,EAAO7gC,EAAOogC,kBAEdS,EAAO7gC,EAAOkgC,mBAGd1gC,EAAOqgB,YAAcrgB,EAAOqgB,WAAWE,QAAU1G,IAAa7Z,EAAOqgB,WAAWE,SAC5EvgB,EAAO+Q,cAAgB/Q,EAAOQ,OAAO6M,MACzCrN,EAAOgX,YAELhX,EAAO+Q,YACTswB,EAAO7gC,EAAOmgC,mBAEdU,EAAO7gC,EAAOigC,mBAKhBzgC,EAAO+0B,YACPlb,EAAS3X,QAAQonB,GAAkBtpB,EAAOQ,OAAOu0B,WAAWiB,eAE5Dnc,EAASqoB,QAEb,CA0BA,SAASC,IACP,OAAOniC,EAAO+0B,YAAc/0B,EAAO+0B,WAAW4B,SAAW32B,EAAO+0B,WAAW4B,QAAQ99B,MACrF,CAEA,SAASupC,IACP,OAAOD,KAAmBniC,EAAOQ,OAAOu0B,WAAWC,SACrD,CAwBA,MAAMqN,EAAY,CAAC1lC,EAAI2lC,EAAWhB,KAChCE,EAAgB7kC,GACG,WAAfA,EAAG+3B,UACLgN,EAAU/kC,EAAI,UACdA,EAAG3D,iBAAiB,UAAWipC,IAEjCH,EAAWnlC,EAAI2kC,GAzIjB,SAAuB3kC,EAAI4lC,IACzB5lC,EAAKy3B,EAAkBz3B,IACpBhE,SAAS87B,IACVA,EAAM36B,aAAa,gBAAiByoC,EAAS,GAEjD,CAqIEC,CAAc7lC,EAAI2lC,EAAU,EAExBG,EAAoB,KACxBziC,EAAOugC,KAAKY,SAAU,CAAI,EAEtBuB,EAAkB,KACtB1mC,uBAAsB,KACpBA,uBAAsB,KACfgE,EAAOsH,YACVtH,EAAOugC,KAAKY,SAAU,EACxB,GACA,GACF,EAGEwB,EAAe3+B,IACnB,GAAIhE,EAAOugC,KAAKY,QAAS,OACzB,MAAMt/B,EAAUmC,EAAExL,OAAOqb,QAAS,IAAG7T,EAAOQ,OAAO8J,4BACnD,IAAKzI,IAAY7B,EAAOqK,OAAO9D,SAAS1E,GAAU,OAClD,MAAM+gC,EAAW5iC,EAAOqK,OAAOnL,QAAQ2C,KAAa7B,EAAOyP,YACrDozB,EACJ7iC,EAAOQ,OAAOkO,qBACd1O,EAAOwP,eACPxP,EAAOwP,cAAcjJ,SAAS1E,GAC5B+gC,GAAYC,GACZ7+B,EAAE8+B,oBAAsB9+B,EAAE8+B,mBAAmBC,mBAC7C/iC,EAAOiJ,eACTjJ,EAAOrD,GAAGqG,WAAa,EAEvBhD,EAAOrD,GAAGmG,UAAY,EAExB9C,EAAOuV,QAAQvV,EAAOqK,OAAOnL,QAAQ2C,GAAU,GAAE,EAG7CiK,EAAa,KACjB,MAAMtL,EAASR,EAAOQ,OAAO+/B,KACzB//B,EAAOygC,4BACTW,EAAqB5hC,EAAOqK,OAAQ7J,EAAOygC,4BAEzCzgC,EAAO0gC,WACTQ,EAAU1hC,EAAOqK,OAAQ7J,EAAO0gC,WAGlC,MAAM32B,EAAevK,EAAOqK,OAAOxR,OAC/B2H,EAAOsgC,mBACT9gC,EAAOqK,OAAO1R,SAAQ,CAACkJ,EAASuG,KAC9B,MAAMwF,EAAa5N,EAAOQ,OAAO6M,KAC7BlE,SAAStH,EAAQ0N,aAAa,2BAA4B,IAC1DnH,EAIJ05B,EAAWjgC,EAHcrB,EAAOsgC,kBAC7BtjC,QAAQ,gBAAiBoQ,EAAa,GACtCpQ,QAAQ,uBAAwB+M,GACE,GAEzC,EAGI4X,EAAO,KACX,MAAM3hB,EAASR,EAAOQ,OAAO+/B,KAE7BvgC,EAAOrD,GAAG+b,OAAO0oB,GAGjB,MAAMtc,EAAc9kB,EAAOrD,GACvB6D,EAAOwgC,iCACTY,EAAqB9c,EAAatkB,EAAOwgC,iCAEvCxgC,EAAOugC,kBACTe,EAAWhd,EAAatkB,EAAOugC,kBAIjC,MAAMrgC,EAAYV,EAAOU,UACnB4hC,EACJ9hC,EAAOrE,IAAMuE,EAAU6O,aAAa,OAAU,kBAhPzBrL,EAgP0D,QAhPtD,IAAJA,MAAO,IAEvB,IAAI8+B,OAAO9+B,GAAM1G,QAAQ,MADb,IAAM2D,KAAK8hC,MAAM,GAAK9hC,KAAK+hC,UAAUplC,SAAS,QADnE,IAAyBoG,EAiPvB,MAAMi/B,EAAOnjC,EAAOQ,OAAOogB,UAAY5gB,EAAOQ,OAAOogB,SAASzW,QAAU,MAAQ,SAzMlF,IAAqBhO,IA0MAmmC,EAzMdlO,EAyMG1zB,GAxML/H,SAAS87B,IACVA,EAAM36B,aAAa,KAAMqC,EAAG,IAGhC,SAAmBQ,EAAIwmC,IACrBxmC,EAAKy3B,EAAkBz3B,IACpBhE,SAAS87B,IACVA,EAAM36B,aAAa,YAAaqpC,EAAK,GAEzC,CAgMEC,CAAU1iC,EAAWyiC,GAGrBr3B,IAGA,IAAIwU,OAAEA,EAAMC,OAAEA,GAAWvgB,EAAOqgB,WAAargB,EAAOqgB,WAAa,GAYjE,GAXAC,EAAS8T,EAAkB9T,GAC3BC,EAAS6T,EAAkB7T,GAEvBD,GACFA,EAAO3nB,SAASgE,GAAO0lC,EAAU1lC,EAAI2lC,EAAW9hC,EAAOkgC,oBAErDngB,GACFA,EAAO5nB,SAASgE,GAAO0lC,EAAU1lC,EAAI2lC,EAAW9hC,EAAOigC,oBAIrD2B,IAA0B,EACP7/B,MAAMC,QAAQxC,EAAO+0B,WAAWp4B,IACjDqD,EAAO+0B,WAAWp4B,GAClB,CAACqD,EAAO+0B,WAAWp4B,KACVhE,SAASgE,IACpBA,EAAG3D,iBAAiB,UAAWipC,EAAkB,GAErD,CAGAjiC,EAAOrD,GAAG3D,iBAAiB,QAAS2pC,GAAa,GACjD3iC,EAAOrD,GAAG3D,iBAAiB,cAAeypC,GAAmB,GAC7DziC,EAAOrD,GAAG3D,iBAAiB,YAAa0pC,GAAiB,EAAK,EA8BhE17B,EAAG,cAAc,KACfo6B,EAAa1nC,EAAc,OAAQsG,EAAOQ,OAAO+/B,KAAKC,mBACtDY,EAAWtnC,aAAa,YAAa,aACrCsnC,EAAWtnC,aAAa,cAAe,QACnCkG,EAAO4P,WACTwxB,EAAWtnC,aAAa,OAAQ,gBAClC,IAGFkN,EAAG,aAAa,KACThH,EAAOQ,OAAO+/B,KAAKp2B,SACxBgY,GAAM,IAERnb,EAAG,kEAAkE,KAC9DhH,EAAOQ,OAAO+/B,KAAKp2B,SACxB2B,GAAY,IAEd9E,EAAG,yCAAyC,KACrChH,EAAOQ,OAAO+/B,KAAKp2B,SAxN1B,WACE,GAAInK,EAAOQ,OAAO6M,MAAQrN,EAAOQ,OAAOuW,SAAW/W,EAAOqgB,WAAY,OACtE,MAAMC,OAAEA,EAAMC,OAAEA,GAAWvgB,EAAOqgB,WAE9BE,IACEvgB,EAAO+Q,aACTgxB,EAAUxhB,GACVkhB,EAAmBlhB,KAEnByhB,EAASzhB,GACTihB,EAAgBjhB,KAGhBD,IACEtgB,EAAOgR,OACT+wB,EAAUzhB,GACVmhB,EAAmBnhB,KAEnB0hB,EAAS1hB,GACTkhB,EAAgBlhB,IAGtB,CAmME+iB,EAAkB,IAEpBr8B,EAAG,oBAAoB,KAChBhH,EAAOQ,OAAO+/B,KAAKp2B,SA5L1B,WACE,MAAM3J,EAASR,EAAOQ,OAAO+/B,KACxB4B,KACLniC,EAAO+0B,WAAW4B,QAAQh+B,SAASo+B,IAC7B/2B,EAAOQ,OAAOu0B,WAAWC,YAC3BwM,EAAgBzK,GACX/2B,EAAOQ,OAAOu0B,WAAWO,eAC5BoM,EAAU3K,EAAU,UACpB+K,EACE/K,EACAv2B,EAAOqgC,wBAAwBrjC,QAAQ,gBAAiB8F,EAAayzB,GAAY,MAInFA,EAAS70B,QAAS,IAAGlC,EAAOQ,OAAOu0B,WAAWkB,qBAChDc,EAASj9B,aAAa,eAAgB,QAEtCi9B,EAASzd,gBAAgB,eAC3B,GAEJ,CAyKEgqB,EAAkB,IAEpBt8B,EAAG,WAAW,KACPhH,EAAOQ,OAAO+/B,KAAKp2B,SAtD1B,WACMi3B,GAAcA,EAAWvoC,OAAS,GAAGuoC,EAAWnyB,SACpD,IAAIqR,OAAEA,EAAMC,OAAEA,GAAWvgB,EAAOqgB,WAAargB,EAAOqgB,WAAa,GACjEC,EAAS8T,EAAkB9T,GAC3BC,EAAS6T,EAAkB7T,GACvBD,GACFA,EAAO3nB,SAASgE,GAAOA,EAAG1D,oBAAoB,UAAWgpC,KAEvD1hB,GACFA,EAAO5nB,SAASgE,GAAOA,EAAG1D,oBAAoB,UAAWgpC,KAIvDG,MACmB7/B,MAAMC,QAAQxC,EAAO+0B,WAAWp4B,IACjDqD,EAAO+0B,WAAWp4B,GAClB,CAACqD,EAAO+0B,WAAWp4B,KACVhE,SAASgE,IACpBA,EAAG1D,oBAAoB,UAAWgpC,EAAkB,IAKxDjiC,EAAOrD,GAAG1D,oBAAoB,QAAS0pC,GAAa,GACpD3iC,EAAOrD,GAAG1D,oBAAoB,cAAewpC,GAAmB,GAChEziC,EAAOrD,GAAG1D,oBAAoB,YAAaypC,GAAiB,EAC9D,CA6BEha,EAAS,GAEb,EClXe,SAA+C3oB,GAAA,IAA9BC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,GAAIjH,EAC1D6mB,EAAa,CACX3rB,QAAS,CACPkP,SAAS,EACTo5B,KAAM,GACNroC,cAAc,EACdtC,IAAK,SACL4qC,WAAW,KAIf,IAAI9vB,GAAc,EACd+vB,EAAQ,GAEZ,MAAMC,EAAWC,GACRA,EACJ7lC,WACAN,QAAQ,OAAQ,KAChBA,QAAQ,WAAY,IACpBA,QAAQ,OAAQ,KAChBA,QAAQ,MAAO,IACfA,QAAQ,MAAO,IAGdomC,EAAiBC,IACrB,MAAMvnC,EAASF,IACf,IAAIlC,EAEFA,EADE2pC,EACS,IAAIC,IAAID,GAERvnC,EAAOpC,SAEpB,MAAM6pC,EAAY7pC,EAASM,SACxB6D,MAAM,GACNhB,MAAM,KACN4B,QAAQ+kC,GAAkB,KAATA,IACd/M,EAAQ8M,EAAUlrC,OAGxB,MAAO,CAAED,IAFGmrC,EAAU9M,EAAQ,GAEhB5R,MADA0e,EAAU9M,EAAQ,GACX,EAEjBgN,EAAa,CAACrrC,EAAKwP,KACvB,MAAM9L,EAASF,IACf,IAAKsX,IAAgB1T,EAAOQ,OAAOvF,QAAQkP,QAAS,OACpD,IAAIjQ,EAEFA,EADE8F,EAAOQ,OAAO8hB,IACL,IAAIwhB,IAAI9jC,EAAOQ,OAAO8hB,KAEtBhmB,EAAOpC,SAEpB,MAAMgS,EAAQlM,EAAOqK,OAAOjC,GAC5B,IAAIid,EAAQqe,EAAQx3B,EAAMqD,aAAa,iBACvC,GAAIvP,EAAOQ,OAAOvF,QAAQsoC,KAAK1qC,OAAS,EAAG,CACzC,IAAI0qC,EAAOvjC,EAAOQ,OAAOvF,QAAQsoC,KACH,MAA1BA,EAAKA,EAAK1qC,OAAS,KAAY0qC,EAAOA,EAAKllC,MAAM,EAAGklC,EAAK1qC,OAAS,IACtEwsB,EAAS,GAAEke,KAAQ3qC,EAAO,GAAEA,KAAS,KAAKysB,G,MAChCnrB,EAASM,SAAS+L,SAAS3N,KACrCysB,EAAS,GAAEzsB,EAAO,GAAEA,KAAS,KAAKysB,KAEhCrlB,EAAOQ,OAAOvF,QAAQuoC,YACxBne,GAASnrB,EAASQ,QAEpB,MAAMwpC,EAAe5nC,EAAOrB,QAAQkpC,MAChCD,GAAgBA,EAAa7e,QAAUA,IAGvCrlB,EAAOQ,OAAOvF,QAAQC,aACxBoB,EAAOrB,QAAQC,aAAa,CAAEmqB,SAAS,KAAMA,GAE7C/oB,EAAOrB,QAAQE,UAAU,CAAEkqB,SAAS,KAAMA,GAC5C,EAGI+e,EAAgB,CAAC3jC,EAAO4kB,EAAO1Q,KACnC,GAAI0Q,EACF,IAAK,IAAIvmB,EAAI,EAAGjG,EAASmH,EAAOqK,OAAOxR,OAAQiG,EAAIjG,EAAQiG,GAAK,EAAG,CACjE,MAAMoN,EAAQlM,EAAOqK,OAAOvL,GAE5B,GADqB4kC,EAAQx3B,EAAMqD,aAAa,mBAC3B8V,EAAO,CAC1B,MAAMjd,EAAQpI,EAAOuR,cAAcrF,GACnClM,EAAOuV,QAAQnN,EAAO3H,EAAOkU,EAC/B,CACF,MAEA3U,EAAOuV,QAAQ,EAAG9U,EAAOkU,EAC3B,EAGI0vB,EAAqB,KACzBZ,EAAQG,EAAc5jC,EAAOQ,OAAO8hB,KACpC8hB,EAAcpkC,EAAOQ,OAAOC,MAAOgjC,EAAMpe,OAAO,EAAM,EA+BxDre,EAAG,QAAQ,KACLhH,EAAOQ,OAAOvF,QAAQkP,SA7Bf,MACX,MAAM7N,EAASF,IACf,GAAK4D,EAAOQ,OAAOvF,QAAnB,CACA,IAAKqB,EAAOrB,UAAYqB,EAAOrB,QAAQE,UAGrC,OAFA6E,EAAOQ,OAAOvF,QAAQkP,SAAU,OAChCnK,EAAOQ,OAAO8jC,eAAen6B,SAAU,GAGzCuJ,GAAc,EACd+vB,EAAQG,EAAc5jC,EAAOQ,OAAO8hB,KAC/BmhB,EAAM7qC,KAAQ6qC,EAAMpe,OAMzB+e,EAAc,EAAGX,EAAMpe,MAAOrlB,EAAOQ,OAAOmT,oBACvC3T,EAAOQ,OAAOvF,QAAQC,cACzBoB,EAAOtD,iBAAiB,WAAYqrC,IAP/BrkC,EAAOQ,OAAOvF,QAAQC,cACzBoB,EAAOtD,iBAAiB,WAAYqrC,EAVZ,CAiB5B,EAWEliB,EACF,IAEFnb,EAAG,WAAW,KACRhH,EAAOQ,OAAOvF,QAAQkP,SAbZ,MACd,MAAM7N,EAASF,IACV4D,EAAOQ,OAAOvF,QAAQC,cACzBoB,EAAOrD,oBAAoB,WAAYorC,EACzC,EAUE3b,EACF,IAEF1hB,EAAG,4CAA4C,KACzC0M,GACFuwB,EAAWjkC,EAAOQ,OAAOvF,QAAQrC,IAAKoH,EAAOyP,YAC/C,IAEFzI,EAAG,eAAe,KACZ0M,GAAe1T,EAAOQ,OAAOiL,SAC/Bw4B,EAAWjkC,EAAOQ,OAAOvF,QAAQrC,IAAKoH,EAAOyP,YAC/C,GAEJ,EC5Ie,SAA4D1P,GAAA,IAApCC,OAAEA,EAAM4mB,aAAEA,EAAYre,KAAEA,EAAIvB,GAAEA,GAAIjH,EACnE2T,GAAc,EAClB,MAAM7Y,EAAWF,IACX2B,EAASF,IACfwqB,EAAa,CACX0d,eAAgB,CACdn6B,SAAS,EACTjP,cAAc,EACdqpC,YAAY,KAGhB,MAAMC,EAAe,KACnBj8B,EAAK,cACL,MAAMk8B,EAAU5pC,EAASX,SAASC,KAAKqD,QAAQ,IAAK,IAEpD,GAAIinC,IADoBzkC,EAAOqK,OAAOrK,EAAOyP,aAAaF,aAAa,aACtC,CAC/B,MAAM+G,EAAWtW,EAAOuR,cACtBxP,EACE/B,EAAO4J,SACN,IAAG5J,EAAOQ,OAAO8J,yBAAyBm6B,gCAAsCA,OACjF,IAEJ,QAAwB,IAAbnuB,EAA0B,OACrCtW,EAAOuV,QAAQe,EACjB,GAEIouB,EAAU,KACd,GAAKhxB,GAAgB1T,EAAOQ,OAAO8jC,eAAen6B,QAClD,GACEnK,EAAOQ,OAAO8jC,eAAeppC,cAC7BoB,EAAOrB,SACPqB,EAAOrB,QAAQC,aAEfoB,EAAOrB,QAAQC,aACb,KACA,KACC,IAAG8E,EAAOqK,OAAOrK,EAAOyP,aAAaF,aAAa,gBAAkB,IAEvEhH,EAAK,eACA,CACL,MAAM2D,EAAQlM,EAAOqK,OAAOrK,EAAOyP,aAC7BtV,EAAO+R,EAAMqD,aAAa,cAAgBrD,EAAMqD,aAAa,gBACnE1U,EAASX,SAASC,KAAOA,GAAQ,GACjCoO,EAAK,UACP,GA+BFvB,EAAG,QAAQ,KACLhH,EAAOQ,OAAO8jC,eAAen6B,SA9BtB,MACX,IACGnK,EAAOQ,OAAO8jC,eAAen6B,SAC7BnK,EAAOQ,OAAOvF,SAAW+E,EAAOQ,OAAOvF,QAAQkP,QAEhD,OACFuJ,GAAc,EACd,MAAMvZ,EAAOU,EAASX,SAASC,KAAKqD,QAAQ,IAAK,IACjD,GAAIrD,EAAM,CACR,MAAMsG,EAAQ,EACd,IAAK,IAAI3B,EAAI,EAAGjG,EAASmH,EAAOqK,OAAOxR,OAAQiG,EAAIjG,EAAQiG,GAAK,EAAG,CACjE,MAAMoN,EAAQlM,EAAOqK,OAAOvL,GAE5B,IADkBoN,EAAMqD,aAAa,cAAgBrD,EAAMqD,aAAa,mBACtDpV,EAAM,CACtB,MAAMiO,EAAQpI,EAAOuR,cAAcrF,GACnClM,EAAOuV,QAAQnN,EAAO3H,EAAOT,EAAOQ,OAAOmT,oBAAoB,EACjE,CACF,CACF,CACI3T,EAAOQ,OAAO8jC,eAAeC,YAC/BjoC,EAAOtD,iBAAiB,aAAcwrC,EACxC,EAUEriB,EACF,IAEFnb,EAAG,WAAW,KACRhH,EAAOQ,OAAO8jC,eAAen6B,SAX7BnK,EAAOQ,OAAO8jC,eAAeC,YAC/BjoC,EAAOrD,oBAAoB,aAAcurC,EAY3C,IAEFx9B,EAAG,4CAA4C,KACzC0M,GACFgxB,GACF,IAEF19B,EAAG,eAAe,KACZ0M,GAAe1T,EAAOQ,OAAOiL,SAC/Bi5B,GACF,GAEJ,EC9Fe,SAA8D3kC,GAAA,IAkBvEygB,EACAmkB,GAnB2B3kC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,EAAEuB,KAAEA,EAAI/H,OAAEA,GAAQT,EACzEC,EAAO4gB,SAAW,CAChBC,SAAS,EACTC,QAAQ,EACR8jB,SAAU,GAGZhe,EAAa,CACXhG,SAAU,CACRzW,SAAS,EACT3N,MAAO,IACPqoC,mBAAmB,EACnBC,sBAAsB,EACtBC,iBAAiB,EACjBC,kBAAkB,EAClBC,mBAAmB,KAKvB,IAEIC,EAEAC,EACAlrB,EACAmrB,EACAC,EACAC,EACAC,EATAC,EAAqBhlC,GAAUA,EAAOogB,SAAWpgB,EAAOogB,SAASpkB,MAAQ,IACzEipC,EAAuBjlC,GAAUA,EAAOogB,SAAWpgB,EAAOogB,SAASpkB,MAAQ,IAE3EkpC,GAAoB,IAAI/pC,MAAOsF,QAQnC,SAASi8B,EAAgBl5B,GAClBhE,IAAUA,EAAOsH,WAActH,EAAOU,WACvCsD,EAAExL,SAAWwH,EAAOU,YACxBV,EAAOU,UAAUzH,oBAAoB,gBAAiBikC,GACtDnc,IACF,CAEA,MAAM4kB,EAAe,KACnB,GAAI3lC,EAAOsH,YAActH,EAAO4gB,SAASC,QAAS,OAC9C7gB,EAAO4gB,SAASE,OAClBqkB,GAAY,EACHA,IACTM,EAAuBP,EACvBC,GAAY,GAEd,MAAMP,EAAW5kC,EAAO4gB,SAASE,OAC7BokB,EACAQ,EAAoBD,GAAuB,IAAI9pC,MAAOsF,UAC1DjB,EAAO4gB,SAASgkB,SAAWA,EAC3Br8B,EAAK,mBAAoBq8B,EAAUA,EAAWY,GAC9Cb,EAAM3oC,uBAAsB,KAC1B2pC,GAAc,GACd,EAiBEC,EAAOC,IACX,GAAI7lC,EAAOsH,YAActH,EAAO4gB,SAASC,QAAS,OAClD3kB,qBAAqByoC,GACrBgB,IAEA,IAAInpC,OAA8B,IAAfqpC,EAA6B7lC,EAAOQ,OAAOogB,SAASpkB,MAAQqpC,EAC/EL,EAAqBxlC,EAAOQ,OAAOogB,SAASpkB,MAC5CipC,EAAuBzlC,EAAOQ,OAAOogB,SAASpkB,MAC9C,MAAMspC,EAtBc,MACpB,IAAIC,EAQJ,GANEA,EADE/lC,EAAOkK,SAAWlK,EAAOQ,OAAO0J,QAAQC,QAC1BnK,EAAOqK,OAAOpL,QAAQ4C,GACpCA,EAAQQ,UAAU0M,SAAS,yBAC3B,GAEc/O,EAAOqK,OAAOrK,EAAOyP,cAElCs2B,EAAe,OAEpB,OAD0B58B,SAAS48B,EAAcx2B,aAAa,wBAAyB,GAC/D,EAWEy2B,IAEvBr/B,OAAOyC,MAAM08B,IACdA,EAAoB,QACE,IAAfD,IAEPrpC,EAAQspC,EACRN,EAAqBM,EACrBL,EAAuBK,GAEzBZ,EAAmB1oC,EACnB,MAAMiE,EAAQT,EAAOQ,OAAOC,MACtBwlC,EAAU,KACTjmC,IAAUA,EAAOsH,YAClBtH,EAAOQ,OAAOogB,SAASokB,kBACpBhlC,EAAO+Q,aAAe/Q,EAAOQ,OAAO6M,MAAQrN,EAAOQ,OAAOuW,QAC7D/W,EAAOgX,UAAUvW,GAAO,GAAM,GAC9B8H,EAAK,aACKvI,EAAOQ,OAAOogB,SAASmkB,kBACjC/kC,EAAOuV,QAAQvV,EAAOqK,OAAOxR,OAAS,EAAG4H,GAAO,GAAM,GACtD8H,EAAK,cAGFvI,EAAOgR,OAAShR,EAAOQ,OAAO6M,MAAQrN,EAAOQ,OAAOuW,QACvD/W,EAAOuW,UAAU9V,GAAO,GAAM,GAC9B8H,EAAK,aACKvI,EAAOQ,OAAOogB,SAASmkB,kBACjC/kC,EAAOuV,QAAQ,EAAG9U,GAAO,GAAM,GAC/B8H,EAAK,aAGLvI,EAAOQ,OAAOiL,UAChBi6B,GAAoB,IAAI/pC,MAAOsF,UAC/BjF,uBAAsB,KACpB4pC,GAAK,KAET,EAcF,OAZIppC,EAAQ,GACVV,aAAa0kB,GACbA,EAAU3kB,YAAW,KACnBoqC,GAAS,GACRzpC,IAEHR,uBAAsB,KACpBiqC,GAAS,IAKNzpC,CAAK,EAGR0pC,EAAQ,KACZlmC,EAAO4gB,SAASC,SAAU,EAC1B+kB,IACAr9B,EAAK,gBAAgB,EAGjBsrB,EAAO,KACX7zB,EAAO4gB,SAASC,SAAU,EAC1B/kB,aAAa0kB,GACbtkB,qBAAqByoC,GACrBp8B,EAAK,eAAe,EAEhB49B,EAAQ,CAACtxB,EAAUuxB,KACvB,GAAIpmC,EAAOsH,YAActH,EAAO4gB,SAASC,QAAS,OAClD/kB,aAAa0kB,GACR3L,IACH0wB,GAAsB,GAGxB,MAAMU,EAAU,KACd19B,EAAK,iBACDvI,EAAOQ,OAAOogB,SAASikB,kBACzB7kC,EAAOU,UAAU1H,iBAAiB,gBAAiBkkC,GAEnDnc,GACF,EAIF,GADA/gB,EAAO4gB,SAASE,QAAS,EACrBslB,EAMF,OALId,IACFJ,EAAmBllC,EAAOQ,OAAOogB,SAASpkB,OAE5C8oC,GAAe,OACfW,IAGF,MAAMzpC,EAAQ0oC,GAAoBllC,EAAOQ,OAAOogB,SAASpkB,MACzD0oC,EAAmB1oC,IAAS,IAAIb,MAAOsF,UAAYykC,GAC/C1lC,EAAOgR,OAASk0B,EAAmB,IAAMllC,EAAOQ,OAAO6M,OACvD63B,EAAmB,IAAGA,EAAmB,GAC7Ce,IAAS,EAGLllB,EAAS,KAEV/gB,EAAOgR,OAASk0B,EAAmB,IAAMllC,EAAOQ,OAAO6M,MACxDrN,EAAOsH,YACNtH,EAAO4gB,SAASC,UAGnB6kB,GAAoB,IAAI/pC,MAAOsF,UAC3BskC,GACFA,GAAsB,EACtBK,EAAIV,IAEJU,IAEF5lC,EAAO4gB,SAASE,QAAS,EACzBvY,EAAK,kBAAiB,EAGlB89B,EAAqB,KACzB,GAAIrmC,EAAOsH,YAActH,EAAO4gB,SAASC,QAAS,OAClD,MAAMhmB,EAAWF,IACgB,WAA7BE,EAASyrC,kBACXf,GAAsB,EACtBY,GAAM,IAEyB,YAA7BtrC,EAASyrC,iBACXvlB,GACF,EAGIwlB,EAAkBviC,IACA,UAAlBA,EAAE2V,cACN4rB,GAAsB,EACtBY,GAAM,GAAK,EAGPK,EAAkBxiC,IACA,UAAlBA,EAAE2V,aACF3Z,EAAO4gB,SAASE,QAClBC,GACF,EAyBF/Z,EAAG,QAAQ,KACLhH,EAAOQ,OAAOogB,SAASzW,UAtBvBnK,EAAOQ,OAAOogB,SAASqkB,oBACzBjlC,EAAOrD,GAAG3D,iBAAiB,eAAgButC,GAC3CvmC,EAAOrD,GAAG3D,iBAAiB,eAAgBwtC,IAU5B7rC,IACR3B,iBAAiB,mBAAoBqtC,GAY5CX,GAAoB,IAAI/pC,MAAOsF,UAC/BilC,IACF,IAGFl/B,EAAG,WAAW,KAvBZhH,EAAOrD,GAAG1D,oBAAoB,eAAgBstC,GAC9CvmC,EAAOrD,GAAG1D,oBAAoB,eAAgButC,GAS7B7rC,IACR1B,oBAAoB,mBAAoBotC,GAe7CrmC,EAAO4gB,SAASC,SAClBgT,GACF,IAGF7sB,EAAG,yBAAyB,CAAC8jB,EAAIrqB,EAAOoU,MAClC7U,EAAOsH,WAActH,EAAO4gB,SAASC,UACrChM,IAAa7U,EAAOQ,OAAOogB,SAASkkB,qBACtCqB,GAAM,GAAM,GAEZtS,IACF,IAGF7sB,EAAG,mBAAmB,MAChBhH,EAAOsH,WAActH,EAAO4gB,SAASC,UAErC7gB,EAAOQ,OAAOogB,SAASkkB,qBACzBjR,KAGF5Z,GAAY,EACZmrB,GAAgB,EAChBG,GAAsB,EACtBF,EAAoBxpC,YAAW,KAC7B0pC,GAAsB,EACtBH,GAAgB,EAChBe,GAAM,EAAK,GACV,MAAI,IAGTn/B,EAAG,YAAY,KACb,IAAIhH,EAAOsH,WAActH,EAAO4gB,SAASC,SAAY5G,EAArD,CAIA,GAHAne,aAAaupC,GACbvpC,aAAa0kB,GAETxgB,EAAOQ,OAAOogB,SAASkkB,qBAGzB,OAFAM,GAAgB,OAChBnrB,GAAY,GAIVmrB,GAAiBplC,EAAOQ,OAAOiL,SAASsV,IAC5CqkB,GAAgB,EAChBnrB,GAAY,CAZoD,CAY/C,IAGnBjT,EAAG,eAAe,MACZhH,EAAOsH,WAActH,EAAO4gB,SAASC,UACzCykB,GAAe,EAAI,IAGrBhtC,OAAO+Q,OAAOrJ,EAAO4gB,SAAU,CAC7BslB,QACArS,OACAsS,QACAplB,UAEJ,ECvTe,SAA6ChhB,GAAA,IAA9BC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,GAAIjH,EACxD6mB,EAAa,CACX6f,OAAQ,CACNzmC,OAAQ,KACR0mC,sBAAsB,EACtBC,iBAAkB,EAClBC,sBAAuB,4BACvBC,qBAAsB,mBAI1B,IAAInzB,GAAc,EACdozB,GAAgB,EAMpB,SAASC,IACP,MAAMC,EAAehnC,EAAOymC,OAAOzmC,OACnC,IAAKgnC,GAAgBA,EAAa1/B,UAAW,OAE7C,MAAM0M,EAAegzB,EAAahzB,aAC5BD,EAAeizB,EAAajzB,aAClC,GAAIA,GAAgBA,EAAa1R,UAAU0M,SAAS/O,EAAOQ,OAAOimC,OAAOG,uBACvE,OACF,GAAI,MAAO5yB,EAAuD,OAClE,IAAI4D,EAEFA,EADEovB,EAAaxmC,OAAO6M,KACPlE,SACb69B,EAAajzB,aAAaxE,aAAa,2BACvC,IAGayE,EAEbhU,EAAOQ,OAAO6M,KAChBrN,EAAOqW,YAAYuB,GAEnB5X,EAAOuV,QAAQqC,EAEnB,CAEA,SAASuK,IACP,MAAQskB,OAAQQ,GAAiBjnC,EAAOQ,OACxC,GAAIkT,EAAa,OAAO,EACxBA,GAAc,EACd,MAAMwzB,EAAclnC,EAAO3H,YAC3B,GAAI4uC,EAAajnC,kBAAkBknC,EACjClnC,EAAOymC,OAAOzmC,OAASinC,EAAajnC,OACpC1H,OAAO+Q,OAAOrJ,EAAOymC,OAAOzmC,OAAOgkB,eAAgB,CACjDtV,qBAAqB,EACrBuF,qBAAqB,IAEvB3b,OAAO+Q,OAAOrJ,EAAOymC,OAAOzmC,OAAOQ,OAAQ,CACzCkO,qBAAqB,EACrBuF,qBAAqB,IAEvBjU,EAAOymC,OAAOzmC,OAAO6I,cAChB,GAAI1Q,EAAS8uC,EAAajnC,QAAS,CACxC,MAAMmnC,EAAqB7uC,OAAO+Q,OAAO,GAAI49B,EAAajnC,QAC1D1H,OAAO+Q,OAAO89B,EAAoB,CAChCz4B,qBAAqB,EACrBuF,qBAAqB,IAEvBjU,EAAOymC,OAAOzmC,OAAS,IAAIknC,EAAYC,GACvCL,GAAgB,CAClB,CAGA,OAFA9mC,EAAOymC,OAAOzmC,OAAOrD,GAAG0F,UAAUC,IAAItC,EAAOQ,OAAOimC,OAAOI,sBAC3D7mC,EAAOymC,OAAOzmC,OAAOgH,GAAG,MAAO+/B,IACxB,CACT,CAEA,SAASl+B,EAAO2M,GACd,MAAMwxB,EAAehnC,EAAOymC,OAAOzmC,OACnC,IAAKgnC,GAAgBA,EAAa1/B,UAAW,OAE7C,MAAM0E,EACkC,SAAtCg7B,EAAaxmC,OAAOwL,cAChBg7B,EAAatwB,uBACbswB,EAAaxmC,OAAOwL,cAG1B,IAAIo7B,EAAmB,EACvB,MAAMC,EAAmBrnC,EAAOQ,OAAOimC,OAAOG,sBAa9C,GAXI5mC,EAAOQ,OAAOwL,cAAgB,IAAMhM,EAAOQ,OAAOgL,iBACpD47B,EAAmBpnC,EAAOQ,OAAOwL,eAG9BhM,EAAOQ,OAAOimC,OAAOC,uBACxBU,EAAmB,GAGrBA,EAAmBjmC,KAAKwL,MAAMy6B,GAE9BJ,EAAa38B,OAAO1R,SAASkJ,GAAYA,EAAQQ,UAAU4M,OAAOo4B,KAEhEL,EAAaxmC,OAAO6M,MACnB25B,EAAaxmC,OAAO0J,SAAW88B,EAAaxmC,OAAO0J,QAAQC,QAE5D,IAAK,IAAIrL,EAAI,EAAGA,EAAIsoC,EAAkBtoC,GAAK,EACzCiD,EACEilC,EAAap9B,SACZ,6BAA4B5J,EAAOkT,UAAYpU,OAChDnG,SAASkJ,IACTA,EAAQQ,UAAUC,IAAI+kC,EAAiB,SAI3C,IAAK,IAAIvoC,EAAI,EAAGA,EAAIsoC,EAAkBtoC,GAAK,EACrCkoC,EAAa38B,OAAOrK,EAAOkT,UAAYpU,IACzCkoC,EAAa38B,OAAOrK,EAAOkT,UAAYpU,GAAGuD,UAAUC,IAAI+kC,GAK9D,MAAMV,EAAmB3mC,EAAOQ,OAAOimC,OAAOE,iBACxCW,EAAYX,IAAqBK,EAAaxmC,OAAO6M,KAC3D,GAAIrN,EAAOkT,YAAc8zB,EAAa9zB,WAAao0B,EAAW,CAC5D,MAAMC,EAAqBP,EAAav3B,YACxC,IAAI+3B,EACAnyB,EACJ,GAAI2xB,EAAaxmC,OAAO6M,KAAM,CAC5B,MAAMo6B,EAAiBT,EAAa38B,OAAOpL,QACxC4C,GAAYA,EAAQ0N,aAAa,6BAAgC,GAAEvP,EAAOkT,cAC3E,GACFs0B,EAAiBR,EAAa38B,OAAOnL,QAAQuoC,GAE7CpyB,EAAYrV,EAAOyP,YAAczP,EAAOiT,cAAgB,OAAS,MACnE,MACEu0B,EAAiBxnC,EAAOkT,UACxBmC,EAAYmyB,EAAiBxnC,EAAOiT,cAAgB,OAAS,OAE3Dq0B,IACFE,GAAgC,SAAdnyB,EAAuBsxB,GAAoB,EAAIA,GAIjEK,EAAa72B,sBACb62B,EAAa72B,qBAAqBjR,QAAQsoC,GAAkB,IAExDR,EAAaxmC,OAAOgL,eAEpBg8B,EADEA,EAAiBD,EACFC,EAAiBrmC,KAAKwL,MAAMX,EAAgB,GAAK,EAEjDw7B,EAAiBrmC,KAAKwL,MAAMX,EAAgB,GAAK,EAGpEw7B,EAAiBD,GACjBP,EAAaxmC,OAAOsM,eAItBk6B,EAAazxB,QAAQiyB,EAAgBhyB,EAAU,OAAI5W,GAEvD,CACF,CA/IAoB,EAAOymC,OAAS,CACdzmC,OAAQ,MAgJVgH,EAAG,cAAc,KACf,MAAMy/B,OAAEA,GAAWzmC,EAAOQ,OAC1B,GAAKimC,GAAWA,EAAOzmC,OACvB,GAA6B,iBAAlBymC,EAAOzmC,QAAuBymC,EAAOzmC,kBAAkBxB,YAAa,CAC7E,MAAM3D,EAAWF,IACX+sC,EAA0B,KAC9B,MAAMC,EACqB,iBAAlBlB,EAAOzmC,OAAsBnF,EAASxB,cAAcotC,EAAOzmC,QAAUymC,EAAOzmC,OACrF,GAAI2nC,GAAiBA,EAAc3nC,OACjCymC,EAAOzmC,OAAS2nC,EAAc3nC,OAC9BmiB,IACAtZ,GAAO,QACF,GAAI8+B,EAAe,CACxB,MAAMC,EAAkB5jC,IACtByiC,EAAOzmC,OAASgE,EAAEuuB,OAAO,GACzBoV,EAAc1uC,oBAAoB,OAAQ2uC,GAC1CzlB,IACAtZ,GAAO,GACP49B,EAAOzmC,OAAO6I,SACd7I,EAAO6I,QAAQ,EAEjB8+B,EAAc3uC,iBAAiB,OAAQ4uC,EACzC,CACA,OAAOD,CAAa,EAGhBE,EAAyB,KAC7B,GAAI7nC,EAAOsH,UAAW,OACAogC,KAEpB1rC,sBAAsB6rC,EACxB,EAEF7rC,sBAAsB6rC,EACxB,MACE1lB,IACAtZ,GAAO,EACT,IAEF7B,EAAG,4CAA4C,KAC7C6B,GAAQ,IAEV7B,EAAG,iBAAiB,CAAC8jB,EAAIvqB,KACvB,MAAMymC,EAAehnC,EAAOymC,OAAOzmC,OAC9BgnC,IAAgBA,EAAa1/B,WAClC0/B,EAAa33B,cAAc9O,EAAS,IAEtCyG,EAAG,iBAAiB,KAClB,MAAMggC,EAAehnC,EAAOymC,OAAOzmC,OAC9BgnC,IAAgBA,EAAa1/B,WAC9Bw/B,GACFE,EAAate,SACf,IAGFpwB,OAAO+Q,OAAOrJ,EAAOymC,OAAQ,CAC3BtkB,OACAtZ,UAEJ,EC3Ne,SAAwD9I,GAAA,IAAtCC,OAAEA,EAAM4mB,aAAEA,EAAYre,KAAEA,EAAId,KAAEA,GAAM1H,EACnE6mB,EAAa,CACXhK,SAAU,CACRzS,SAAS,EACT29B,UAAU,EACVC,cAAe,EACfC,gBAAgB,EAChBC,oBAAqB,EACrBC,sBAAuB,EACvB3U,QAAQ,EACR4U,gBAAiB,OAiNrB7vC,OAAO+Q,OAAOrJ,EAAQ,CACpB4c,SAAU,CACRrD,aA/MJ,WACE,MAAMnZ,EAAYJ,EAAOtD,eACzBsD,EAAOoU,aAAahU,GACpBJ,EAAOqP,cAAc,GACrBrP,EAAOwZ,gBAAgB0N,WAAWruB,OAAS,EAC3CmH,EAAO4c,SAAS0C,WAAW,CAAEM,WAAY5f,EAAO+J,IAAM/J,EAAOI,WAAaJ,EAAOI,WACnF,EA0MIyc,YAxMJ,WACE,MAAQrD,gBAAiBhR,EAAIsQ,QAAEA,GAAY9Y,EAEZ,IAA3BwI,EAAK0e,WAAWruB,QAClB2P,EAAK0e,WAAWrjB,KAAK,CACnByvB,SAAUxa,EAAQ9Y,EAAOiJ,eAAiB,SAAW,UACrD5I,KAAMmI,EAAK2T,iBAGf3T,EAAK0e,WAAWrjB,KAAK,CACnByvB,SAAUxa,EAAQ9Y,EAAOiJ,eAAiB,WAAa,YACvD5I,KAAM5D,KAEV,EA4LI6iB,WA1LJ,SAAoCgN,GAAA,IAAhB1M,WAAEA,GAAY0M,EAChC,MAAM9rB,OAAEA,EAAME,UAAEA,EAAWoJ,aAAcC,EAAGS,SAAEA,EAAUgP,gBAAiBhR,GAASxI,EAG5Eyf,EADehjB,IACW+L,EAAK2T,eAErC,GAAIyD,GAAc5f,EAAOsQ,eACvBtQ,EAAOuV,QAAQvV,EAAOyP,kBAGxB,GAAImQ,GAAc5f,EAAO8Q,eACnB9Q,EAAOqK,OAAOxR,OAAS2R,EAAS3R,OAClCmH,EAAOuV,QAAQ/K,EAAS3R,OAAS,GAEjCmH,EAAOuV,QAAQvV,EAAOqK,OAAOxR,OAAS,OAJ1C,CASA,GAAI2H,EAAOoc,SAASkrB,SAAU,CAC5B,GAAIt/B,EAAK0e,WAAWruB,OAAS,EAAG,CAC9B,MAAMuvC,EAAgB5/B,EAAK0e,WAAWmhB,MAChCC,EAAgB9/B,EAAK0e,WAAWmhB,MAEhCE,EAAWH,EAAc9U,SAAWgV,EAAchV,SAClDjzB,EAAO+nC,EAAc/nC,KAAOioC,EAAcjoC,KAChDL,EAAOgnB,SAAWuhB,EAAWloC,EAC7BL,EAAOgnB,UAAY,EACf7lB,KAAK0L,IAAI7M,EAAOgnB,UAAYxmB,EAAOoc,SAASurB,kBAC9CnoC,EAAOgnB,SAAW,IAIhB3mB,EAAO,KAAO5D,IAAQ2rC,EAAc/nC,KAAO,OAC7CL,EAAOgnB,SAAW,EAEtB,MACEhnB,EAAOgnB,SAAW,EAEpBhnB,EAAOgnB,UAAYxmB,EAAOoc,SAASsrB,sBAEnC1/B,EAAK0e,WAAWruB,OAAS,EACzB,IAAIkmC,EAAmB,IAAOv+B,EAAOoc,SAASmrB,cAC9C,MAAMS,EAAmBxoC,EAAOgnB,SAAW+X,EAE3C,IAAI0J,EAAczoC,EAAOI,UAAYooC,EACjCz+B,IAAK0+B,GAAeA,GAExB,IACIC,EADAC,GAAW,EAEf,MAAMC,EAA2C,GAA5BznC,KAAK0L,IAAI7M,EAAOgnB,UAAiBxmB,EAAOoc,SAASqrB,oBACtE,IAAIY,EACJ,GAAIJ,EAAczoC,EAAO8Q,eACnBtQ,EAAOoc,SAASorB,gBACdS,EAAczoC,EAAO8Q,gBAAkB83B,IACzCH,EAAczoC,EAAO8Q,eAAiB83B,GAExCF,EAAsB1oC,EAAO8Q,eAC7B63B,GAAW,EACXngC,EAAKsW,qBAAsB,GAE3B2pB,EAAczoC,EAAO8Q,eAEnBtQ,EAAO6M,MAAQ7M,EAAOgL,iBAAgBq9B,GAAe,QACpD,GAAIJ,EAAczoC,EAAOsQ,eAC1B9P,EAAOoc,SAASorB,gBACdS,EAAczoC,EAAOsQ,eAAiBs4B,IACxCH,EAAczoC,EAAOsQ,eAAiBs4B,GAExCF,EAAsB1oC,EAAOsQ,eAC7Bq4B,GAAW,EACXngC,EAAKsW,qBAAsB,GAE3B2pB,EAAczoC,EAAOsQ,eAEnB9P,EAAO6M,MAAQ7M,EAAOgL,iBAAgBq9B,GAAe,QACpD,GAAIroC,EAAOoc,SAAS2W,OAAQ,CACjC,IAAInhB,EACJ,IAAK,IAAI02B,EAAI,EAAGA,EAAIt+B,EAAS3R,OAAQiwC,GAAK,EACxC,GAAIt+B,EAASs+B,IAAML,EAAa,CAC9Br2B,EAAY02B,EACZ,KACF,CAQAL,EAJAtnC,KAAK0L,IAAIrC,EAAS4H,GAAaq2B,GAC7BtnC,KAAK0L,IAAIrC,EAAS4H,EAAY,GAAKq2B,IACX,SAA1BzoC,EAAOoc,eAEO5R,EAAS4H,GAET5H,EAAS4H,EAAY,GAErCq2B,GAAeA,CACjB,CAOA,GANII,GACFphC,EAAK,iBAAiB,KACpBzH,EAAO6W,SAAS,IAII,IAApB7W,EAAOgnB,UAMT,GAJE+X,EADEh1B,EACiB5I,KAAK0L,MAAM47B,EAAczoC,EAAOI,WAAaJ,EAAOgnB,UAEpD7lB,KAAK0L,KAAK47B,EAAczoC,EAAOI,WAAaJ,EAAOgnB,UAEpExmB,EAAOoc,SAAS2W,OAAQ,CAQ1B,MAAMwV,EAAe5nC,KAAK0L,KAAK9C,GAAO0+B,EAAcA,GAAezoC,EAAOI,WACpE4oC,EAAmBhpC,EAAO0K,gBAAgB1K,EAAOyP,aAErDsvB,EADEgK,EAAeC,EACExoC,EAAOC,MACjBsoC,EAAe,EAAIC,EACM,IAAfxoC,EAAOC,MAEQ,IAAfD,EAAOC,KAE9B,OACK,GAAID,EAAOoc,SAAS2W,OAEzB,YADAvzB,EAAOyX,iBAILjX,EAAOoc,SAASorB,gBAAkBW,GACpC3oC,EAAO2Q,eAAe+3B,GACtB1oC,EAAOqP,cAAc0vB,GACrB/+B,EAAOoU,aAAaq0B,GACpBzoC,EAAO8V,iBAAgB,EAAM9V,EAAOoc,gBACpCpc,EAAO8U,WAAY,EACnBhR,EAAqBpD,GAAW,KACzBV,IAAUA,EAAOsH,WAAckB,EAAKsW,sBACzCvW,EAAK,kBACLvI,EAAOqP,cAAc7O,EAAOC,OAC5B5E,YAAW,KACTmE,EAAOoU,aAAas0B,GACpB5kC,EAAqBpD,GAAW,KACzBV,IAAUA,EAAOsH,WACtBtH,EAAO+V,eAAe,GACtB,GACD,GAAE,KAEE/V,EAAOgnB,UAChBze,EAAK,8BACLvI,EAAO2Q,eAAe83B,GACtBzoC,EAAOqP,cAAc0vB,GACrB/+B,EAAOoU,aAAaq0B,GACpBzoC,EAAO8V,iBAAgB,EAAM9V,EAAOoc,gBAC/Bpc,EAAO8U,YACV9U,EAAO8U,WAAY,EACnBhR,EAAqBpD,GAAW,KACzBV,IAAUA,EAAOsH,WACtBtH,EAAO+V,eAAe,MAI1B/V,EAAO2Q,eAAe83B,GAGxBzoC,EAAO+S,oBACP/S,EAAO8R,qBACT,KAAO,IAAItR,EAAOoc,SAAS2W,OAEzB,YADAvzB,EAAOyX,iBAEEjX,EAAOoc,UAChBrU,EAAK,6BACP,GAEK/H,EAAOoc,SAASkrB,UAAYroB,GAAYjf,EAAOyf,gBAClDjgB,EAAO2Q,iBACP3Q,EAAO+S,oBACP/S,EAAO8R,sBAjKT,CAmKF,IASF,ECpOe,SAAwC/R,GAAA,IAQjDkpC,EACAC,EACAC,GAVuBnpC,OAAEA,EAAM4mB,aAAEA,GAAc7mB,EACnD6mB,EAAa,CACXjb,KAAM,CACJC,KAAM,EACNyY,KAAM,YAiFVrkB,EAAO2L,KAAO,CACZG,WA1EkBvB,IAClB,MAAMyB,cAAEA,GAAkBhM,EAAOQ,QAC3BoL,KAAEA,EAAIyY,KAAEA,GAASrkB,EAAOQ,OAAOmL,KACrCu9B,EAAeD,EAAyBr9B,EACxCu9B,EAAiBhoC,KAAKwL,MAAMpC,EAAeqB,GAEzCq9B,EADE9nC,KAAKwL,MAAMpC,EAAeqB,KAAUrB,EAAeqB,EAC5BrB,EAEApJ,KAAKoM,KAAKhD,EAAeqB,GAAQA,EAEtC,SAAlBI,GAAqC,QAATqY,IAC9B4kB,EAAyB9nC,KAAKC,IAAI6nC,EAAwBj9B,EAAgBJ,GAC5E,EA+DAO,YA5DkB,CAACrN,EAAGoN,EAAO3B,EAAchB,KAC3C,MAAMuD,eAAEA,EAAc7B,aAAEA,GAAiBjL,EAAOQ,QAC1CoL,KAAEA,EAAIyY,KAAEA,GAASrkB,EAAOQ,OAAOmL,KAErC,IAAIy9B,EACAC,EACAC,EACJ,GAAa,QAATjlB,GAAkBvX,EAAiB,EAAG,CACxC,MAAMy8B,EAAapoC,KAAKwL,MAAM7N,GAAKgO,EAAiBlB,IAC9C49B,EAAoB1qC,EAAI8M,EAAOkB,EAAiBy8B,EAChDE,EACW,IAAfF,EACIz8B,EACA3L,KAAKE,IACHF,KAAKoM,MAAMhD,EAAeg/B,EAAa39B,EAAOkB,GAAkBlB,GAChEkB,GAERw8B,EAAMnoC,KAAKwL,MAAM68B,EAAoBC,GACrCJ,EAASG,EAAoBF,EAAMG,EAAiBF,EAAaz8B,EAEjEs8B,EAAqBC,EAAUC,EAAML,EAA0Br9B,EAC/DM,EAAMrS,MAAM6vC,MAAQN,CACtB,KAAoB,WAAT/kB,GACTglB,EAASloC,KAAKwL,MAAM7N,EAAI8M,GACxB09B,EAAMxqC,EAAIuqC,EAASz9B,GACfy9B,EAASF,GAAmBE,IAAWF,GAAkBG,IAAQ19B,EAAO,KAC1E09B,GAAO,EACHA,GAAO19B,IACT09B,EAAM,EACND,GAAU,MAIdC,EAAMnoC,KAAKwL,MAAM7N,EAAIoqC,GACrBG,EAASvqC,EAAIwqC,EAAMJ,GAErBh9B,EAAMrS,MAAM0P,EAAkB,eACpB,IAAR+/B,EAAYr+B,GAAiB,GAAEA,MAAmB,EAAE,EAwBtDiC,kBArBwB,CAACrB,EAAWrB,EAAUjB,KAC9C,MAAM0B,aAAEA,EAAYO,eAAEA,EAAce,aAAEA,GAAiBvM,EAAOQ,QACxDoL,KAAEA,GAAS5L,EAAOQ,OAAOmL,KAI/B,GAHA3L,EAAOoL,aAAeS,EAAYZ,GAAgBg+B,EAClDjpC,EAAOoL,YAAcjK,KAAKoM,KAAKvN,EAAOoL,YAAcQ,GAAQX,EAC5DjL,EAAOU,UAAU7G,MAAM0P,EAAkB,UAAa,GAAEvJ,EAAOoL,YAAcH,MACzEO,EAAgB,CAClB,MAAM2B,EAAgB,GACtB,IAAK,IAAIrO,EAAI,EAAGA,EAAI0L,EAAS3R,OAAQiG,GAAK,EAAG,CAC3C,IAAIsO,EAAiB5C,EAAS1L,GAC1ByN,IAAca,EAAiBjM,KAAKwL,MAAMS,IAC1C5C,EAAS1L,GAAKkB,EAAOoL,YAAcZ,EAAS,IAAI2C,EAActJ,KAAKuJ,EACzE,CACA5C,EAASnC,OAAO,EAAGmC,EAAS3R,QAC5B2R,EAAS3G,QAAQsJ,EACnB,GAQJ,ECpFe,SAAkCpN,GAAA,IAAZC,OAAEA,GAAQD,EAC7CzH,OAAO+Q,OAAOrJ,EAAQ,CACpBupB,YAAaA,GAAY7F,KAAK1jB,GAC9B4pB,aAAcA,GAAalG,KAAK1jB,GAChC8pB,SAAUA,GAASpG,KAAK1jB,GACxBmqB,YAAaA,GAAYzG,KAAK1jB,GAC9BsqB,gBAAiBA,GAAgB5G,KAAK1jB,IAE1C,ECTe,SAAkDD,GAAA,IAA9BC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,GAAIjH,EAC7D6mB,EAAa,CACX+iB,WAAY,CACVC,WAAW,KAmCfrf,GAAW,CACTvd,OAAQ,OACRhN,SACAgH,KACAoN,aAnCmB,KACnB,MAAM/J,OAAEA,GAAWrK,EACJA,EAAOQ,OAAOmpC,WAC7B,IAAK,IAAI7qC,EAAI,EAAGA,EAAIuL,EAAOxR,OAAQiG,GAAK,EAAG,CACzC,MAAM+C,EAAU7B,EAAOqK,OAAOvL,GAE9B,IAAI+qC,GADWhoC,EAAQkO,kBAElB/P,EAAOQ,OAAO0T,mBAAkB21B,GAAM7pC,EAAOI,WAClD,IAAI0pC,EAAK,EACJ9pC,EAAOiJ,iBACV6gC,EAAKD,EACLA,EAAK,GAEP,MAAME,EAAe/pC,EAAOQ,OAAOmpC,WAAWC,UAC1CzoC,KAAKC,IAAI,EAAID,KAAK0L,IAAIhL,EAAQX,UAAW,GACzC,EAAIC,KAAKE,IAAIF,KAAKC,IAAIS,EAAQX,UAAW,GAAI,GAE3C2Y,EAAWmR,GAAaxqB,EAAQqB,GACtCgY,EAAShgB,MAAMo/B,QAAU8Q,EACzBlwB,EAAShgB,MAAMsD,UAAa,eAAc0sC,QAASC,WACrD,GAgBAz6B,cAdqB9O,IACrB,MAAM8qB,EAAoBrrB,EAAOqK,OAAO/M,KAAKuE,GAAYD,EAAoBC,KAC7EwpB,EAAkB1yB,SAASgE,IACzBA,EAAG9C,MAAMspB,mBAAsB,GAAE5iB,KAAY,IAG/C6qB,GAA2B,CAAEprB,SAAQO,WAAU8qB,oBAAmBC,WAAW,GAAO,EASpFd,gBAAiB,MACfxe,cAAe,EACfc,eAAgB,EAChB4B,qBAAqB,EACrBzD,aAAc,EACdiJ,kBAAmBlU,EAAOQ,OAAOiL,WAGvC,ECtDe,SAAkD1L,GAAA,IAA9BC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,GAAIjH,EAC7D6mB,EAAa,CACXojB,WAAY,CACVjf,cAAc,EACdkf,QAAQ,EACRC,aAAc,GACdC,YAAa,OAIjB,MAAMC,EAAqB,CAACvoC,EAASX,EAAU+H,KAC7C,IAAIohC,EAAephC,EACfpH,EAAQxI,cAAc,6BACtBwI,EAAQxI,cAAc,4BACtBixC,EAAcrhC,EACdpH,EAAQxI,cAAc,8BACtBwI,EAAQxI,cAAc,+BACrBgxC,IACHA,EAAe3wC,EAAc,MAAQ,wBAAsBuP,EAAe,OAAS,QACnFpH,EAAQ6W,OAAO2xB,IAEZC,IACHA,EAAc5wC,EACZ,MACC,wBAAsBuP,EAAe,QAAU,WAElDpH,EAAQ6W,OAAO4xB,IAEbD,IAAcA,EAAaxwC,MAAMo/B,QAAU93B,KAAKC,KAAKF,EAAU,IAC/DopC,IAAaA,EAAYzwC,MAAMo/B,QAAU93B,KAAKC,IAAIF,EAAU,GAAE,EAgJpEqpB,GAAW,CACTvd,OAAQ,OACRhN,SACAgH,KACAoN,aAxImB,KACnB,MAAMzX,GACJA,EAAE+D,UACFA,EAAS2J,OACTA,EACA9E,MAAOirB,EACP/qB,OAAQgrB,EACR3mB,aAAcC,EACd7F,KAAM2F,EAAUtF,QAChBA,GACEvE,EACEQ,EAASR,EAAOQ,OAAOwpC,WACvB/gC,EAAejJ,EAAOiJ,eACtBgB,EAAYjK,EAAOkK,SAAWlK,EAAOQ,OAAO0J,QAAQC,QAC1D,IACIogC,EADAC,EAAgB,EAGhBhqC,EAAOypC,SACLhhC,GACFshC,EAAevqC,EAAO4J,SAASvQ,cAAc,uBACxCkxC,IACHA,EAAe7wC,EAAc,MAAO,sBACpCsG,EAAO4J,SAAS8O,OAAO6xB,IAEzBA,EAAa1wC,MAAM4L,OAAU,GAAE+qB,QAE/B+Z,EAAe5tC,EAAGtD,cAAc,uBAC3BkxC,IACHA,EAAe7wC,EAAc,MAAO,sBACpCiD,EAAG+b,OAAO6xB,MAIhB,IAAK,IAAIzrC,EAAI,EAAGA,EAAIuL,EAAOxR,OAAQiG,GAAK,EAAG,CACzC,MAAM+C,EAAUwI,EAAOvL,GACvB,IAAI8O,EAAa9O,EACbmL,IACF2D,EAAazE,SAAStH,EAAQ0N,aAAa,2BAA4B,KAEzE,IAAIk7B,EAA0B,GAAb78B,EACbq1B,EAAQ9hC,KAAKwL,MAAM89B,EAAa,KAChC1gC,IACF0gC,GAAcA,EACdxH,EAAQ9hC,KAAKwL,OAAO89B,EAAa,MAEnC,MAAMvpC,EAAWC,KAAKC,IAAID,KAAKE,IAAIQ,EAAQX,SAAU,IAAK,GAC1D,IAAI2oC,EAAK,EACLC,EAAK,EACLY,EAAK,EACL98B,EAAa,GAAM,GACrBi8B,EAAc,GAAR5G,EAAYp5B,EAClB6gC,EAAK,IACK98B,EAAa,GAAK,GAAM,GAClCi8B,EAAK,EACLa,EAAc,GAARzH,EAAYp5B,IACR+D,EAAa,GAAK,GAAM,GAClCi8B,EAAKhgC,EAAqB,EAARo5B,EAAYp5B,EAC9B6gC,EAAK7gC,IACK+D,EAAa,GAAK,GAAM,IAClCi8B,GAAMhgC,EACN6gC,EAAK,EAAI7gC,EAA0B,EAAbA,EAAiBo5B,GAErCl5B,IACF8/B,GAAMA,GAGH5gC,IACH6gC,EAAKD,EACLA,EAAK,GAGP,MAAM1sC,EAAa,WAAU8L,EAAe,GAAKwhC,iBAC/CxhC,EAAewhC,EAAa,qBACVZ,QAASC,QAASY,OAClCxpC,GAAY,GAAKA,GAAY,IAC/BspC,EAA6B,GAAb58B,EAA6B,GAAX1M,EAC9B6I,IAAKygC,EAA8B,IAAb58B,EAA6B,GAAX1M,IAE9CW,EAAQhI,MAAMsD,UAAYA,EACtBqD,EAAOuqB,cACTqf,EAAmBvoC,EAASX,EAAU+H,EAE1C,CAIA,GAHAvI,EAAU7G,MAAM8iC,gBAAmB,YAAW9yB,EAAa,MAC3DnJ,EAAU7G,MAAM,4BAA+B,YAAWgQ,EAAa,MAEnErJ,EAAOypC,OACT,GAAIhhC,EACFshC,EAAa1wC,MAAMsD,UAAa,oBAC9BqzB,EAAc,EAAIhwB,EAAO0pC,oBACnB1Z,EAAc,2CAA2ChwB,EAAO2pC,mBACnE,CACL,MAAMQ,EAAcxpC,KAAK0L,IAAI29B,GAA4D,GAA3CrpC,KAAKwL,MAAMxL,KAAK0L,IAAI29B,GAAiB,IAC7E55B,EACJ,KACCzP,KAAKypC,IAAmB,EAAdD,EAAkBxpC,KAAKK,GAAM,KAAO,EAC7CL,KAAKI,IAAmB,EAAdopC,EAAkBxpC,KAAKK,GAAM,KAAO,GAC5CqpC,EAASrqC,EAAO2pC,YAChBW,EAAStqC,EAAO2pC,YAAcv5B,EAC9Bud,EAAS3tB,EAAO0pC,aACtBK,EAAa1wC,MAAMsD,UAAa,WAAU0tC,SAAcC,uBACtDra,EAAe,EAAItC,SACbsC,EAAe,EAAIqa,sBAC7B,CAEF,MAAMC,GACHxmC,EAAQ6B,UAAY7B,EAAQqC,YAAcrC,EAAQ4B,oBAAsB0D,EAAa,EAAI,EAC5FnJ,EAAU7G,MAAMsD,UAAa,qBAAoB4tC,gBAC/C/qC,EAAOiJ,eAAiB,EAAIuhC,iBACdxqC,EAAOiJ,gBAAkBuhC,EAAgB,QAEzD9pC,EAAU7G,MAAMgG,YAAY,4BAA8B,GAAEkrC,MAAY,EA0BxE17B,cAxBqB9O,IACrB,MAAM5D,GAAEA,EAAE0N,OAAEA,GAAWrK,EAYvB,GAXAqK,EAAO1R,SAASkJ,IACdA,EAAQhI,MAAMspB,mBAAsB,GAAE5iB,MACtCsB,EACGvI,iBACC,gHAEDX,SAAS87B,IACRA,EAAM56B,MAAMspB,mBAAsB,GAAE5iB,KAAY,GAChD,IAGFP,EAAOQ,OAAOwpC,WAAWC,SAAWjqC,EAAOiJ,eAAgB,CAC7D,MAAMnH,EAAWnF,EAAGtD,cAAc,uBAC9ByI,IAAUA,EAASjI,MAAMspB,mBAAsB,GAAE5iB,MACvD,GASAmqB,gBAnJsB,KAEtB,MAAMzhB,EAAejJ,EAAOiJ,eAC5BjJ,EAAOqK,OAAO1R,SAASkJ,IACrB,MAAMX,EAAWC,KAAKC,IAAID,KAAKE,IAAIQ,EAAQX,SAAU,IAAK,GAC1DkpC,EAAmBvoC,EAASX,EAAU+H,EAAa,GACnD,EA8IF0hB,gBAAiB,IAAM3qB,EAAOQ,OAAOwpC,WACrCvf,YAAa,KAAM,EACnBD,gBAAiB,MACfxe,cAAe,EACfc,eAAgB,EAChB4B,qBAAqB,EACrByQ,gBAAiB,EACjBlU,aAAc,EACdO,gBAAgB,EAChB0I,kBAAkB,KAGxB,EC7Le,SAAkDnU,GAAA,IAA9BC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,GAAIjH,EAC7D6mB,EAAa,CACXokB,WAAY,CACVjgB,cAAc,EACdkgB,eAAe,KAInB,MAAMb,EAAqB,CAACvoC,EAASX,EAAUV,KAC7C,IAAI6pC,EAAerqC,EAAOiJ,eACtBpH,EAAQxI,cAAc,6BACtBwI,EAAQxI,cAAc,4BACtBixC,EAActqC,EAAOiJ,eACrBpH,EAAQxI,cAAc,8BACtBwI,EAAQxI,cAAc,+BACrBgxC,IACHA,EAAe1e,GAAanrB,EAAQqB,EAAS7B,EAAOiJ,eAAiB,OAAS,QAE3EqhC,IACHA,EAAc3e,GAAanrB,EAAQqB,EAAS7B,EAAOiJ,eAAiB,QAAU,WAE5EohC,IAAcA,EAAaxwC,MAAMo/B,QAAU93B,KAAKC,KAAKF,EAAU,IAC/DopC,IAAaA,EAAYzwC,MAAMo/B,QAAU93B,KAAKC,IAAIF,EAAU,GAAE,EAiEpEqpB,GAAW,CACTvd,OAAQ,OACRhN,SACAgH,KACAoN,aAtDmB,KACnB,MAAM/J,OAAEA,EAAQP,aAAcC,GAAQ/J,EAChCQ,EAASR,EAAOQ,OAAOwqC,WAC7B,IAAK,IAAIlsC,EAAI,EAAGA,EAAIuL,EAAOxR,OAAQiG,GAAK,EAAG,CACzC,MAAM+C,EAAUwI,EAAOvL,GACvB,IAAIoC,EAAWW,EAAQX,SACnBlB,EAAOQ,OAAOwqC,WAAWC,gBAC3B/pC,EAAWC,KAAKC,IAAID,KAAKE,IAAIQ,EAAQX,SAAU,IAAK,IAEtD,MAAMitB,EAAStsB,EAAQkO,kBAEvB,IAAIm7B,GADY,IAAMhqC,EAElBiqC,EAAU,EACVtB,EAAK7pC,EAAOQ,OAAOiL,SAAW0iB,EAASnuB,EAAOI,WAAa+tB,EAC3D2b,EAAK,EACJ9pC,EAAOiJ,eAKDc,IACTmhC,GAAWA,IALXpB,EAAKD,EACLA,EAAK,EACLsB,GAAWD,EACXA,EAAU,GAKZrpC,EAAQhI,MAAMuxC,QAAUjqC,KAAK0L,IAAI1L,KAAK8hC,MAAM/hC,IAAamJ,EAAOxR,OAE5D2H,EAAOuqB,cACTqf,EAAmBvoC,EAASX,GAE9B,MAAM/D,EAAa,eAAc0sC,QAASC,qBAAsBqB,iBAAuBD,QACtElgB,GAAaxqB,EAAQqB,GAC7BhI,MAAMsD,UAAYA,CAC7B,GAuBAkS,cApBqB9O,IACrB,MAAM8qB,EAAoBrrB,EAAOqK,OAAO/M,KAAKuE,GAAYD,EAAoBC,KAE7EwpB,EAAkB1yB,SAASgE,IACzBA,EAAG9C,MAAMspB,mBAAsB,GAAE5iB,MACjC5D,EAAGrD,iBACD,gHACAX,SAASmJ,IACTA,EAASjI,MAAMspB,mBAAsB,GAAE5iB,KAAY,GACnD,IAGJ6qB,GAA2B,CAAEprB,SAAQO,WAAU8qB,qBAAoB,EASnEX,gBApEsB,KAEP1qB,EAAOQ,OAAOwqC,WAC7BhrC,EAAOqK,OAAO1R,SAASkJ,IACrB,IAAIX,EAAWW,EAAQX,SACnBlB,EAAOQ,OAAOwqC,WAAWC,gBAC3B/pC,EAAWC,KAAKC,IAAID,KAAKE,IAAIQ,EAAQX,SAAU,IAAK,IAEtDkpC,EAAmBvoC,EAASX,EAAiB,GAC7C,EA4DFypB,gBAAiB,IAAM3qB,EAAOQ,OAAOwqC,WACrCvgB,YAAa,KAAM,EACnBD,gBAAiB,MACfxe,cAAe,EACfc,eAAgB,EAChB4B,qBAAqB,EACrBzD,aAAc,EACdiJ,kBAAmBlU,EAAOQ,OAAOiL,WAGvC,ECzGe,SAAuD1L,GAAA,IAA9BC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,GAAIjH,EAClE6mB,EAAa,CACXykB,gBAAiB,CACf/Q,OAAQ,GACRgR,QAAS,EACTC,MAAO,IACPvT,MAAO,EACPwT,SAAU,EACVzgB,cAAc,KAsFlBR,GAAW,CACTvd,OAAQ,YACRhN,SACAgH,KACAoN,aAtFmB,KACnB,MAAQ7O,MAAOirB,EAAa/qB,OAAQgrB,EAAYpmB,OAAEA,EAAMK,gBAAEA,GAAoB1K,EACxEQ,EAASR,EAAOQ,OAAO6qC,gBACvBpiC,EAAejJ,EAAOiJ,eACtB9L,EAAY6C,EAAOI,UACnBqrC,EAASxiC,EAA4BunB,EAAc,EAA1BrzB,EAA2CszB,EAAe,EAA3BtzB,EACxDm9B,EAASrxB,EAAezI,EAAO85B,QAAU95B,EAAO85B,OAChDl6B,EAAYI,EAAO+qC,MAEzB,IAAK,IAAIzsC,EAAI,EAAGjG,EAASwR,EAAOxR,OAAQiG,EAAIjG,EAAQiG,GAAK,EAAG,CAC1D,MAAM+C,EAAUwI,EAAOvL,GACjB+M,EAAYnB,EAAgB5L,GAE5B4sC,GAAgBD,EADF5pC,EAAQkO,kBACiBlE,EAAY,GAAKA,EACxD8/B,EACuB,mBAApBnrC,EAAOgrC,SACVhrC,EAAOgrC,SAASE,GAChBA,EAAelrC,EAAOgrC,SAE5B,IAAIN,EAAUjiC,EAAeqxB,EAASqR,EAAmB,EACrDR,EAAUliC,EAAe,EAAIqxB,EAASqR,EAEtCC,GAAcxrC,EAAYe,KAAK0L,IAAI8+B,GAEnCL,EAAU9qC,EAAO8qC,QAEE,iBAAZA,IAAkD,IAA1BA,EAAQpsC,QAAQ,OACjDosC,EAAWttC,WAAWwC,EAAO8qC,SAAW,IAAOz/B,GAEjD,IAAI4xB,EAAax0B,EAAe,EAAIqiC,EAAUK,EAC1CnO,EAAav0B,EAAeqiC,EAAUK,EAAmB,EAEzD3T,EAAQ,GAAK,EAAIx3B,EAAOw3B,OAAS72B,KAAK0L,IAAI8+B,GAG1CxqC,KAAK0L,IAAI2wB,GAAc,OAAOA,EAAa,GAC3Cr8B,KAAK0L,IAAI4wB,GAAc,OAAOA,EAAa,GAC3Ct8B,KAAK0L,IAAI++B,GAAc,OAAOA,EAAa,GAC3CzqC,KAAK0L,IAAIq+B,GAAW,OAAOA,EAAU,GACrC/pC,KAAK0L,IAAIs+B,GAAW,OAAOA,EAAU,GACrChqC,KAAK0L,IAAImrB,GAAS,OAAOA,EAAQ,GAErC,MAAM6T,EAAkB,eAAcrO,OAAgBC,OAAgBmO,iBAA0BT,iBAAuBD,eAAqBlT,KAM5I,GALiBhN,GAAaxqB,EAAQqB,GAC7BhI,MAAMsD,UAAY0uC,EAE3BhqC,EAAQhI,MAAMuxC,OAAmD,EAAzCjqC,KAAK0L,IAAI1L,KAAK8hC,MAAM0I,IAExCnrC,EAAOuqB,aAAc,CAEvB,IAAI+gB,EAAiB7iC,EACjBpH,EAAQxI,cAAc,6BACtBwI,EAAQxI,cAAc,4BACtB0yC,EAAgB9iC,EAChBpH,EAAQxI,cAAc,8BACtBwI,EAAQxI,cAAc,+BACrByyC,IACHA,EAAiBngB,GAAanrB,EAAQqB,EAASoH,EAAe,OAAS,QAEpE8iC,IACHA,EAAgBpgB,GAAanrB,EAAQqB,EAASoH,EAAe,QAAU,WAErE6iC,IACFA,EAAejyC,MAAMo/B,QAAU0S,EAAmB,EAAIA,EAAmB,GACvEI,IACFA,EAAclyC,MAAMo/B,SAAW0S,EAAmB,GAAKA,EAAmB,EAC9E,CACF,GAoBAt8B,cAlBqB9O,IACKP,EAAOqK,OAAO/M,KAAKuE,GAAYD,EAAoBC,KAE3DlJ,SAASgE,IACzBA,EAAG9C,MAAMspB,mBAAsB,GAAE5iB,MACjC5D,EAAGrD,iBACD,gHACAX,SAASmJ,IACTA,EAASjI,MAAMspB,mBAAsB,GAAE5iB,KAAY,GACnD,GACF,EASFkqB,YAAa,KAAM,EACnBD,gBAAiB,MACf9b,qBAAqB,KAG3B,ECxGe,SAAsD3O,GAAA,IAA9BC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,GAAIjH,EACjE6mB,EAAa,CACXolB,eAAgB,CACdC,cAAe,EACfC,mBAAmB,EACnBC,mBAAoB,EACpB1hB,aAAa,EACb7X,KAAM,CACJxS,UAAW,CAAC,EAAG,EAAG,GAClBk6B,OAAQ,CAAC,EAAG,EAAG,GACfrB,QAAS,EACTjB,MAAO,GAETzlB,KAAM,CACJnS,UAAW,CAAC,EAAG,EAAG,GAClBk6B,OAAQ,CAAC,EAAG,EAAG,GACfrB,QAAS,EACTjB,MAAO,MAKb,MAAMoU,EAAqB/mB,GACJ,iBAAVA,EAA2BA,EAC9B,GAAEA,MAiHZkF,GAAW,CACTvd,OAAQ,WACRhN,SACAgH,KACAoN,aAlHmB,KACnB,MAAM/J,OAAEA,EAAM3J,UAAEA,EAASgK,gBAAEA,GAAoB1K,EACzCQ,EAASR,EAAOQ,OAAOwrC,gBACrBG,mBAAoBv7B,GAAepQ,EAErC6rC,EAAmBrsC,EAAOQ,OAAOgL,eAEvC,GAAI6gC,EAAkB,CACpB,MAAMC,EAAS5hC,EAAgB,GAAK,EAAI1K,EAAOQ,OAAOoK,oBAAsB,EAC5ElK,EAAU7G,MAAMsD,UAAa,yBAAwBmvC,OACvD,CAEA,IAAK,IAAIxtC,EAAI,EAAGA,EAAIuL,EAAOxR,OAAQiG,GAAK,EAAG,CACzC,MAAM+C,EAAUwI,EAAOvL,GACjBuR,EAAgBxO,EAAQX,SACxBA,EAAWC,KAAKE,IACpBF,KAAKC,IAAIS,EAAQX,UAAWV,EAAOyrC,eACnCzrC,EAAOyrC,eAET,IAAIv7B,EAAmBxP,EAElBmrC,IACH37B,EAAmBvP,KAAKE,IACtBF,KAAKC,IAAIS,EAAQ6O,kBAAmBlQ,EAAOyrC,eAC3CzrC,EAAOyrC,gBAIX,MAAM9d,EAAStsB,EAAQkO,kBACjBiG,EAAI,CAAChW,EAAOQ,OAAOiL,SAAW0iB,EAASnuB,EAAOI,WAAa+tB,EAAQ,EAAG,GACtEoe,EAAI,CAAC,EAAG,EAAG,GACjB,IAAIC,GAAS,EACRxsC,EAAOiJ,iBACV+M,EAAE,GAAKA,EAAE,GACTA,EAAE,GAAK,GAET,IAAIxN,EAAO,CACTpI,UAAW,CAAC,EAAG,EAAG,GAClBk6B,OAAQ,CAAC,EAAG,EAAG,GACftC,MAAO,EACPiB,QAAS,GAEP/3B,EAAW,GACbsH,EAAOhI,EAAO+R,KACdi6B,GAAS,GACAtrC,EAAW,IACpBsH,EAAOhI,EAAOoS,KACd45B,GAAS,GAGXx2B,EAAErd,SAAQ,CAAC0sB,EAAOjd,KAChB4N,EAAE5N,GAAU,QAAOid,UAAc+mB,EAAkB5jC,EAAKpI,UAAUgI,SAAajH,KAAK0L,IAClF3L,EAAW0P,MACR,IAGP27B,EAAE5zC,SAAQ,CAAC0sB,EAAOjd,KAChBmkC,EAAEnkC,GAASI,EAAK8xB,OAAOlyB,GAASjH,KAAK0L,IAAI3L,EAAW0P,EAAW,IAGjE/O,EAAQhI,MAAMuxC,QAAUjqC,KAAK0L,IAAI1L,KAAK8hC,MAAM5yB,IAAkBhG,EAAOxR,OAErE,MAAM4zC,EAAkBz2B,EAAEvY,KAAK,MACzBivC,EAAgB,WAAUH,EAAE,kBAAkBA,EAAE,kBAAkBA,EAAE,SACpEI,EACJj8B,EAAmB,EACd,SAAQ,GAAK,EAAIlI,EAAKwvB,OAAStnB,EAAmBE,KAClD,SAAQ,GAAK,EAAIpI,EAAKwvB,OAAStnB,EAAmBE,KACnDg8B,EACJl8B,EAAmB,EACf,GAAK,EAAIlI,EAAKywB,SAAWvoB,EAAmBE,EAC5C,GAAK,EAAIpI,EAAKywB,SAAWvoB,EAAmBE,EAC5CzT,EAAa,eAAcsvC,MAAoBC,KAAgBC,IAGrE,GAAKH,GAAUhkC,EAAKyhC,SAAYuC,EAAQ,CACtC,IAAI1qC,EAAWD,EAAQxI,cAAc,wBAIrC,IAHKyI,GAAY0G,EAAKyhC,SACpBnoC,EAAW6pB,GAAanrB,EAAQqB,IAE9BC,EAAU,CACZ,MAAM+qC,EAAgBrsC,EAAO0rC,kBACzBhrC,GAAY,EAAIV,EAAOyrC,eACvB/qC,EACJY,EAASjI,MAAMo/B,QAAU93B,KAAKE,IAAIF,KAAKC,IAAID,KAAK0L,IAAIggC,GAAgB,GAAI,EAC1E,CACF,CAEA,MAAMhzB,EAAWmR,GAAaxqB,EAAQqB,GACtCgY,EAAShgB,MAAMsD,UAAYA,EAC3B0c,EAAShgB,MAAMo/B,QAAU2T,EACrBpkC,EAAKjO,SACPsf,EAAShgB,MAAM8iC,gBAAkBn0B,EAAKjO,OAE1C,GAqBA8U,cAlBqB9O,IACrB,MAAM8qB,EAAoBrrB,EAAOqK,OAAO/M,KAAKuE,GAAYD,EAAoBC,KAE7EwpB,EAAkB1yB,SAASgE,IACzBA,EAAG9C,MAAMspB,mBAAsB,GAAE5iB,MACjC5D,EAAGrD,iBAAiB,wBAAwBX,SAASmJ,IACnDA,EAASjI,MAAMspB,mBAAsB,GAAE5iB,KAAY,GACnD,IAGJ6qB,GAA2B,CAAEprB,SAAQO,WAAU8qB,oBAAmBC,WAAW,GAAO,EASpFb,YAAa,IAAMzqB,EAAOQ,OAAOwrC,eAAevhB,YAChDD,gBAAiB,MACf9b,qBAAqB,EACrBwF,kBAAmBlU,EAAOQ,OAAOiL,WAGvC,ECrJe,SAAmD1L,GAAA,IAA9BC,OAAEA,EAAM4mB,aAAEA,EAAY5f,GAAEA,GAAIjH,EAC9D6mB,EAAa,CACXkmB,YAAa,CACX/hB,cAAc,EACduP,QAAQ,EACRyS,eAAgB,EAChBC,eAAgB,KAwGpBziB,GAAW,CACTvd,OAAQ,QACRhN,SACAgH,KACAoN,aAxGmB,KACnB,MAAM/J,OAAEA,EAAMoF,YAAEA,GAAgBzP,EAC1BQ,EAASR,EAAOQ,OAAOssC,aACvBpuB,eAAEA,EAAczE,UAAEA,GAAcja,EAAOwZ,gBACvCrF,EAAmBnU,EAAOI,UAChC,IAAK,IAAItB,EAAI,EAAGA,EAAIuL,EAAOxR,OAAQiG,GAAK,EAAG,CACzC,MAAM+C,EAAUwI,EAAOvL,GACjBuR,EAAgBxO,EAAQX,SACxBA,EAAWC,KAAKE,IAAIF,KAAKC,IAAIiP,GAAgB,GAAI,GACvD,IAAI8d,EAAStsB,EAAQkO,kBACjB/P,EAAOQ,OAAOgL,iBAAmBxL,EAAOQ,OAAOiL,UACjDzL,EAAOU,UAAU7G,MAAMsD,UAAa,cAAa6C,EAAOsQ,qBAEtDtQ,EAAOQ,OAAOgL,gBAAkBxL,EAAOQ,OAAOiL,UAChD0iB,GAAU9jB,EAAO,GAAG0F,mBAEtB,IAAIk9B,EAAKjtC,EAAOQ,OAAOiL,SAAW0iB,EAASnuB,EAAOI,WAAa+tB,EAC3D+e,EAAK,EACT,MAAMC,GAAM,IAAMhsC,KAAK0L,IAAI3L,GAC3B,IAAI82B,EAAQ,EACRsC,GAAU95B,EAAOusC,eAAiB7rC,EAElCksC,EAAQ5sC,EAAOwsC,eAAsC,IAArB7rC,KAAK0L,IAAI3L,GAE7C,MAAM0M,EACJ5N,EAAOkK,SAAWlK,EAAOQ,OAAO0J,QAAQC,QAAUnK,EAAOkK,QAAQgkB,KAAOpvB,EAAIA,EAExEuuC,GACHz/B,IAAe6B,GAAe7B,IAAe6B,EAAc,IAC5DvO,EAAW,GACXA,EAAW,IACV+Y,GAAaja,EAAOQ,OAAOiL,UAC5B0I,EAAmBuK,EACf4uB,GACH1/B,IAAe6B,GAAe7B,IAAe6B,EAAc,IAC5DvO,EAAW,GACXA,GAAY,IACX+Y,GAAaja,EAAOQ,OAAOiL,UAC5B0I,EAAmBuK,EAErB,GAAI2uB,GAAiBC,EAAe,CAClC,MAAMC,GAAe,EAAIpsC,KAAK0L,KAAK1L,KAAK0L,IAAI3L,GAAY,IAAO,MAAS,GACxEo5B,IAAW,GAAKp5B,EAAWqsC,EAC3BvV,IAAU,GAAMuV,EAChBH,GAAS,GAAKG,EACdL,GAAS,GAAKK,EAAcpsC,KAAK0L,IAAI3L,GAA/B,GACR,CAWA,GAPE+rC,EAFE/rC,EAAW,EAEP,QAAO+rC,UAAWG,EAAQjsC,KAAK0L,IAAI3L,QAChCA,EAAW,EAEd,QAAO+rC,WAAYG,EAAQjsC,KAAK0L,IAAI3L,QAEpC,GAAE+rC,OAELjtC,EAAOiJ,eAAgB,CAC1B,MAAMoU,EAAQ6vB,EACdA,EAAKD,EACLA,EAAK5vB,CACP,CAEA,MAAMsvB,EACJzrC,EAAW,EAAK,IAAE,GAAK,EAAI82B,GAAS92B,GAAc,IAAE,GAAK,EAAI82B,GAAS92B,GAElE/D,EAAa,yBACH8vC,MAAOC,MAAOC,yBAClB3sC,EAAO85B,OAASA,EAAS,wBAC3BqS,aAGV,GAAInsC,EAAOuqB,aAAc,CAEvB,IAAIjpB,EAAWD,EAAQxI,cAAc,wBAChCyI,IACHA,EAAW6pB,GAAanrB,EAAQqB,IAE9BC,IACFA,EAASjI,MAAMo/B,QAAU93B,KAAKE,IAAIF,KAAKC,KAAKD,KAAK0L,IAAI3L,GAAY,IAAO,GAAK,GAAI,GACrF,CAEAW,EAAQhI,MAAMuxC,QAAUjqC,KAAK0L,IAAI1L,KAAK8hC,MAAM5yB,IAAkBhG,EAAOxR,OACpDmyB,GAAaxqB,EAAQqB,GAC7BhI,MAAMsD,UAAYA,CAC7B,GAoBAkS,cAjBqB9O,IACrB,MAAM8qB,EAAoBrrB,EAAOqK,OAAO/M,KAAKuE,GAAYD,EAAoBC,KAC7EwpB,EAAkB1yB,SAASgE,IACzBA,EAAG9C,MAAMspB,mBAAsB,GAAE5iB,MACjC5D,EAAGrD,iBAAiB,wBAAwBX,SAASmJ,IACnDA,EAASjI,MAAMspB,mBAAsB,GAAE5iB,KAAY,GACnD,IAGJ6qB,GAA2B,CAAEprB,SAAQO,WAAU8qB,qBAAoB,EASnEZ,YAAa,KAAM,EACnBD,gBAAiB,MACf9b,qBAAqB,EACrBwF,kBAAmBlU,EAAOQ,OAAOiL,WAGvC,G,OvBvHA6a,EAAO0F,IAAIvF,I"}