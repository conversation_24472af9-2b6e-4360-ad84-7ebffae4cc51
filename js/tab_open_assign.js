function tabOpen(evt, catg) {
    document.querySelectorAll('.tabcontent').forEach((tabContent) => {
        tabContent.style.display = 'none';
    });

    document.querySelectorAll('.tab-button').forEach((tabButton) => {
        tabButton.classList.remove('shadowTabActive');
    });

    const targetContent = document.getElementById(catg);
    if (targetContent) {
        targetContent.style.display = 'block';
    } else {
        console.error(`Element s ID "${catg}" nebyl nalezen.`);
    }

    evt.currentTarget.classList.add('shadowTabActive');
    
    // Reset card container and show button when switching categories
    const viewcard = document.querySelector('.card-container');
    const viewCardBtn = document.querySelector('.cardViewBtn');
    const cardOverlay = document.querySelector('.card-overlay');
    
    if (viewcard && viewCardBtn && cardOverlay) {
        viewcard.classList.remove('card-container-active');
        cardOverlay.classList.remove('cardoverlayClose');
        viewCardBtn.style.display = 'flex';
    }
}


document.addEventListener('DOMContentLoaded', () => {

    document.querySelectorAll('.tab-button').forEach((button) => {
        button.addEventListener('click', (evt) => {
            const termId = evt.currentTarget.getAttribute('data-term-id');
            tabOpen(evt, 'tab-' + termId);
        });
    });

    // viewBox===========
    const viewcard = document.querySelector('.card-container');
    const viewCardBtn = document.querySelector('.cardViewBtn');
    const cardOverlay = document.querySelector('.card-overlay');
    viewCardBtn.addEventListener('click', () => {
        viewcard.classList.toggle('card-container-active');
        cardOverlay.classList.toggle('cardoverlayClose');
        
        // Hide button when all references are shown
        if (viewcard.classList.contains('card-container-active')) {
            viewCardBtn.style.display = 'none';
        }
    });

});
