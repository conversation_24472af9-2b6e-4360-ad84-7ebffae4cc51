<?php

use Umimeweby\UWTheme\Settings\UW_Settings_Page;

$contact_tel = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::FIELD_CONTACT_TEL_FIELD, ['object_type' => 'setting'], UW_Settings_Page::OPTION_CONTACT_SETTING);
$contact_email = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::FIELD_CONTACT_EMAIL_FIELD, ['object_type' => 'setting'], UW_Settings_Page::OPTION_CONTACT_SETTING);

?>
<div class="relative z-40 mx-auto mt-8 mb-16 flex w-full max-w-[788px] flex-col items-center justify-between gap-5 sm:mb-24 md:mt-11 md:flex-row xl:mb-[54px]">
    <div class="w-full md:max-w-[242px]">
        <?php
        $tel = $contact_tel;
        $firstNumber = substr($tel, 0, 4);
        $lastNumber = substr($tel, 5);
        ?>
        <a href="tel:<?= $contact_tel ?? '' ?>" class="flex items-center justify-center gap-2.5 text-xl font-bold leading-6 text-theme-purple md:justify-end lg:text-2xl">
            <?= $firstNumber ?><svg xmlns="http://www.w3.org/2000/svg" width="30" height="31" fill="none">
                <path fill="#FE1CA6"
                      d="M28.35 21.133c-2.05 0-4.033-.333-5.883-.933a1.629 1.629 0 0 0-1.684.4l-2.616 3.283c-4.717-2.25-9.134-6.5-11.484-11.383l3.25-2.767a1.7 1.7 0 0 0 .4-1.7A18.589 18.589 0 0 1 9.4 2.15C9.4 1.25 8.65.5 7.75.5H1.983C1.083.5 0 .9 0 2.15 0 17.633 12.883 30.5 28.35 30.5c1.183 0 1.65-1.05 1.65-1.967v-5.75c0-.9-.75-1.65-1.65-1.65Z" />
            </svg><?= $lastNumber ?>
        </a>
        <p class="mt-1 text-center text-sm font-normal md:ml-auto md:mt-0 md:w-max md:text-left lg:text-base">
            Po - Pá 8 - 16 hod.
        </p>
    </div>
    <div class="hidden w-max flex-col gap-[5px] md:flex">
        <span class="block h-[5px] w-[5px] rounded-full bg-[#d9d9d9]"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-[#d9d9d9]"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-theme-pink"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-theme-pink"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-[#d9d9d9]"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-[#d9d9d9]"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-[#d9d9d9]"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-[#d9d9d9]"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-[#d9d9d9]"></span>
    </div>
    <div class="w-full md:max-w-[297px]">
        <a href="mailto:<?= $contact_email ?? '' ?>"
            class="flex items-center justify-center gap-2.5 text-xl font-bold leading-6 text-theme-purple md:justify-start lg:text-2xl">
            <?php
            $email = $contact_email;
            $firstWords = substr($email, 0, 4);
            $lastWords = substr($email, 5);
            ?>
            <?= $firstWords ?><svg xmlns="http://www.w3.org/2000/svg" width="30" height="31" fill="none">
                <path fill="#FE1CA6"
                      d="M15 .5C6.72.5 0 7.22 0 15.5c0 8.28 6.72 15 15 15h7.5v-3H15c-6.51 0-12-5.49-12-12s5.49-12 12-12 12 5.49 12 12v2.145C27 18.83 25.935 20 24.75 20s-2.25-1.17-2.25-2.355V15.5c0-4.14-3.36-7.5-7.5-7.5-4.14 0-7.5 3.36-7.5 7.5 0 4.14 3.36 7.5 7.5 7.5 2.07 0 3.96-.84 5.31-2.205C21.285 22.13 22.965 23 24.75 23c2.955 0 5.25-2.4 5.25-5.355V15.5c0-8.28-6.72-15-15-15ZM15 20c-2.49 0-4.5-2.01-4.5-4.5S12.51 11 15 11s4.5 2.01 4.5 4.5S17.49 20 15 20Z" />
            </svg><?= $lastWords ?>
        </a>
        <p class="text-center text-sm font-normal md:w-max md:text-left lg:text-base">
            odpověď do 24 hod.
        </p>
    </div>
    <div class="hidden w-max flex-col gap-[5px] md:flex">
        <span class="block h-[5px] w-[5px] rounded-full bg-[#d9d9d9]"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-[#d9d9d9]"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-[#d9d9d9]"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-[#d9d9d9]"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-[#d9d9d9]"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-theme-pink"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-theme-pink"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-[#d9d9d9]"></span>
        <span class="block h-[5px] w-[5px] rounded-full bg-[#d9d9d9]"></span>
    </div>
    <a href="<?= esc_url('https://www.linkedin.com/company/umimeweby/') ?>" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" width="31" height="30" fill="none">
            <path fill="#FE1CA6"
                  d="M6.999 9.64H.963a.485.485 0 0 0-.485.484v19.391c0 .268.217.485.485.485h6.036a.485.485 0 0 0 .485-.485v-19.39a.485.485 0 0 0-.485-.486ZM3.983 0A3.985 3.985 0 0 0 0 3.979a3.986 3.986 0 0 0 3.983 3.98 3.984 3.984 0 0 0 3.98-3.98A3.984 3.984 0 0 0 3.983 0ZM22.351 9.158c-2.424 0-4.216 1.042-5.303 2.226v-1.26a.485.485 0 0 0-.485-.485h-5.78a.485.485 0 0 0-.485.485v19.391c0 .268.217.485.485.485h6.022a.485.485 0 0 0 .485-.485v-9.594c0-3.233.878-4.492 3.132-4.492 2.454 0 2.65 2.019 2.65 4.658v9.428c0 .268.216.485.484.485h6.025a.485.485 0 0 0 .485-.485V18.88c0-4.807-.916-9.721-7.715-9.721Z" />
        </svg></a>
</div>
