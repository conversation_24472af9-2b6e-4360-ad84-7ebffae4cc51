<?php

use Umimeweby\UWTheme\CustomFields\AboutUs\Why_We_About_Us_Custom_Fields;

$title = rwmb_get_value(Why_We_About_Us_Custom_Fields::PREFIX . Why_We_About_Us_Custom_Fields::ABOUT_US_WHY_WE_FIELD_TITLE);
$description = rwmb_get_value(Why_We_About_Us_Custom_Fields::PREFIX . Why_We_About_Us_Custom_Fields::ABOUT_US_WHY_WE_FIELD_DESCRIPTION);
$another_description = rwmb_get_value(Why_We_About_Us_Custom_Fields::PREFIX . Why_We_About_Us_Custom_Fields::ABOUT_US_WHY_WE_FIELD_ANOTHER_DESCRIPTION);
$btn_text = rwmb_get_value(Why_We_About_Us_Custom_Fields::PREFIX . Why_We_About_Us_Custom_Fields::ABOUT_US_WHY_WE_FIELD_BTN_TEXT);
$group = rwmb_meta(Why_We_About_Us_Custom_Fields::PREFIX . Why_We_About_Us_Custom_Fields::ABOUT_US_WHY_WE_FIELD_GROUP);
$group_number = Why_We_About_Us_Custom_Fields::PREFIX . Why_We_About_Us_Custom_Fields::ABOUT_US_WHY_WE_FIELD_GROUP_NUMBER;
$group_title = Why_We_About_Us_Custom_Fields::PREFIX . Why_We_About_Us_Custom_Fields::ABOUT_US_WHY_WE_FIELD_GROUP_TITLE;
$group_description = Why_We_About_Us_Custom_Fields::PREFIX . Why_We_About_Us_Custom_Fields::ABOUT_US_WHY_WE_FIELD_GROUP_DESCRIPTION;

?>
<section class="relative mx-auto mb-[69px] w-full max-w-[1440px] rounded-[50px] bg-purple-gradient bg-cover bg-no-repeat shadow-blueBox md:rounded-[100px]">
    <div class="mx-auto w-full max-w-[1170px] px-4 pt-12 pb-9 sm:px-8 md:pt-14 md:pb-[69px]">
        <h2 class="mb-4 text-center text-2xl font-bold leading-normal text-white md:mb-[22px] md:text-left md:text-3xl">
            <?= $title ?? '' ?>
        </h2>
        <div class="relative flex w-full flex-col items-start gap-3 sm:gap-[60px] lg:flex-row">
            <div class="relative w-full overflow-hidden sm:overflow-visible lg:max-w-[560px]">
                <div class="viewTextBox">
                    <p class="text-xs font-normal leading-4 text-center text-light-purple sm:text-base sm:leading-6 md:text-left">
                        <?= $description ?? '' ?>
                    </p>
                    <p class="text-center viewText mt-7 md:text-left text-light-purple">
                        <?= $another_description ?? '' ?>
                    </p>
                    <button class="relative z-50 flex justify-center mx-auto text-xs leading-4 text-center text-white uppercase viewTextBtn sm:hidden">
                        <?= $btn_text ?? '' ?>
                    </button>
                    <span class="text-overlay bg-gradient-to-b from-[#2A0151]/60 to-[#2A0151] bg-cover bg-no-repeat"></span>
                </div>
            </div>
            <div class="scroll-pink relative z-30 grid w-full grid-cols-1 md:grid-cols-2 md:gap-10 md:pr-6 lg:block lg:h-[calc(460px-150px)] lg:max-w-[550px] lg:overflow-auto">
                <?php foreach ($group as $why_we) : ?>
                    <div class="flex items-center gap-[18px]">
                        <h1 class="text-[100px] font-extrabold uppercase leading-none text-white/20">
                            <?php
                            $number = strval($why_we[$group_number]);
                            $first_number = substr($number, 0, 1);
                            $second_number = substr($number, 1);
                            ?>
                            <span class="hidden lg:inline"><?= $first_number ?></span><?= $second_number ?>
                        </h1>
                        <div>
                            <h3 class="text-sm font-bold leading-5 text-white sm:text-xl sm:leading-7">
                                <?= $why_we[$group_title] ?? '' ?>
                            </h3>
                            <p class="text-xs font-normal leading-4 text-white sm:text-sm sm:leading-5">
                                <?= $why_we[$group_description] ?>
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <span class="absolute bottom-0 left-1/2 block h-0.5 w-[70%] -translate-x-1/2 bg-bgborder"></span>
</section>