<?php

use Umimeweby\UWTheme\CustomFields\About_Us_Hero_Bottom;

$title = rwmb_get_value(About_Us_Hero_Bottom::PREFIX . About_Us_Hero_Bottom::ABOUT_US_HERO_TITLE);
$description = rwmb_get_value(About_Us_Hero_Bottom::PREFIX . About_Us_Hero_Bottom::ABOUT_US_HERO_DESC);



?>

<section class="relative z-50 mx-auto mb-5 w-full max-w-[1480px]">
    <hr class="mx-5 mt-14 mb-14 lg:hidden" />
    <div class="relative lg:-mt-[180px]">
        <svg fill="none" class="absolute top-0 right-0 hidden object-cover pointer-events-none -left-1 -z-10 lg:block" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1441 379">
            <path d="M71.469 51.641C89.072 19.78 122.598 0 158.999 0H1281c36.4 0 69.93 19.78 87.53 51.641L1440 181H0L71.469 51.641ZM1441 378.5H1V181h1440v197.5Z" fill="#fff" />
        </svg>

        <div class="relative z-20 mb- px-5 text-center text-black lg:pl-[135px] lg:pt-15 lg:text-left">
            <h4 class="heading-tertiary md:heading-secondary !text-theme-purple">Od správy webu po vývoj software</h4>
            <p class="text-base uppercase leading-normal">Spravujeme, optimalizujeme a vyvíjíme webová řešení na míru</p>
        </div>
        <div class="container flex flex-col items-center px-5 py-12 md:py-24 mx-auto mt-4 mb-12 md:flex-row lg:pl-[135px] lg:pt-15">
            <div class="flex flex-col items-center mb-16 text-center lg:flex-grow md:w-1/2 lg:pr-24 md:pr-16 md:items-start md:text-left md:mb-0">
                <h1 class="mb-4 text-3xl font-medium text-gray-900 title-font sm:text-4xl"><?= $title ?>
                </h1>
                <div class="mb-8 text-base text-gray-900"><?= $description ?></div>
            </div>
            <div class="w-5/6 lg:max-w-lg md:w-1/2">
                <img class="object-cover object-center rounded" alt="hero" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/home/<USER>" loading="lazy" decoding="async">
            </div>
        </div>
    </div>
</section>