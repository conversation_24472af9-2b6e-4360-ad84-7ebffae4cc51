<?php

use Um<PERSON>weby\UWTheme\Settings\UW_Settings_Page;

use Umimeweby\UWTheme\Settings\UW_Sections\UW_New_Or_Existing_Settings;

$new_post_id = rwmb_meta(UW_Settings_Page::PREFIX . UW_New_Or_Existing_Settings::FIELD_LINK_NEW, ['object_type' => 'setting'], UW_New_Or_Existing_Settings::OPTION_NAME);
$new_post_link = get_permalink($new_post_id);

$existing_post_id = rwmb_meta(UW_Settings_Page::PREFIX . UW_New_Or_Existing_Settings::FIELD_LINK_EXISTING, ['object_type' => 'setting'], UW_New_Or_Existing_Settings::OPTION_NAME);
$existing_post_link = get_permalink($existing_post_id);

$post_id = get_the_ID();
$services_group = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::OUR_SERVICES_FIELD_GROUP, ['object_type' => 'setting'], UW_Settings_Page::OPTION_OUR_SERVICES_SETTING);
$services_link = UW_Settings_Page::PREFIX . UW_Settings_Page::OUR_SERVICES_FIELD_LINK;

$another_services_title = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::OUR_SERVICES_FIELD_ANOTHER_SERVICES_TITLE, ['object_type' => 'setting'], UW_Settings_Page::OPTION_OUR_SERVICES_SETTING);
$another_services_description = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::OUR_SERVICES_FIELD_ANOTHER_SERVICES_DESCRIPTION, ['object_type' => 'setting'], UW_Settings_Page::OPTION_OUR_SERVICES_SETTING);
$left_to_button_title = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::OUR_SERVICES_FIELD_LEFT_TO_BUTTON_TITLE, ['object_type' => 'setting'], UW_Settings_Page::OPTION_OUR_SERVICES_SETTING);
$left_to_button_description = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::OUR_SERVICES_FIELD_LEFT_TO_BUTTON_DESCRIPTION, ['object_type' => 'setting'], UW_Settings_Page::OPTION_OUR_SERVICES_SETTING);

?>

<section class="w-full px-2.5 md:px-5">
    <div class="relative mx-auto min-h-[1030px] w-full max-w-[1440px] rounded-[50px] bg-plants bg-theme-purple bg-cover bg-no-repeat pb-14 md:rounded-[100px] lg:pb-10">
        <span class="absolute top-0 left-1/2 z-10 inline-block h-0.5 w-full max-w-[80vw] -translate-x-1/2 bg-bgborder sm:max-w-[50vw] lg:z-50 xl:max-w-[1170px]"></span>
        <img src="<?= get_stylesheet_directory_uri() ?>/assets/images/home/<USER>" alt="planets" class="absolute -top-10 left-16 z-20 h-auto w-24 object-contain md:pointer-events-none md:-top-20 md:left-[120px] md:w-auto" loading="lazy" decoding="async" />
        <div class="mx-auto w-full max-w-[750px] px-4 pt-[65px] lg:max-w-[1170px]">
            <div class="mx-auto grid w-full max-w-[450px] grid-cols-1 gap-12 text-center md:max-w-full md:gap-5 md:text-left lg:grid-cols-[1fr,max-content,1fr] xl:gap-11">
                <div class="flex flex-col items-center gap-4 md:flex-row xl:gap-7">
                    <img src="<?= get_stylesheet_directory_uri() ?>/assets/images/home/<USER>" alt="planets" class="blend-screen h-auto w-full max-w-[120px] object-contain xl:w-auto xl:max-w-max" loading="lazy" decoding="async" />
                    <div class="w-full lg:max-w-[300px]">
                        <h2 class="text-2xl font-bold text-white mb-3 md:mt-2.5 md:text-[28px]">
                            Převezmeme správu vašeho webu
                        </h2>
                        <p class="mb-[22px] text-base font-normal text-white/80">
                            Máte funkční web nebo aplikaci a hledáte spolehlivého partnera pro jejich další rozvoj a údržbu? Postaráme se o váš projekt, ať už potřebujete přidat nové funkce, vyřešit technické problémy nebo zajistit pravidelnou údržbu.
                        </p>
                        <a href="<?= esc_url($existing_post_link) ?>" class="mx-auto flex h-[62px] w-full max-w-[222px] items-center justify-center gap-2.5 rounded-[20px] border-2 text-base font-bold text-light-purple shadow-btnwhite sm:border-[#d9d9d9] sm:text-[#D9D9D9] md:mx-0 hover:shadow-btnBlue hover:shadow-theme-blue hover:bg-theme-blue hover:border-theme-blue">
                            Zjistit více o správě
                            <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" fill="none">
                                <path fill="#FEFEFE" fill-rule="evenodd" d="M.663 6.603c-.555.474-.555 1.243 0 1.717 2.449 2.092 4.906 4.175 7.356 6.266.556.475 1.457.475 2.012 0l2.258-1.93c-2.036-1.73-4.054-3.492-6.103-5.201C8.2 5.722 10.226 4 12.248 2.275L9.986.355c-.554-.473-1.456-.473-2.01 0C5.535 2.437 3.1 4.521.662 6.603Z" clip-rule="evenodd" />
                                <path fill="#FEFEFE" fill-rule="evenodd" d="M8.711 7.344c2.037 1.73 4.059 3.483 6.103 5.201l-6.061 5.18c.753.64 1.51 1.28 2.26 1.92.556.473 1.457.473 2.012 0l7.312-6.248c.555-.474.555-1.243 0-1.717-2.45-2.092-4.906-4.175-7.356-6.267-.556-.474-1.456-.474-2.012 0L8.71 7.344Z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </div>
                </div>
                <span class="hidden h-full w-0.5 border-r border-dashed border-white lg:block"></span>
                <div class="flex flex-col items-center gap-7 md:flex-row">
                    <img src="<?= get_stylesheet_directory_uri() ?>/assets/images/home/<USER>" alt="planets" class="h-auto w-full max-w-[100px] object-contain md:max-w-[120px] xl:w-auto xl:max-w-max" loading="lazy" decoding="async" />
                    <div class="w-full lg:max-w-[300px]">
                        <h2 class="text-2xl font-bold text-white mb-3 md:mt-2.5 md:text-[28px]">
                            Vytvoříme váš nový projekt
                        </h2>
                        <p class="mb-[22px] text-base font-normal text-white/80">
                            Máte nápad na web nebo aplikaci a hledáte zkušený tým pro jeho realizaci? Pomůžeme vám od návrhu přes vývoj až po spuštění. Postaráme se o profesionální technické řešení vašeho byznysu.
                        </p>
                        <a href="<?= esc_url($new_post_link) ?>" class="mx-auto flex h-[62px] w-full max-w-[222px] items-center justify-center gap-2.5 rounded-[20px] border-2 text-base font-bold text-light-purple shadow-btnwhite sm:border-[#d9d9d9] sm:text-[#D9D9D9] md:mx-0 hover:shadow-btnBlue hover:shadow-theme-blue hover:bg-theme-blue hover:border-theme-blue">
                            Rozjet nový projekt
                            <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" fill="none">
                                <path fill="currentColor" fill-rule="evenodd" d="M.663 6.603c-.555.474-.555 1.243 0 1.717 2.449 2.092 4.906 4.175 7.356 6.266.556.475 1.457.475 2.012 0l2.258-1.93c-2.036-1.73-4.054-3.492-6.103-5.201C8.2 5.722 10.226 4 12.248 2.275L9.986.355c-.554-.473-1.456-.473-2.01 0C5.535 2.437 3.1 4.521.662 6.603Z" clip-rule="evenodd" />
                                <path fill="currentColor" fill-rule="evenodd" d="M8.711 7.344c2.037 1.73 4.059 3.483 6.103 5.201l-6.061 5.18c.753.64 1.51 1.28 2.26 1.92.556.473 1.457.473 2.012 0l7.312-6.248c.555-.474.555-1.243 0-1.717-2.45-2.092-4.906-4.175-7.356-6.267-.556-.474-1.456-.474-2.012 0L8.71 7.344Z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            <span class="mt-12 inline-block h-0.5 w-full bg-bgborderTwo md:mt-[64px]"></span>
            <!-- ::::::::::::cards:::::::::::: -->
            <div class="w-full mt-12">
                <h2 class="heading-tertiary sm:heading-secondary mb-2 mt-1.5 text-center text-white sm:mb-0 sm:text-left">
                    <?= $another_services_title ?? '' ?>
                </h2>
                <p class="text-center text-xs font-normal uppercase text-[#eaeaea] sm:text-left sm:text-base sm:leading-6">
                    <?= $another_services_description ?? '' ?>
                </p>
                <!-- ::::::::::::card container:::::::::::: -->
                <div class="w-full pt-2 mt-9">
                    <div class="swiper nase-slider -mt-8 -mb-16 w-full pt-8 <?= is_page('sluzby') ? 'pb-[8rem]' : 'pb-[6rem]' ?>">
                        <div class="items-stretch swiper-wrapper">
                            <?php if (!empty($services_group)) : ?>
                                <?php foreach ($services_group as $one_service) : ?>
                                    <?php
                                    $service_id = $one_service[$services_link];
                                    $service_title = get_the_title($service_id);
                                    $service_url = get_permalink($service_id);
                                    $service_thumbnail = get_the_post_thumbnail($service_id, 'full');
                                    $service_content = get_post_field('post_content', $service_id);
                                    $service_excerpt = wp_trim_words($service_content, 15);
                                    ?>
                                    <div class="items-stretch h-full swiper-slide">
                                        <a href="<?= trailingslashit(esc_url($service_url ?? '')) ?>" class="group relative block h-full w-full rounded-[50px] bg-white py-9 px-[30px] ring-[30px] ring-transparent transition-all duration-300 hover:z-[9999] hover:bg-theme-pink hover:ring-theme-pink/20 xl:min-h-[322px] max-w-[212px]">
                                            <div class="flex items-center flex-grow gap-5">
                                                <span class="ml-3 flex h-[50px] w-[50px] flex-shrink-0 items-center justify-center rounded-full bg-light-purple ring-[10px] ring-light-purple/10 transition-all duration-300 group-hover:bg-white group-hover:ring-white/20">
                                                    <?= $service_thumbnail ?>
                                                </span>
                                                <ul class="flex items-center gap-1">
                                                    <li>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" fill="none">
                                                            <path fill="#fff" d="M8.421 12.859 13.625 16l-1.38-5.92 4.597-3.983-6.055-.514L8.421 0 6.055 5.583 0 6.097l4.598 3.983L3.217 16l5.204-3.141Z" />
                                                        </svg>
                                                    </li>
                                                    <li>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" fill="none">
                                                            <path fill="#fff" d="M8.421 12.859 13.625 16l-1.38-5.92 4.597-3.983-6.055-.514L8.421 0 6.055 5.583 0 6.097l4.598 3.983L3.217 16l5.204-3.141Z" />
                                                        </svg>
                                                    </li>
                                                    <li>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" fill="none">
                                                            <path fill="#fff" d="M8.421 12.859 13.625 16l-1.38-5.92 4.597-3.983-6.055-.514L8.421 0 6.055 5.583 0 6.097l4.598 3.983L3.217 16l5.204-3.141Z" />
                                                        </svg>
                                                    </li>
                                                </ul>
                                            </div>
                                            <h4 class="mt-[26px] mb-2.5 text-lg font-extrabold uppercase leading-[22px] transition-all duration-300 group-hover:text-white min-h-[6rem]">
                                                <?= $service_title ?? '' ?>
                                            </h4>
                                            <p class="mb-1.5 transition-all duration-300 group-hover:text-white min-h-[10.5rem]">
                                                <?= $service_excerpt ?? '' ?>
                                            </p>
                                            <button class="ml-auto flex items-center gap-2.5 text-xl font-bold leading-5 transition-all duration-300 group-hover:text-white mt-auto">
                                                Více
                                                <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="none">
                                                    <path fill="currentColor" d="M15 0C6.72 0 0 6.72 0 15c0 8.28 6.72 15 15 15 8.28 0 15-6.72 15-15 0-8.28-6.72-15-15-15Zm-3 21.75V8.25L21 15l-9 6.75Z" />
                                                </svg>
                                            </button>
                                            <div class="invisible absolute top-[112%] left-1/2 flex w-full max-w-[170px] -translate-x-1/2 items-center justify-center gap-1 opacity-0 transition-all duration-300 group-hover:visible group-hover:opacity-100">
                                                <ul class="flex items-center gap-2">
                                                    <li>
                                                        <svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M6.31579 9.64421L10.2189 12L9.18316 7.56L12.6316 4.57263L8.09053 4.18737L6.31579 0L4.54105 4.18737L0 4.57263L3.44842 7.56L2.41263 12L6.31579 9.64421Z" fill="#8078A0" />
                                                        </svg>
                                                    </li>
                                                    <li>
                                                        <svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M6.31579 9.64421L10.2189 12L9.18316 7.56L12.6316 4.57263L8.09053 4.18737L6.31579 0L4.54105 4.18737L0 4.57263L3.44842 7.56L2.41263 12L6.31579 9.64421Z" fill="#8078A0" />
                                                        </svg>
                                                    </li>
                                                    <li>
                                                        <svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M6.31579 9.64421L10.2189 12L9.18316 7.56L12.6316 4.57263L8.09053 4.18737L6.31579 0L4.54105 4.18737L0 4.57263L3.44842 7.56L2.41263 12L6.31579 9.64421Z" fill="#8078A0" />
                                                        </svg>
                                                    </li>
                                                </ul>
                                                <p>velmi oblíbené</p>
                                            </div>
                                        </a>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>

                        <div class="nase-dots mt-8 flex items-center justify-center gap-2.5"></div>
                    </div>
                </div>
                <?php if (!is_page('homepage')) : ?>
                    <div class="mt-14 ml-auto flex w-full max-w-[710px] flex-col items-center justify-between gap-4 pb-2.5 text-center md:flex-row md:text-left lg:mt-[90px] lg:pb-0">
                        <button id="button-scroll-to" class="btn-primary order-last mx-auto border-2 border-light-purple bg-transparent bg-purple-gradient text-light-purple shadow-btnwhite sm:w-full sm:max-w-[247px] md:order-none md:mx-0">
                            Více služeb
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" style="transform: rotate(90deg);">
                                <path d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM6.4 11.6V4.4L11.2 8L6.4 11.6Z" fill="currentColor" />
                            </svg>
                        </button>
                    </div>
                <?php endif ?>
                <?php if (is_page('homepage')) : ?>
                    <div class="mt-14 ml-auto flex w-full max-w-[710px] flex-col items-center justify-between gap-4 pb-2.5 text-center md:flex-row md:text-left lg:mt-[90px] lg:pb-0">
                        <a href="/nase-sluzby/" class="btn-primary order-last mx-auto border-2 border-light-purple bg-transparent bg-purple-gradient text-light-purple shadow-btnwhite sm:w-full sm:max-w-[247px] md:order-none md:mx-0">
                            Více služeb
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"">
                                    <path d=" M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM6.4 11.6V4.4L11.2 8L6.4 11.6Z" fill="currentColor" />
                            </svg>
                        </a>
                    </div>
                <?php endif ?>
            </div>
        </div>
    </div>
</section>
<?php if (!is_page('sluzby')) : ?>
    <section class="mx-auto mt-12 w-full max-w-[1440px] px-5 lg:mt-0 lg:pl-4 lg:pr-[120px]">
        <div class="ml-auto flex w-full max-w-[1000px] flex-col items-center gap-10 lg:flex-row">
            <div class="flex w-full max-w-[550px] items-center gap-4">
                <div class="hidden space-y-[5px] lg:block">
                    <div class="aspect-square w-[5px] rounded-full bg-[#d9d9d9]"></div>
                    <div class="aspect-square w-[5px] rounded-full bg-theme-pink"></div>
                    <div class="aspect-square w-[5px] rounded-full bg-theme-pink"></div>
                    <div class="aspect-square w-[5px] rounded-full bg-[#d9d9d9]"></div>
                    <div class="aspect-square w-[5px] rounded-full bg-[#d9d9d9]"></div>
                    <div class="aspect-square w-[5px] rounded-full bg-[#d9d9d9]"></div>
                    <div class="aspect-square w-[5px] rounded-full bg-[#d9d9d9]"></div>
                    <div class="aspect-square w-[5px] rounded-full bg-[#d9d9d9]"></div>
                </div>
                <div class="ml-auto w-full text-center lg:max-w-[500px] lg:text-left">
                    <h4 class="mb-3 text-2xl font-bold text-theme-purple lg:mb-0 lg:text-sm lg:leading-6">
                        <?= $left_to_button_title ?? '' ?>
                    </h4>
                    <p class="text-xs sm:text-base">
                        <?= $left_to_button_description ?? '' ?>
                    </p>
                </div>
            </div>
            <div class="relative -mt-1 w-full max-w-[350px] lg:max-w-[416px]">
                <svg class="pointer-events-none -mt-5 hidden max-w-[350px] lg:mt-0 lg:block lg:max-w-[416px]" xmlns="http://www.w3.org/2000/svg" width="416" height="172" viewBox="0 0 416 172" fill="none">
                    <path fill="#18012D" d="M.752.1 415.86.734l-4.185 82.32c-1.201 23.617-18.782 43.163-42.14 46.85l-256.832 40.541c-22.833 3.604-45.16-8.913-53.998-30.273L.752.101Z" />
                </svg>
                <div class="top-[20%] left-1/2 mx-auto w-full max-w-[264px] lg:absolute lg:-translate-x-1/2">
                    <a href="<?= esc_url(home_url('/kontakt/')) ?>" class="mx-auto btn-primary lg:mx-0">
                        Kontaktujte nás
                        <svg width="31" height="30" viewBox="0 0 31 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M0.62427 9.90397C-0.20809 10.6158 -0.20809 11.7684 0.62427 12.4801C4.29775 15.6175 7.98389 18.7423 11.6589 21.8797C12.4926 22.5919 13.8436 22.5916 14.6773 21.8797C15.8063 20.9145 16.9347 19.9485 18.0638 18.9833C15.0097 16.3902 11.9824 13.7455 8.90886 11.1824C11.9318 8.58328 14.9693 6.0008 18.0015 3.41241L14.6096 0.532872C13.7776 -0.177723 12.4249 -0.177525 11.5928 0.532872C7.93529 3.65515 4.28228 6.78208 0.62427 9.90397Z" fill="#FEFEFE" />
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M12.6968 11.0164C15.7521 13.6108 18.7846 16.2398 21.8515 18.8175L12.7591 26.5876C13.8898 27.5476 15.0242 28.5061 16.1506 29.4672C16.983 30.1776 18.3353 30.1776 19.1676 29.4672C22.825 26.3447 26.4784 23.2178 30.1363 20.0959C30.9687 19.3842 30.9687 18.2316 30.1363 17.5197C26.4617 14.3823 22.7764 11.2573 19.1017 8.12004C18.2679 7.40834 16.9171 7.40815 16.0834 8.12004C14.9544 9.08533 13.8259 10.0512 12.6968 11.0164Z" fill="#FEFEFE" />
                        </svg>
                    </a>
                </div>
            </div>
        </div>
        <hr class="mt-14 border-t-[#D9D9D9]" />
    </section>
<?php endif ?>