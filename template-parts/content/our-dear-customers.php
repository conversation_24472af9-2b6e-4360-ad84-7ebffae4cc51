<?php

use Um<PERSON>weby\UWTheme\Settings\UW_Sections\UW_Client_Logos_Settings;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;

$title = rwmb_meta(UW_Settings_Page::PREFIX . UW_Client_Logos_Settings::FIELD_TITLE, ['object_type' => 'setting'], UW_Client_Logos_Settings::OPTION_NAME);

$description = rwmb_meta(UW_Settings_Page::PREFIX . UW_Client_Logos_Settings::FIELD_DESCRIPTION, ['object_type' => 'setting'], UW_Client_Logos_Settings::OPTION_NAME);

$group = rwmb_meta(UW_Settings_Page::PREFIX . UW_Client_Logos_Settings::FIELD_LOGO_GROUP, ['object_type' => 'setting'], UW_Client_Logos_Settings::OPTION_NAME);

$group_image = UW_Settings_Page::PREFIX . UW_Client_Logos_Settings::FIELD_LOGO_GROUP_IMAGE;

?>

<!-- :::::::::::: Customer logos :::::::::::: -->
<section class="mx-auto mt-12 w-full max-w-screen-xl px-5 mb-15">
    <div class="mb-16 text-center sm:text-left">

        <?php get_template_part(
            'template-parts/components/section/_section_title',
            null,
            [
                'title' => $title,
                'description' => $description,
            ]
        );
        ?>

    </div>

    <div class="flex flex-wrap justify-center gap-6">
        <?php
        if (!empty($group)) :
            foreach ($group as $logos) :
                $image = $logos[$group_image];
                $logo = reset($image);
        ?>
                <div class="flex items-center justify-center w-[calc(50%-12px)] sm:w-[calc(33.333%-16px)] md:w-[calc(25%-18px)] lg:w-[calc(16.666%-20px)] min-w-[200px] h-[160px] bg-gray-50 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300">
                    <img src="<?= wp_get_attachment_image_url($logo, 'medium') ?>"
                        alt="partner"
                        class="w-auto h-auto max-h-[100px] object-contain">
                </div>
        <?php
            endforeach;
        endif;
        ?>
    </div>
</section>