<?php

use Um<PERSON>weby\UWTheme\CustomPostType\UW_Service_Custom_Post_Type;
use Umimeweby\UWTheme\Settings\UW_Sections\UW_New_Or_Existing_Settings;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;
use Umimeweby\UWTheme\Taxonomies\UW_Service_Custom_Taxonomy;

// Získání odkazů na nové a existující projekty
$new_post_id = rwmb_meta(UW_Settings_Page::PREFIX . UW_New_Or_Existing_Settings::FIELD_LINK_NEW, ['object_type' => 'setting'], UW_New_Or_Existing_Settings::OPTION_NAME);
$new_post_link = get_permalink($new_post_id);

$existing_post_id = rwmb_meta(UW_Settings_Page::PREFIX . UW_New_Or_Existing_Settings::FIELD_LINK_EXISTING, ['object_type' => 'setting'], UW_New_Or_Existing_Settings::OPTION_NAME);
$existing_post_link = get_permalink($existing_post_id);

// Získání kategorií služeb
$service_categories = get_terms([
    'taxonomy' => UW_Service_Custom_Taxonomy::TAXONOMY_KEY,
    'hide_empty' => false,
    'meta_key' => UW_Service_Custom_Taxonomy::TAXONOMY_PREFIX . UW_Service_Custom_Taxonomy::TAXONOMY_FIELD_ORDER,
    'orderby' => 'meta_value_num',
    'order' => 'ASC',
    'number' => 4, // Omezíme na 4 kategorie pro homepage
]);

?>

<section class="w-full px-2.5 md:px-5">
    <div class="relative mx-auto w-full max-w-[1440px] rounded-[50px] bg-gradient-to-br from-theme-purple via-dark-purple to-theme-purple overflow-hidden pb-14 lg:pb-16">
        <!-- Dekorativní pozadí -->
        <div class="absolute inset-0 bg-plants bg-cover bg-no-repeat opacity-20"></div>
        <div class="absolute top-10 left-10 w-24 h-24 bg-theme-pink/10 rounded-full blur-xl"></div>
        <div class="absolute bottom-20 right-16 w-32 h-32 bg-theme-blue/10 rounded-full blur-xl"></div>
        <span class="absolute top-0 left-1/2 z-10 inline-block h-0.5 w-full max-w-[80vw] -translate-x-1/2 bg-bgborder sm:max-w-[50vw] lg:z-50 xl:max-w-[1170px]"></span>
        
        <div class="relative z-10 mx-auto w-full max-w-[1170px] px-4 pt-[65px]">
            
            <!-- Horní sekce - Nové vs Existující projekty -->
            <div class="mx-auto grid w-full max-w-[450px] grid-cols-1 gap-12 text-center md:max-w-full md:gap-8 md:text-left lg:grid-cols-[1fr,max-content,1fr] xl:gap-11 mb-16">
                <div class="flex flex-col items-center gap-4 md:flex-row xl:gap-7">
                    <img src="<?= get_stylesheet_directory_uri() ?>/assets/images/home/<USER>" alt="existing projects" class="blend-screen h-auto w-full max-w-[120px] object-contain xl:w-auto xl:max-w-max" loading="lazy" decoding="async" />
                    <div class="w-full lg:max-w-[300px]">
                        <h2 class="text-2xl font-bold text-white mb-3 md:mt-2.5 md:text-[28px]">
                            Převezmeme a vylepšíme vaše webové řešení
                        </h2>
                        <p class="mb-[22px] text-base font-normal text-white/80">
                            Hledáte technicky zdatného partnera pro správu a rozvoj vašeho webu nebo aplikace? Postaráme se o váš projekt s důrazem na stabilitu a výkon.
                        </p>
                        <a href="<?= esc_url($existing_post_link) ?>" class="mx-auto flex h-[62px] w-full max-w-[222px] items-center justify-center gap-2.5 rounded-[20px] border-2 text-base font-bold text-light-purple shadow-btnwhite sm:border-[#d9d9d9] sm:text-[#D9D9D9] md:mx-0 hover:shadow-btnBlue hover:shadow-theme-blue hover:bg-theme-blue hover:border-theme-blue transition-all">
                            Zjistit více o správě
                            <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" fill="none">
                                <path fill="#FEFEFE" fill-rule="evenodd" d="M.663 6.603c-.555.474-.555 1.243 0 1.717 2.449 2.092 4.906 4.175 7.356 6.266.556.475 1.457.475 2.012 0l2.258-1.93c-2.036-1.73-4.054-3.492-6.103-5.201C8.2 5.722 10.226 4 12.248 2.275L9.986.355c-.554-.473-1.456-.473-2.01 0C5.535 2.437 3.1 4.521.662 6.603Z" clip-rule="evenodd" />
                                <path fill="#FEFEFE" fill-rule="evenodd" d="M8.711 7.344c2.037 1.73 4.059 3.483 6.103 5.201l-6.061 5.18c.753.64 1.51 1.28 2.26 1.92.556.473 1.457.473 2.012 0l7.312-6.248c.555-.474.555-1.243 0-1.717-2.45-2.092-4.906-4.175-7.356-6.267-.556-.474-1.456-.474-2.012 0L8.71 7.344Z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </div>
                </div>
                <span class="hidden h-full w-0.5 border-r border-dashed border-white lg:block"></span>
                <div class="flex flex-col items-center gap-7 md:flex-row">
                    <img src="<?= get_stylesheet_directory_uri() ?>/assets/images/home/<USER>" alt="new projects" class="h-auto w-full max-w-[100px] object-contain md:max-w-[120px] xl:w-auto xl:max-w-max" loading="lazy" decoding="async" />
                    <div class="w-full lg:max-w-[300px]">
                        <h2 class="text-2xl font-bold text-white mb-3 md:mt-2.5 md:text-[28px]">
                            Vytvoříme spolehlivé webové řešení
                        </h2>
                        <p class="mb-[22px] text-base font-normal text-white/80">
                           Potřebujete vyvinout nový web nebo aplikaci? Nabízíme komplexní realizaci od analýzy přes vývoj až po dlouhodobou podporu.
                        </p>
                        <a href="<?= esc_url($new_post_link) ?>" class="mx-auto flex h-[62px] w-full max-w-[222px] items-center justify-center gap-2.5 rounded-[20px] border-2 text-base font-bold text-light-purple shadow-btnwhite sm:border-[#d9d9d9] sm:text-[#D9D9D9] md:mx-0 hover:shadow-btnBlue hover:shadow-theme-blue hover:bg-theme-blue hover:border-theme-blue transition-all">
                            Rozjet nový projekt
                            <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" fill="none">
                                <path fill="currentColor" fill-rule="evenodd" d="M.663 6.603c-.555.474-.555 1.243 0 1.717 2.449 2.092 4.906 4.175 7.356 6.266.556.475 1.457.475 2.012 0l2.258-1.93c-2.036-1.73-4.054-3.492-6.103-5.201C8.2 5.722 10.226 4 12.248 2.275L9.986.355c-.554-.473-1.456-.473-2.01 0C5.535 2.437 3.1 4.521.662 6.603Z" clip-rule="evenodd" />
                                <path fill="currentColor" fill-rule="evenodd" d="M8.711 7.344c2.037 1.73 4.059 3.483 6.103 5.201l-6.061 5.18c.753.64 1.51 1.28 2.26 1.92.556.473 1.457.473 2.012 0l7.312-6.248c.555-.474.555-1.243 0-1.717-2.45-2.092-4.906-4.175-7.356-6.267-.556-.474-1.456-.474-2.012 0L8.71 7.344Z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>

            <span class="inline-block h-0.5 w-full bg-bgborderTwo mb-12"></span>

            <!-- Nadpis sekce služeb -->
            <div class="text-center mb-12">
                <h2 class="heading-tertiary sm:heading-secondary mb-4 text-white">
                    Komplexní technologické služby
                </h2>
                <p class="text-center text-base font-normal text-white/80 max-w-2xl mx-auto">
                    Od správy jednoduchých webů po vývoj složitých aplikací. Podívejte se na všechny oblasti, ve kterých můžeme pomoct vašemu byznysu.
                </p>
            </div>

            <!-- Grid kategorií služeb -->
            <?php if (!empty($service_categories) && !is_wp_error($service_categories)) : ?>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
                    <?php foreach ($service_categories as $category) : ?>
                        <?php
                        // Dynamické přiřazení ikon podle názvu kategorie
                        $category_name = strtolower($category->name);
                        $icon_html = '';
                        $category_link = get_term_link($category);
                        
                        if (strpos($category_name, 'vývoj') !== false || strpos($category_name, 'aplikace') !== false || strpos($category_name, 'software') !== false) {
                            $icon_html = '<svg class="w-8 h-8 text-theme-purple" viewBox="0 0 24 24" fill="none">
                                <path d="M13 3L4 14h7l-2 7 9-11h-7l2-7z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>';
                        } elseif (strpos($category_name, 'správa') !== false || strpos($category_name, 'údržba') !== false || strpos($category_name, 'podpora') !== false) {
                            $icon_html = '<svg class="w-8 h-8 text-theme-purple" viewBox="0 0 24 24" fill="none">
                                <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>';
                        } elseif (strpos($category_name, 'marketing') !== false || strpos($category_name, 'reklama') !== false || strpos($category_name, 'seo') !== false) {
                            $icon_html = '<svg class="w-8 h-8 text-theme-purple" viewBox="0 0 24 24" fill="none">
                                <path d="M3 11l18-5v12L3 14v-3z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M11.6 16.8a3 3 0 1 1-5.8-1.6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>';
                        } elseif (strpos($category_name, 'konzultace') !== false || strpos($category_name, 'poradenství') !== false || strpos($category_name, 'analýza') !== false) {
                            $icon_html = '<svg class="w-8 h-8 text-theme-purple" viewBox="0 0 24 24" fill="none">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 9h8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 13h6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>';
                        } else {
                            $icon_html = '<svg class="w-8 h-8 text-theme-purple" viewBox="0 0 24 24" fill="none">
                                <path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M2 17l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>';
                        }
                        ?>

                        <div class="relative bg-white/95 backdrop-blur-sm rounded-3xl p-6 lg:p-8 shadow-2xl hover:shadow-3xl transition-all duration-500 ring-2 ring-white/20 hover:ring-theme-pink/40 hover:ring-offset-2 hover:ring-offset-theme-pink/10 group hover:bg-white hover:-translate-y-2 min-h-[350px] flex flex-col">
                            <!-- Header s ikonou a názvem -->
                            <div class="flex items-center gap-4 mb-6">
                                <div class="w-12 h-12 lg:w-14 lg:h-14 rounded-2xl bg-gradient-to-br from-theme-purple/10 to-theme-blue/10 flex items-center justify-center flex-shrink-0 group-hover:from-theme-purple/20 group-hover:to-theme-blue/20 transition-all duration-300 group-hover:scale-110">
                                    <?php echo $icon_html; ?>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl lg:text-2xl font-bold text-theme-purple leading-tight group-hover:text-theme-blue transition-colors">
                                        <?php echo $category->name; ?>
                                    </h3>
                                </div>
                            </div>

                            <!-- Popis kategorie -->
                            <div class="mb-6">
                                <p class="text-gray-600 text-sm lg:text-base leading-relaxed">
                                    <?php echo $category->description; ?>
                                </p>
                            </div>

                            <!-- Seznam služeb - dynamicky generovaný -->
                            <?php
                            $services_in_category = get_posts([
                                'post_type' => UW_Service_Custom_Post_Type::CPT_KEY,
                                'posts_per_page' => 3, // Omezíme na 3 služby pro homepage
                                'tax_query' => [
                                    [
                                        'taxonomy' => UW_Service_Custom_Taxonomy::TAXONOMY_KEY,
                                        'field'    => 'term_id',
                                        'terms'    => $category->term_id,
                                    ],
                                ],
                                'post_status' => 'publish',
                            ]);
                            ?>
                            
                            <?php if (!empty($services_in_category)) : ?>
                            <div class="mb-6 flex-grow">
                                <ul class="space-y-2 text-sm lg:text-base">
                                    <?php foreach ($services_in_category as $service) : ?>
                                        <li>
                                            <a href="<?php echo get_permalink($service->ID); ?>" class="flex items-center gap-3 text-gray-900 hover:text-theme-pink transition-colors group/link">
                                                <svg class="w-4 h-4 text-theme-pink group-hover/link:scale-110 transition-transform flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" />
                                                </svg>
                                                <span class="font-medium"><?php echo get_the_title($service->ID); ?></span>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <?php endif; ?>

                            <!-- CTA odkaz -->
                            <div class="mt-auto">
                                <a href="<?php echo esc_url($category_link); ?>" class="inline-flex items-center text-theme-pink hover:text-theme-purple transition-colors font-medium">
                                    Zjistit více
                                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                                    </svg>
                                </a>
                            </div>
                        </div>

                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <!-- CTA sekce -->
            <div class="mt-16 text-center">
                <a href="<?= esc_url(home_url('/sluzby/')) ?>" class="btn-primary border-2 border-light-purple bg-transparent bg-purple-gradient text-light-purple shadow-btnwhite hover:bg-white hover:text-theme-purple transition-all">
                    Všechny naše služby
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM6.4 11.6V4.4L11.2 8L6.4 11.6Z" fill="currentColor" />
                    </svg>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Kontaktní sekce -->
<section class="mx-auto mt-12 w-full max-w-[1440px] px-5 lg:mt-0 lg:pl-4 lg:pr-[120px]">
    <div class="ml-auto flex w-full max-w-[1000px] flex-col items-center gap-10 lg:flex-row">
        <div class="flex w-full max-w-[550px] items-center gap-4">
            <div class="hidden space-y-[5px] lg:block">
                <div class="aspect-square w-[5px] rounded-full bg-[#d9d9d9]"></div>
                <div class="aspect-square w-[5px] rounded-full bg-theme-pink"></div>
                <div class="aspect-square w-[5px] rounded-full bg-theme-pink"></div>
                <div class="aspect-square w-[5px] rounded-full bg-[#d9d9d9]"></div>
                <div class="aspect-square w-[5px] rounded-full bg-[#d9d9d9]"></div>
                <div class="aspect-square w-[5px] rounded-full bg-[#d9d9d9]"></div>
                <div class="aspect-square w-[5px] rounded-full bg-[#d9d9d9]"></div>
                <div class="aspect-square w-[5px] rounded-full bg-[#d9d9d9]"></div>
            </div>
            <div class="ml-auto w-full text-center lg:max-w-[500px] lg:text-left">
                <h4 class="mb-3 text-2xl font-bold text-theme-purple lg:mb-0 lg:text-sm lg:leading-6">
                    Pojďme probrat váš projekt.
                </h4>
                <p class="text-xs sm:text-base">
                    Domluvte si nezávaznou konzultaci. Probereme vaše potřeby a navrhneme optimální řešení pro váš web nebo aplikaci.
            </div>
        </div>
        <div class="relative -mt-1 w-full max-w-[350px] lg:max-w-[416px]">
            <svg class="pointer-events-none -mt-5 hidden max-w-[350px] lg:mt-0 lg:block lg:max-w-[416px]" xmlns="http://www.w3.org/2000/svg" width="416" height="172" viewBox="0 0 416 172" fill="none">
                <path fill="#18012D" d="M.752.1 415.86.734l-4.185 82.32c-1.201 23.617-18.782 43.163-42.14 46.85l-256.832 40.541c-22.833 3.604-45.16-8.913-53.998-30.273L.752.101Z" />
            </svg>
            <div class="top-[20%] left-1/2 mx-auto w-full max-w-[264px] lg:absolute lg:-translate-x-1/2">
                <a href="<?= esc_url(home_url('/kontakt/')) ?>" class="mx-auto btn-primary lg:mx-0">
                    Kontaktujte nás
                    <svg width="31" height="30" viewBox="0 0 31 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.62427 9.90397C-0.20809 10.6158 -0.20809 11.7684 0.62427 12.4801C4.29775 15.6175 7.98389 18.7423 11.6589 21.8797C12.4926 22.5919 13.8436 22.5916 14.6773 21.8797C15.8063 20.9145 16.9347 19.9485 18.0638 18.9833C15.0097 16.3902 11.9824 13.7455 8.90886 11.1824C11.9318 8.58328 14.9693 6.0008 18.0015 3.41241L14.6096 0.532872C13.7776 -0.177723 12.4249 -0.177525 11.5928 0.532872C7.93529 3.65515 4.28228 6.78208 0.62427 9.90397Z" fill="#FEFEFE" />
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M12.6968 11.0164C15.7521 13.6108 18.7846 16.2398 21.8515 18.8175L12.7591 26.5876C13.8898 27.5476 15.0242 28.5061 16.1506 29.4672C16.983 30.1776 18.3353 30.1776 19.1676 29.4672C22.825 26.3447 26.4784 23.2178 30.1363 20.0959C30.9687 19.3842 30.9687 18.2316 30.1363 17.5197C26.4617 14.3823 22.7764 11.2573 19.1017 8.12004C18.2679 7.40834 16.9171 7.40815 16.0834 8.12004C14.9544 9.08533 13.8259 10.0512 12.6968 11.0164Z" fill="#FEFEFE" />
                    </svg>
                </a>
            </div>
        </div>
    </div>
    <hr class="mt-14 border-t-[#D9D9D9]" />
</section>
