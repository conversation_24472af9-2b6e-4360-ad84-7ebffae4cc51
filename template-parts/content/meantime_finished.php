<?php

use Umimeweby\UWTheme\CustomFields\Meantime_Finished_Custom_Fields;

$title = rwmb_get_value(Meantime_Finished_Custom_Fields::PREFIX . Meantime_Finished_Custom_Fields::MEANTIME_FINISHED_CUSTOM_FIELDS_TITLE);
$description = rwmb_get_value(Meantime_Finished_Custom_Fields::PREFIX . Meantime_Finished_Custom_Fields::MEANTIME_FINISHED_CUSTOM_FIELDS_DESCRIPTION);
$group = rwmb_meta(Meantime_Finished_Custom_Fields::PREFIX . Meantime_Finished_Custom_Fields::MEANTIME_FINISHED_CUSTOM_FIELDS_GROUP);
$image = Meantime_Finished_Custom_Fields::PREFIX . Meantime_Finished_Custom_Fields::MEANTIME_FINISHED_CUSTOM_FIELDS_GROUP_IMAGE;
$btn_text = rwmb_get_value(Meantime_Finished_Custom_Fields::PREFIX . Meantime_Finished_Custom_Fields::MEANTIME_FINISHED_CUSTOM_FIELDS_BTN_TEXT);
$btn_link = rwmb_get_value(Meantime_Finished_Custom_Fields::PREFIX . Meantime_Finished_Custom_Fields::MEANTIME_FINISHED_CUSTOM_FIELDS_BTN_LINK);

?>

<section class="mx-auto w-full max-w-[1330px] md:mb-44 lg:px-5">
    <div class="mx-auto w-full max-w-[1160px] px-5 text-center sm:mb-16 sm:text-left lg:px-0">
        <h2 class="heading-tertiary sm:heading-secondary mb-1 !text-theme-purple">
            <?= $title ?? '' ?>
        </h2>
        <p class="text-xs uppercase sm:text-base">
            <?= $description ?? '' ?>
        </p>
    </div>
    <!-- ::::::::::::mezitim slider:::::::::::: -->
    <div class="w-full overflow-hidden p-12 pb-40 sm:mb-[76px] sm:p-12 sm:pb-40 xl:-m-12">
        <div class="relative overflow-visible swiper mezitim-slider">
            <?php if (!empty($group)) : ?>
                <div class="swiper-wrapper <?= (count($group) < 5) ? 'flex justify-center' : '' ?>">
                    <?php foreach ($group as $group_images) : ?>
                        <?php
                        $images = $group_images[$image];
                        $one_image = reset($images)
                        ?>
                        <div class="swiper-slide rounded-[10px] bg-white p-2 shadow-btnwhite md:rounded-[20px]">
                            <img src="<?= wp_get_attachment_image_url($one_image, 'large') ?>"
                                 alt="mezitim"
                                 class="h-auto w-full rounded-[20px] object-cover"
                                 loading="lazy"
                                 decoding="async"/>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif ?>
            <div
                    class="absolute top-[72%] left-0 right-0 z-50 h-[467px] w-[calc(100%+400px)] -translate-x-[200px] rounded-[50%] bg-white">
                <div class="mezitim-dots mt-[60px] flex items-center justify-center gap-3.5"></div>
                <a href="<?= trailingslashit(esc_url(get_page_link($btn_link ?? ''))) ?>"
                   class="mx-auto mt-10 btn-primary">
                    <?= $btn_text ?? '' ?>
                    <svg width="31"
                         height="30"
                         viewBox="0 0 31 30"
                         fill="none"
                         xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M0.62427 9.90397C-0.20809 10.6158 -0.20809 11.7684 0.62427 12.4801C4.29775 15.6175 7.98389 18.7423 11.6589 21.8797C12.4926 22.5919 13.8436 22.5916 14.6773 21.8797C15.8063 20.9145 16.9347 19.9485 18.0638 18.9833C15.0097 16.3902 11.9824 13.7455 8.90886 11.1824C11.9318 8.58328 14.9693 6.0008 18.0015 3.41241L14.6096 0.532872C13.7776 -0.177723 12.4249 -0.177525 11.5928 0.532872C7.93529 3.65515 4.28228 6.78208 0.62427 9.90397Z"
                              fill="#FEFEFE"/>
                        <path fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M12.6968 11.0164C15.7521 13.6108 18.7846 16.2398 21.8515 18.8175L12.7591 26.5876C13.8898 27.5476 15.0242 28.5061 16.1507 29.4672C16.983 30.1776 18.3353 30.1776 19.1677 29.4672C22.825 26.3447 26.4784 23.2178 30.1363 20.0959C30.9687 19.3842 30.9687 18.2316 30.1363 17.5197C26.4617 14.3823 22.7764 11.2573 19.1017 8.12004C18.2679 7.40834 16.9171 7.40815 16.0834 8.12004C14.9544 9.08533 13.8259 10.0512 12.6968 11.0164Z"
                              fill="#FEFEFE"/>
                    </svg>
                </a>
            </div>
        </div>
    </div>
</section>
