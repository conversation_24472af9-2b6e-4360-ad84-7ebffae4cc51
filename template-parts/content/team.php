<?php

use Umimeweby\UWTheme\CustomFields\AboutUs\Team_Custom_Fields;

$title = rwmb_get_value(Team_Custom_Fields::PREFIX . Team_Custom_Fields::TEAM_FIELD_TITLE);
$description = rwmb_get_value(Team_Custom_Fields::PREFIX . Team_Custom_Fields::TEAM_FIELD_DESCRIPTION);
$group = rwmb_meta(Team_Custom_Fields::PREFIX . Team_Custom_Fields::TEAM_FIELD_GROUP);
$image = Team_Custom_Fields::PREFIX . Team_Custom_Fields::TEAM_FIELDS_GROUP_IMAGE;
$name_and_surname = Team_Custom_Fields::PREFIX . Team_Custom_Fields::TEAM_GROUP_NAME_AND_SURNAME;
$position = Team_Custom_Fields::PREFIX . Team_Custom_Fields::TEAM_GROUP_POSITION;
$description_group = Team_Custom_Fields::PREFIX . Team_Custom_Fields::TEAM_GROUP_DESCRIPTION;

?>
<section class="uw-team relative mx-auto mb-[78px] w-full max-w-[1170px]">
    <div class="mb-[31px] lg:mb-[100px]">
        <h4 class="text-3xl font-bold leading-normal text-theme-purple">
            <?= $title ?? '' ?>
        </h4>
        <p class="mt-[22px] text-sm font-normal leading-5">
           <?= $description ?? '' ?>
        </p>
    </div>
    <div class="mb-5 grid w-full gap-y-10 gap-x-6 lg:grid-cols-2 lg:gap-y-14">
        <?php foreach ($group as $index => $one_member) : ?>
        <div class="grid w-full items-center gap-6 sm:grid-cols-[minmax(160px,240px),1fr] lg:-mt-12 <?= $index >= 4 ? 'hidden' : '' ?>">
            <?php if(!empty($one_member[$image])) : ?>
            <div class="mx-auto h-[240px] max-w-[240px] rounded-full border-[20px] border-l-[20px] border-transparent border-l-[#eaeaea] border-t-[#eaeaea] sm:mx-0">
                <?php
                    $images = $one_member[$image];
                    $photo = reset($images)
                ?>
                <img src="<?= wp_get_attachment_image_url($photo, 'large') ?>" alt="girl" class="rounded-full p-5" loading="lazy" decoding="async"/>
            </div>
            <?php endif; ?>
            <div class="relative z-20 hidden text-center sm:block sm:text-left">
                <h2 class="text-base font-bold leading-7 text-theme-purple sm:text-xl">
                   <?= $one_member[$name_and_surname] ?? '' ?>
                </h2>
                <p class="mb-6 text-xs font-normal leading-6 sm:text-base">
                    <?= $one_member[$position] ?? '' ?>
                </p>
                <i class="text-sm font-normal leading-5 text-theme-purple sm:text-base"><?= $one_member[$description_group] ?? '' ?></i>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    <button class="team-btn relative z-40 mx-auto flex w-full max-w-[200px] items-center justify-center gap-1.5 rounded-[10px] border-2 border-dark-purple p-2.5 text-base font-bold leading-loose text-dark-purple transition-all duration-200 hover:bg-dark-purple hover:text-white lg:gap-2.5">
        <span>Zobrazit další</span>
        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" fill="none">
            <path fill="#8078A0" fill-rule="evenodd"
                  d="M.416 6.603c-.555.474-.555 1.243 0 1.717 2.45 2.092 4.907 4.175 7.357 6.266.555.475 1.456.475 2.012 0l2.258-1.93c-2.036-1.73-4.055-3.492-6.104-5.201C7.955 5.722 9.98 4 12.001 2.275L9.74.355c-.555-.473-1.457-.473-2.011 0C5.29 2.437 2.855 4.521.416 6.603Z"
                  clip-rule="evenodd" />
            <path fill="#8078A0" fill-rule="evenodd"
                  d="M8.465 7.344c2.036 1.73 4.058 3.483 6.103 5.201l-6.062 5.18c.754.64 1.51 1.28 2.261 1.92.555.473 1.457.473 2.011 0 2.439-2.082 4.874-4.166 7.313-6.248.555-.474.555-1.243 0-1.717-2.45-2.092-4.907-4.175-7.357-6.267-.555-.474-1.456-.474-2.012 0L8.465 7.344Z"
                  clip-rule="evenodd" />
        </svg>
    </button>
    <span class="team-cover absolute bottom-0 left-0 right-0 z-30 block h-96 w-full bg-box bg-cover bg-no-repeat"></span>
</section>