<?php

use Um<PERSON>weby\UWTheme\CustomPostType\Reference_Custom_Post_Type;
use Umimeweby\UWTheme\Taxonomies\Reference_Custom_Taxonomy;
use Umimeweby\UWTheme\CustomFields\Reference\Single\Single_Reference_Custom_Fields;
use Umimeweby\UWTheme\Settings\UW_Sections\UW_References_By_Category_Settings;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;

$meta_key_taxonomy_order = Reference_Custom_Taxonomy::PREFIX . Reference_Custom_Taxonomy::TAXONONY_FIELD_PORADI;
$post_type = Reference_Custom_Post_Type::CPT_KEY;

$reference_title = rwmb_meta(UW_Settings_Page::PREFIX . UW_References_By_Category_Settings::FIELD_TITLE, ['object_type' => 'setting'], UW_References_By_Category_Settings::OPTION_NAME);

$reference_description = rwmb_meta(UW_Settings_Page::PREFIX . UW_References_By_Category_Settings::FIELD_DESCRIPTION, ['object_type' => 'setting'], UW_References_By_Category_Settings::OPTION_NAME);
?>

<section class="mx-auto mb-14 w-full max-w-screen-2xl border-t border-[#d9d9d9] pt-10 sm:mb-[100px] md:pt-[70px]">
    <div class="mx-auto w-full max-w-screen-xl">
        <div class="md:px-10">
            <?php get_template_part(
                'template-parts/components/section/_section_title',
                null,
                [
                    'title' => $reference_title,
                    'description' => $reference_description,
                ]
            );
            ?>
        </div>
        <!-- cards -->
        <div class="relative transition-all duration-300 card-container">
            <?php
            $args = array(
                'post_type' => $post_type,
                'post_status' => 'publish',
                'posts_per_page' => -1,
                'tax_query' => array(
                    array(
                        'taxonomy' => 'post_tag',
                        'field' => 'slug',
                        'terms' => $args['tag']
                    ),
                ),
            );

            $query = new WP_Query($args);

            ?>
            <div class="animation">
                <div class="relative mb-10 h-auto w-full overflow-auto pt-[50px] sm:mb-[70px]">
                    <div class="grid grid-cols-[repeat(4,240px)] gap-5 pb-10 md:w-full md:grid-cols-2 md:pb-10 lg:grid-cols-3 xl:gap-[50px] xl:px-10">
                        <?php while ($query->have_posts()) :
                            $query->the_post();
                            $post_id = get_the_ID();

                            $title = get_post_meta($post_id, Reference_Custom_Post_Type::PREFIX . Reference_Custom_Post_Type::REFERENCE_CARDS_FIELD_TITLE, true);
                            $description = get_post_meta($post_id, Reference_Custom_Post_Type::PREFIX . Reference_Custom_Post_Type::REFERENCE_CARDS_FIELD_DESCRIPTION, true);
                            $btn_text = get_post_meta($post_id, Reference_Custom_Post_Type::PREFIX . Reference_Custom_Post_Type::REFERENCE_CARDS_FIELD_BTN_TEXT, true);
                            $reference_single_description = get_post_meta($post_id, Single_Reference_Custom_Fields::PREFIX . Single_Reference_Custom_Fields::SINGLE_REFERENCE_FIELD_DESCRIPTION, true);

                        ?>
                            <div class="w-full rounded-[20px] bg-white p-4 shadow-form">
                                <span class="relative group">
                                    <img src="<?= get_the_post_thumbnail_url($post_id, 'full'); ?>" alt="" class="block object-cover w-full h-auto rounded-lg" />
                                    <div class="invisible absolute top-0 left-0 right-0 bottom-0 flex h-full max-h-0 w-full origin-top flex-col items-center justify-center rounded-lg bg-theme-purple py-3 px-5 opacity-0 transition-all duration-300 group-hover:visible group-hover:max-h-full group-hover:opacity-100 xl:py-11 xl:px-[50px]">
                                        <h3 class="text-sm font-bold leading-5 text-center text-white md:text-left md:text-xl md:leading-7">
                                            <?= $title ?? '' ?>
                                        </h3>
                                        <p class="mt-2 text-center text-xs font-normal text-light-purple md:mt-[19px] md:text-left md:text-sm">
                                            <?= $description ?? '' ?>
                                        </p>
                                        <?php if (!empty($reference_single_description)) { ?>
                                            <button class="btn-primary mt-2.5 h-10 rounded-full px-2 py-2 text-xs md:mt-0 md:h-[50px] md:text-base">
                                                <?= $btn_text ?? '' ?>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" fill="none">
                                                    <path fill="#FEFEFE" fill-rule="evenodd" d="M.663 6.603c-.555.474-.555 1.243 0 1.717 2.449 2.092 4.906 4.175 7.356 6.266.556.475 1.457.475 2.012 0l2.258-1.93c-2.036-1.73-4.054-3.492-6.103-5.201C8.2 5.722 10.226 4 12.248 2.275L9.986.355c-.554-.473-1.456-.473-2.01 0C5.535 2.437 3.1 4.521.662 6.603Z" clip-rule="evenodd" />
                                                    <path fill="#FEFEFE" fill-rule="evenodd" d="M8.711 7.344c2.037 1.73 4.059 3.483 6.103 5.201l-6.061 5.18c.753.64 1.51 1.28 2.26 1.92.556.473 1.457.473 2.012 0l7.312-6.248c.555-.474.555-1.243 0-1.717-2.45-2.092-4.906-4.175-7.356-6.267-.556-.474-1.456-.474-2.012 0L8.71 7.344Z" clip-rule="evenodd" />
                                                </svg>
                                            </button>
                                        <?php } ?>
                                    </div>
                                </span>
                            </div>
                        <?php
                        endwhile;
                        wp_reset_postdata();
                        ?>
                    </div>
                </div>
            </div>
            <span class="card-overlay"></span>
        </div>
        <?php if ($query->found_posts > 6): ?>
            <button class="cardViewBtn mx-auto mt-10 flex w-full max-w-[200px] items-center justify-center gap-1.5 rounded-[10px] border-2 border-light-purple p-2.5 text-base font-bold leading-loose text-light-purple transition-all duration-200 hover:bg-light-purple hover:text-white lg:gap-2.5">
                <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" fill="none">
                    <path fill="currentColor" fill-rule="evenodd" d="M.416 6.603c-.555.474-.555 1.243 0 1.717 2.45 2.092 4.907 4.175 7.357 6.266.555.475 1.456.475 2.012 0l2.258-1.93c-2.036-1.73-4.055-3.492-6.104-5.201C7.955 5.722 9.98 4 12.001 2.275L9.74.355c-.555-.473-1.457-.473-2.011 0C5.29 2.437 2.855 4.521.416 6.603Z" clip-rule="evenodd" />
                    <path fill="currentColor" fill-rule="evenodd" d="M8.465 7.344c2.036 1.73 4.058 3.483 6.103 5.201l-6.062 5.18c.754.64 1.51 1.28 2.261 1.92.555.473 1.457.473 2.011 0 2.439-2.082 4.874-4.166 7.313-6.248.555-.474.555-1.243 0-1.717-2.45-2.092-4.907-4.175-7.357-6.267-.555-.474-1.456-.474-2.012 0L8.465 7.344Z" clip-rule="evenodd" />
                </svg>
                <span>Zobrazit další</span>
            </button>
        <?php endif; ?>
    </div>
</section>