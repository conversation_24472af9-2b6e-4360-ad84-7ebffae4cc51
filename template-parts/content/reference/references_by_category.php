<?php

use Um<PERSON>weby\UWTheme\CustomPostType\Reference_Custom_Post_Type;
use Umimeweby\UWTheme\Taxonomies\Reference_Custom_Taxonomy;
use Umimeweby\UWTheme\Settings\UW_Sections\UW_References_By_Category_Settings;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;

$reference_post_type = Reference_Custom_Post_Type::CPT_KEY;
$type_reference = Reference_Custom_Taxonomy::TAXONOMY_CATEGORY;
$meta_key_taxonomy_order = Reference_Custom_Taxonomy::PREFIX . Reference_Custom_Taxonomy::TAXONONY_FIELD_PORADI;


$reference_title = rwmb_meta(UW_Settings_Page::PREFIX . UW_References_By_Category_Settings::FIELD_TITLE, ['object_type' => 'setting'], UW_References_By_Category_Settings::OPTION_NAME);

$reference_description = rwmb_meta(UW_Settings_Page::PREFIX . UW_References_By_Category_Settings::FIELD_DESCRIPTION, ['object_type' => 'setting'], UW_References_By_Category_Settings::OPTION_NAME);

$all_categories = get_categories([
    'taxonomy' => $type_reference,
    'hide_empty' => false,
    'parent' => 0,
    'meta_key' => $meta_key_taxonomy_order,
    'orderby' => 'meta_value_num',
    'order' => 'ASC',
]);

$total_count = 0;
$existing_categories = [];

foreach ($all_categories as $category) {
    $total_count += $category->count;
    $existing_categories[] = $category;
}

array_unshift($all_categories, (object)[
    'term_id' => 0,
    'name' => 'Vše',
    'count' => $total_count,
]);

?>
<section class="mx-auto mb-14 w-full max-w-screen-2xl border-t border-[#d9d9d9] pt-10 sm:mb-[100px] md:pt-[70px]">
    <div class="mx-auto w-full max-w-screen-xl">
        <div class="px-4 md:px-10">
            <div class="mb-6">
                <?php get_template_part(
                    'template-parts/components/section/_section_title',
                    null,
                    [
                        'title' => $reference_title,
                        'description' => $reference_description,
                    ]
                );
                ?>
            </div>
            <!-- list of categories to filter, i.e. tabs -->
            <div class="flex flex-row flex-wrap items-center gap-2.5 pb-3 md:w-auto md:pb-0 whitespace-nowrap">
                <?php foreach ($all_categories as $index => $category) :
                    if ($category->count > 0) : ?>
                        <div class="w-auto">
                            <button data-term-id="<?= $category->term_id ?>" onclick="tabOpen(event,'tab-<?= $category->term_id ?>')" class="tab-button <?= $index === 0 ? 'shadowTabActive' : '' ?> flex-shrink-0 w-fit items-center gap-2.5 rounded-full border border-dashed border-light-purple py-2.5 px-4 text-base font-normal leading-none flex">
                                <?= $category->name; ?>
                                <span class="flex h-[30px] items-center justify-center rounded-[18px] bg-theme-pink px-2.5 text-xs font-normal leading-4 text-white">
                                    <?= $category->count ?>
                                </span>
                            </button>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
            <!-- list of categories to filter, i.e. tabs END-->
        </div>
        

        <!-- cards -->
        <div class="relative transition-all duration-300 card-container">

            <?php foreach ($all_categories as $index => $category) : ?>
                <?php
                $existing_categories[] = $category->term_id;

                $tax_query = [
                    [
                        'taxonomy' => $type_reference,
                        'field' => 'term_id',
                        'terms' => $category->term_id,
                    ],
                ];
                if ($index > 0) {
                    $args = [
                        'post_type' => $reference_post_type,
                        'post_status' => 'publish',
                        'tax_query' => $tax_query,
                    ];
                } else {
                    $args = [
                        'post_type' => $reference_post_type,
                        'post_status' => 'publish',
                        'posts_per_page' => -1,
                    ];
                }
                $query = new WP_Query($args);

                ?>
                <div class="tabcontent animation <?= $index === 0 ? 'block' : 'none' ?>" id="tab-<?= $category->term_id ?>">
                    <div class="relative mb-10 h-auto w-full overflow-auto pt-[50px] sm:mb-[70px]">
                        <div class="flex flex-wrap gap-5 pb-10 md:w-full md:pb-10 xl:gap-[50px] xl:px-10">
                            <?php while ($query->have_posts()) :
                                $query->the_post();
                                $post_id = get_the_ID();
                                get_template_part(
                                    'template-parts/components/reference/_card_reference_single_for_list',
                                    null,
                                    [
                                        'reference_id' => $post_id,
                                    ]
                                );

                            endwhile;
                            wp_reset_postdata();
                            ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
            <span class="card-overlay"></span>
        </div>
        <!-- cards END-->


        <button class="cardViewBtn mx-auto mt-10 flex w-full max-w-[200px] items-center justify-center gap-1.5 rounded-[10px] border-2 border-dark-purple p-2.5 text-base font-bold leading-loose text-dark-purple transition-all duration-200 hover:bg-dark-purple hover:text-white lg:gap-2.5">
            <?php get_template_part('template-parts/icon/uw-brackets'); ?>
            <span>Zobrazit další</span>
        </button>
    </div>
</section>
<script src="<?php echo get_template_directory_uri() ?>/js/tab_open_assign.js"></script>
