<?php

use Umimeweby\UWTheme\CustomPostType\Reference_Custom_Post_Type;

$reference_post_type = Reference_Custom_Post_Type::CPT_KEY;

// Získání parametrů z shortcode
$type = $attributes['type'] ?? 'ids';
$reference_ids = $attributes['ids'] ?? '';
$tagname = $attributes['tagname'] ?? '';
$limit = intval($attributes['limit'] ?? -1);
$reference_title = $attributes['title'] ?? '';
$reference_description = $attributes['description'] ?? '';

// Příprava WP_Query podle typu
$args = [
    'post_type' => $reference_post_type,
    'post_status' => 'publish',
];

if ($type === 'ids') {
    // Filtrování podle ID
    $ids_array = [];
    if (!empty($reference_ids)) {
        $ids_array = array_map('trim', explode(',', $reference_ids));
        $ids_array = array_filter($ids_array, 'is_numeric');
        $ids_array = array_map('intval', $ids_array);
    }
    
    // Pokud nejsou zadána žádná ID, nezobrazujeme nic
    if (empty($ids_array)) {
        return;
    }
    
    $args['post__in'] = $ids_array;
    $args['orderby'] = 'post__in'; // Zachová pořadí podle zadaných ID
    $args['posts_per_page'] = -1;
    
} elseif ($type === 'tag') {
    // Filtrování podle tagu
    if (empty($tagname)) {
        return;
    }
    
    $args['posts_per_page'] = $limit;
    $args['tax_query'] = [
        [
            'taxonomy' => 'post_tag',
            'field' => 'slug',
            'terms' => $tagname,
        ],
    ];
    
} else {
    // Neplatný typ
    return;
}

$query = new WP_Query($args);

// Pokud nejsou nalezeny žádné reference, nezobrazujeme nic
if (!$query->have_posts()) {
    wp_reset_postdata();
    return;
}

// Počet nalezených referencí pro dynamické CSS třídy
$references_count = $query->found_posts;

// Dynamické CSS třídy podle počtu referencí
// Všechny karty mají stejnou šířku jako při 3 sloupcích, ale centrují se podle počtu
$card_css_classes = 'w-11/12 mx-auto md:mx-0 md:w-[320px]'; // Pevná šířka pro konzistenci

// Základní CSS třídy - vždy vycentrované
$container_css_classes = 'flex justify-center';

// Přidání flex-wrap a gap pro více než 1 referenci
if ($references_count > 1) {
    $container_css_classes .= ' flex-wrap gap-5';
}

?>
<section class="mx-auto mb-14 w-full max-w-screen-2xl border-t border-[#d9d9d9] pt-10 sm:mb-[100px] md:pt-[70px]">
    <div class="mx-auto w-full max-w-screen-xl">
        <div class="px-4 md:px-10">
            <?php if (!empty($reference_title) || !empty($reference_description)) : ?>
                <div class="mb-6">
                    <?php get_template_part(
                        'template-parts/components/section/_section_title',
                        null,
                        [
                            'title' => $reference_title,
                            'description' => $reference_description,
                        ]
                    );
                    ?>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- cards -->
        <div class="relative">
            <div class="relative mb-10 h-auto w-full overflow-auto pt-[50px] sm:mb-[70px]">
                <div class="<?= $container_css_classes ?> pb-10 md:w-full md:pb-10 xl:gap-[50px] xl:px-10">
                    <?php while ($query->have_posts()) :
                        $query->the_post();
                        $post_id = get_the_ID();
                        get_template_part(
                            'template-parts/components/reference/_card_reference_single_for_list',
                            null,
                            [
                                'reference_id' => $post_id,
                                'card_css_classes' => $card_css_classes,
                            ]
                        );
                    endwhile;
                    wp_reset_postdata();
                    ?>
                </div>
            </div>
        </div>
        <!-- cards END-->
    </div>
</section>
