<?php


use Umimeweby\UWTheme\Settings\UW_Sections\UW_Carousel_References;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;

$group_references = rwmb_meta(UW_Settings_Page::PREFIX . UW_Carousel_References::FIELD_GROUP, ['object_type' => 'setting'], UW_Carousel_References::OPTION_NAME);


$group_references_image = UW_Settings_Page::PREFIX . UW_Carousel_References::FIELD_GROUP_IMAGE;
$group_references_title = UW_Settings_Page::PREFIX . UW_Carousel_References::FIELD_GROUP_TITLE;
$group_references_description = UW_Settings_Page::PREFIX . UW_Carousel_References::FIELD_GROUP_DESCRIPTION;
$group_references_btn_link = UW_Settings_Page::PREFIX . UW_Carousel_References::FIELD_GROUP_BTN_LINK;

?>

<section class="relative z-50 mx-auto mb-5 w-full max-w-screen-2xl not-prose">
    <svg fill="none" class="absolute top-0 right-0 hidden object-cover pointer-events-none -left-1 -z-10 lg:block" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1441 379">
        <path d="M71.469 51.641C89.072 19.78 122.598 0 158.999 0H1281c36.4 0 69.93 19.78 87.53 51.641L1440 181H0L71.469 51.641ZM1441 378.5H1V181h1440v197.5Z" fill="#fff" />
    </svg>
    <?php if (!empty($group_references)) : ?>
        <div class="relative z-20 mx-auto w-full max-w-screen-xl pt-[66px] pb-7 md:pb-24 lg:pb-12">
            <div class="swiper vybrane-reference relative z-50 -my-14 py-14 px-16 sm:px-[33%]">
                <div class="swiper-wrapper">
                    <?php foreach ($group_references as $one_reference) : ?>
                        <?php
                        $images = $one_reference[$group_references_image];
                        $image = reset($images);
                        $link = isset($one_reference[$group_references_btn_link]) ? get_permalink($one_reference[$group_references_btn_link]) : null;
                        ?>
                        <div class="swiper-slide rounded-[10px] bg-white p-2 shadow-card md:rounded-[20px] md:p-5" 
                        <?= $link ? ('live-website-link=' . $link) : null ?> 
                        >
                            <img src="<?= wp_get_attachment_image_url($image, 'large') ?>" alt="vybrane reference" class="object-contain w-full h-auto" loading="lazy" decoding="async" />
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <!-- ::::::::::::ellipse slider:::::::::::: -->
            <div class="relative mx-auto w-full max-w-screen-lg rounded-[50%] bg-white sm:min-h-[300px] md:-mt-12 lg:bg-[#EAEAEA] md:p-6 md:pt-0">


            
                <div class="m:min-h-[300px] -translate-y-1 rounded-[50%] bg-white">
                    <div class="mx-auto w-full max-w-screen-sm pt-10 text-center sm:pt-16 md:pt-[110px]">
                        <div class="swiper ellipse-slider">
                            <div class="swiper-wrapper">
                                <?php foreach ($group_references as $one_reference) : ?>
                                    <div class="px-5 swiper-slide md:px-0">
                                        <h5 class="mb-3 text-lg font-bold text-center text-theme-purple"><?= $one_reference[$group_references_title] ?? '' ?></h5>
                                        <p class="w-full text-base font-normal leading-[21px]"><?= $one_reference[$group_references_description] ?? '' ?></p>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-center mx-auto mt-8 bg-white w-fit px-9 sm:mt-0 translate-y-4  lg:translate-y-14">
                        <a href="" class="btn-primary shadow-btnLight" id="live-url-link" target="_blank">
                            Zobrazit více
                            <svg xmlns="http://www.w3.org/2000/svg" width="31" height="30" fill="none">
                                <path fill="#FEFEFE" fill-rule="evenodd" d="M.624 9.904c-.832.712-.832 1.864 0 2.576 3.674 3.137 7.36 6.262 11.035 9.4.834.712 2.185.712 3.018 0 1.13-.965 2.258-1.931 3.387-2.897-3.054-2.593-6.082-5.238-9.155-7.8C11.932 8.582 14.969 6 18 3.412L14.61.532c-.832-.71-2.185-.71-3.017 0C7.935 3.655 4.283 6.783.624 9.904Z" clip-rule="evenodd" />
                                <path fill="#FEFEFE" fill-rule="evenodd" d="M12.697 11.016c3.055 2.595 6.088 5.224 9.155 7.801l-9.093 7.77c1.13.96 2.265 1.92 3.392 2.88.832.71 2.184.71 3.017 0 3.657-3.122 7.31-6.25 10.968-9.371.833-.712.833-1.864 0-2.576-3.674-3.138-7.36-6.263-11.034-9.4-.834-.712-2.185-.712-3.019 0l-3.386 2.896Z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </div>
                </div>

                <div class="flex justify-center items-center mt-8 lg:hidden">
                    <button class="ellipse-prev group z-10 block text-[#4C03EF] disabled:text-[#EAEAEA]">
                        <span class="flex h-full w-full rounded-full border-[10px] border-transparent bg-white p-2.5 group-hover:border-[#4C03EF]/10 group-disabled:border-transparent">
                            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none">
                                <path fill="currentColor" d="M25 0c13.8 0 25 11.2 25 25S38.8 50 25 50 0 38.8 0 25 11.2 0 25 0Zm5 36.25v-22.5L15 25l15 11.25Z" />
                            </svg>
                        </span>
                    </button>
                    <button class="ellipse-next group z-10 block text-[#4C03EF] disabled:text-[#EAEAEA]">
                        <span class="flex h-full w-full rounded-full border-[10px] border-transparent bg-white p-2.5 group-hover:border-[#4C03EF]/10 group-disabled:border-transparent">
                            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none">
                                <path fill="currentColor" d="M25 0C11.2 0 0 11.2 0 25s11.2 25 25 25 25-11.2 25-25S38.8 0 25 0Zm-5 36.25v-22.5L35 25 20 36.25Z" />
                            </svg>
                        </span>
                    </button>
                </div>
                
                <!-- Tlačítka pro desktop -->
                <button class="ellipse-prev group absolute top-1/2 left-[8%] z-10 hidden -translate-y-1/2 text-[#4C03EF] disabled:text-[#EAEAEA] lg:block">
                    <span class="flex h-full w-full rounded-full border-[10px] border-transparent bg-white p-2.5 group-hover:border-[#4C03EF]/10 group-disabled:border-transparent">
                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none">
                            <path fill="currentColor" d="M25 0c13.8 0 25 11.2 25 25S38.8 50 25 50 0 38.8 0 25 11.2 0 25 0Zm5 36.25v-22.5L15 25l15 11.25Z" />
                        </svg>
                    </span>
                </button>
                <button class="ellipse-next group absolute top-1/2 right-[8%] z-10 hidden -translate-y-1/2 text-[#4C03EF] disabled:text-[#EAEAEA] lg:block">
                    <span class="flex h-full w-full rounded-full border-[10px] border-transparent bg-white p-2.5 group-hover:border-[#4C03EF]/10 group-disabled:border-transparent">
                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none">
                            <path fill="currentColor" d="M25 0C11.2 0 0 11.2 0 25s11.2 25 25 25 25-11.2 25-25S38.8 0 25 0Zm-5 36.25v-22.5L35 25 20 36.25Z" />
                        </svg>
                    </span>
                </button>
            </div>
        </div>
    <?php endif ?>
    </div>
</section>
