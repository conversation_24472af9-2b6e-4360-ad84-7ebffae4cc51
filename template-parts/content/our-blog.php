<?php

use Umimeweby\UWTheme\CustomFields\Our_Blog_Custom_Fields;

$title = rwmb_get_value(Our_Blog_Custom_Fields::PREFIX . Our_Blog_Custom_Fields::OUR_BLOG_FIELD_TITLE);
$description = rwmb_get_value(Our_Blog_Custom_Fields::PREFIX . Our_Blog_Custom_Fields::OUR_BLOG_FIELD_DESCRIPTION);

?>
<section class="mx-auto mb-16 w-full max-w-[1230px] px-2.5 sm:px-5">
    <div class="grid w-full grid-cols-1 gap-14 lg:grid-cols-2 lg:gap-12">
        <div class="w-full sm:px-3">
            <div class="mb-[46px] text-center sm:text-left">
                <h4 class="heading-tertiary sm:heading-secondary !text-theme-purple">
                    <?= $title ?? '' ?>
                </h4>
                <p class="text-xs sm:text-base sm:leading-6">
                    <?= $description ?? '' ?>
                </p>
                <?php
                $args = array(
                    'post_type' => 'post',
                    'posts_per_page' => 3,
                );

                $query = new WP_Query($args);

                if ($query->have_posts()) : ?>
                    <div class="swiper facility-slider -m-12 p-12">
                        <div class="swiper-wrapper">
                            <?php while ($query->have_posts()) : $query->the_post(); ?>
                                    <div class="swiper-slide">
                                        <div class="mt-[46px] rounded-[50px] shadow-btnwhite">
                                            <?php if (has_post_thumbnail()) : ?>
                                                <img src="<?php the_post_thumbnail_url('medium'); ?>"
                                                     alt="card"
                                                     class="h-[197px] w-full rounded-t-[50px] object-cover"
                                                     loading="lazy"
                                                     decoding="async"/>
                                            <?php endif; ?>
                                            <div class="mx-auto w-full max-w-[430px] px-3 pt-10 pb-12 text-center sm:sm:text-left">
                                                <div class="mb-[26px] flex items-center justify-center gap-2.5 sm:justify-start">
                                                    <?php $categories = get_the_category(); ?>
                                                    <?php foreach ($categories as $category) : ?>
                                                        <p class="inline-block rounded-full bg-dark-purple px-5 py-2.5 text-xs font-normal leading-5 text-white sm:text-sm">
                                                            <?= $category->name ?? '' ?>
                                                        </p>
                                                    <?php endforeach; ?>
                                                </div>
                                                <h2 class="mb-[25px] text-xl font-bold leading-7 text-theme-purple md:text-3xl md:leading-9">
                                                    <a href="<?= trailingslashit(get_permalink()) ?>">
                                                        <?php the_title(); ?>
                                                    </a>
                                                </h2>
                                                <div class="mb-6 flex items-center justify-center gap-7 sm:justify-start">
                                                    <p class="flex items-center gap-3 text-base font-normal leading-5">
                                                        <svg xmlns="http://www.w3.org/2000/svg"
                                                             width="20"
                                                             height="20"
                                                             class="fill-dark-purple"
                                                             fill="none">
                                                            <path
                                                                  d="M10 0C4.5 0 0 4.5 0 10s4.5 10 10 10 10-4.5 10-10S15.5 0 10 0Zm4.2 14.2L9 11V5h1.5v5.2l4.5 2.7-.8 1.3Z"/>
                                                        </svg>
                                                        <?= get_the_date() ?>
                                                    </p>
                                                    <p class="flex items-center gap-3 text-base font-normal leading-5">
                                                        <svg xmlns="http://www.w3.org/2000/svg"
                                                             width="22"
                                                             height="15"
                                                             class="fill-dark-purple"
                                                             fill="none">
                                                            <path d="M11 0C6 0 1.73 3.11 0 7.5 1.73 11.89 6 15 11 15s9.27-3.11 11-7.5C20.27 3.11 16 0 11 0Zm0 12.5c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5Zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3Z"/>
                                                        </svg>
                                                        5 minut čtení
                                                    </p>
                                                </div>
                                                <p class="text-xs leading-normal sm:text-base sm:leading-6">
                                                    <?php the_excerpt(); ?>
                                                </p>
                                            </div>
                                            <span class="mx-auto flex h-0.5 w-full max-w-[70%] justify-center bg-bgborder"></span>
                                        </div>
                                    </div>
                            <?php endwhile; ?>

                        </div>
                        <button
                                class="facility-left absolute top-1/2 left-7 z-50 hidden h-[90px] w-[90px] -translate-y-1/2 items-center justify-center rounded-full bg-white text-theme-pink disabled:text-[#eaeaea] sm:flex">
                            <svg class="rotate-180"
                                 xmlns="http://www.w3.org/2000/svg"
                                 width="50"
                                 height="50"
                                 fill="none">
                                <path fill="currentColor"
                                      d="M25 50C11.2 50 0 38.8 0 25S11.2 0 25 0s25 11.2 25 25-11.2 25-25 25Zm-5-36.25v22.5L35 25 20 13.75Z"/>
                            </svg>
                        </button>
                        <button
                                class="facility-right absolute top-1/2 right-7 z-50 hidden h-[90px] w-[90px] -translate-y-1/2 items-center justify-center rounded-full bg-white text-theme-pink disabled:text-[#eaeaea] sm:flex">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                 width="50"
                                 height="50"
                                 fill="none">
                                <path fill="currentColor"
                                      d="M25 50C11.2 50 0 38.8 0 25S11.2 0 25 0s25 11.2 25 25-11.2 25-25 25Zm-5-36.25v22.5L35 25 20 13.75Z"/>
                            </svg>
                        </button>
                    </div>
                    <?php
                    wp_reset_postdata();
                endif; ?>
            </div>
            <!-- ::::::::::::dots:::::::::::: -->
            <div class="facility-dots flex items-center justify-center gap-3.5"></div>
        </div>
        <div class="order-first h-max w-full rounded-[100px] bg-number px-16 lg:order-none">
            <?php get_template_part('template-parts/components/_firm_numbers') ?>
        </div>
    </div>
</section>