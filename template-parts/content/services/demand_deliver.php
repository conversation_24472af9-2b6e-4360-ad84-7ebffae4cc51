<?php

use Umimeweby\UWTheme\CustomFields\Services\Demand_Deliver_Custom_Fields;

$title = rwmb_get_value(Demand_Deliver_Custom_Fields::PREFIX . Demand_Deliver_Custom_Fields::DEMAND_DELIVER_FIELD_TITLE,  "", 11);
$description = rwmb_get_value(Demand_Deliver_Custom_Fields::PREFIX . Demand_Deliver_Custom_Fields::DEMAND_DELIVER_FIELD_DESCRIPTION, "", 11);
$group = rwmb_meta(Demand_Deliver_Custom_Fields::PREFIX . Demand_Deliver_Custom_Fields::DEMAND_DELIVER_FIELD_GROUP, "", 11);
$category_button = Demand_Deliver_Custom_Fields::PREFIX . Demand_Deliver_Custom_Fields::DEMAND_DELIVER_FIELD_GROUP_CATEGORY;
$left_title = Demand_Deliver_Custom_Fields::PREFIX . Demand_Deliver_Custom_Fields::DEMAND_DELIVER_FIELD_GROUP_LEFT_TEXT_TITLE;
$description_left = Demand_Deliver_Custom_Fields::PREFIX . Demand_Deliver_Custom_Fields::DEMAND_DELIVER_FIELD_GROUP_LEFT_TEXT_DESCRIPTION;
$title_right = Demand_Deliver_Custom_Fields::PREFIX . Demand_Deliver_Custom_Fields::DEMAND_DELIVER_FIELD_GROUP_RIGHT_TEXT_TITLE;
$description_right = Demand_Deliver_Custom_Fields::PREFIX . Demand_Deliver_Custom_Fields::DEMAND_DELIVER_FIELD_GROUP_RIGHT_TEXT_DESCRIPTION;

?>
<section class="px-1 py-14 md:py-[71px]">
    <div id="anchor-for-scroll" class="mx-auto w-full max-w-[1170px] text-center md:text-left">
        <h3 class="mb-3.5 text-2xl font-bold text-theme-purple md:mb-[22px] md:text-3xl">
            <?= $title ?? '' ?>
        </h3>
        <p class="mb-4 pb-0.5 text-sm leading-normal md:mb-11">
            <?= $description ?? '' ?>
        </p>

        <div class="mb-7.5 items-center rounded-full border border-[#eaeaea] p-2.5 md:mb-12 md:grid md:grid-cols-[20px_calc(100%-40px)_20px] md:px-6 md:py-[18px]">
            <button class="flex-shrink-0 hidden pop-prev md:block">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10 0C15.52 0 20 4.48 20 10C20 15.52 15.52 20 10 20C4.48 20 0 15.52 0 10C0 4.48 4.48 0 10 0ZM12 14.5V5.5L6 10L12 14.5Z" fill="#8078A0" />
                </svg>
            </button>

            <div class="md:px-5">
                <div class="pr-12 swiper pop-tags">
                    <div class="swiper-wrapper">
                        <?php foreach ($group as $category) : ?>
                            <div class="swiper-slide w-fit" open>
                                <button class="w-fit rounded-full px-5 py-2.5 text-sm font-bold leading-normal transition-all group-open:bg-dark-purple group-open:text-white md:text-base md:leading-normal" open>
                                    <?= $category[$category_button] ?? '' ?>
                                </button>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <button class="flex-shrink-0 hidden pop-next md:block">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM8 14.5V5.5L14 10L8 14.5Z" fill="#8078A0" />
                </svg>
            </button>
        </div>

        <div class="swiper pop-content" thumbsSlider="">
            <div class="swiper-wrapper">
                <?php foreach ($group as $category_description) : ?>
                    <div class="swiper-slide">
                        <div class="flex flex-col gap-[18px] text-center md:flex-row md:gap-9 md:text-left">
                            <div class="w-full pt-1 md:max-w-[422px]">
                                <h4 class="mb-2.5 text-sm font-bold leading-normal text-theme-purple sm:mb-1.5 sm:text-base">
                                    <?= $category_description[$left_title] ?? '' ?>
                                </h4>
                                <p class="text-xs leading-normal sm:text-sm sm:leading-normal">
                                    <?= $category_description[$description_left] ?? '' ?>
                                </p>
                            </div>
                            <div class="hidden w-1.5 flex-shrink-0 space-y-[5px] md:block">
                                <span class="block aspect-square w-[5px] rounded-full bg-[#D9D9D9]"></span>
                                <span class="block aspect-square w-[5px] rounded-full bg-[#D9D9D9]"></span>
                                <span class="block aspect-square w-[5px] rounded-full bg-theme-pink"></span>
                                <span class="block aspect-square w-[5px] rounded-full bg-theme-pink"></span>
                                <span class="block aspect-square w-[5px] rounded-full bg-[#D9D9D9]"></span>
                                <span class="block aspect-square w-[5px] rounded-full bg-[#D9D9D9]"></span>
                                <span class="block aspect-square w-[5px] rounded-full bg-theme-pink"></span>
                                <span class="block aspect-square w-[5px] rounded-full bg-[#D9D9D9]"></span>
                                <span class="block aspect-square w-[5px] rounded-full bg-[#D9D9D9]"></span>
                                <span class="block aspect-square w-[5px] rounded-full bg-[#D9D9D9]"></span>
                                <span class="block aspect-square w-[5px] rounded-full bg-[#D9D9D9]"></span>
                                <span class="block aspect-square w-[5px] rounded-full bg-[#D9D9D9]"></span>
                            </div>
                            <div class="w-full pt-1 md:max-w-[665px]">
                                <h4 class="mb-2.5 text-sm font-bold leading-normal text-theme-purple sm:mb-1.5 sm:text-base">
                                    <?= $category_description[$title_right] ?? '' ?>
                                </h4>
                                <p class="text-xs leading-normal sm:text-sm sm:leading-normal">
                                    <?= $category_description[$description_right] ?? '' ?>
                                </p>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <a href="<?= esc_url(home_url('/kontakt/')) ?>" class="mx-auto mt-6 flex h-10 w-full max-w-[222px] items-center justify-center gap-2.5 rounded-[10px] bg-theme-pink px-5 text-base font-bold leading-none text-white shadow-theme-pink md:mx-auto md:mt-14 md:h-[62px] md:rounded-[20px]">
            Kontaktujte nás
            <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" fill="none">
                <path fill="#FEFEFE" fill-rule="evenodd" d="M.663 6.603c-.555.474-.555 1.243 0 1.717 2.449 2.092 4.906 4.175 7.356 6.266.556.475 1.457.475 2.012 0l2.258-1.93c-2.036-1.73-4.054-3.492-6.103-5.201C8.2 5.722 10.226 4 12.248 2.275L9.986.355c-.554-.473-1.456-.473-2.01 0C5.535 2.437 3.1 4.521.662 6.603Z" clip-rule="evenodd"></path>
                <path fill="#FEFEFE" fill-rule="evenodd" d="M8.711 7.344c2.037 1.73 4.059 3.483 6.103 5.201l-6.061 5.18c.753.64 1.51 1.28 2.26 1.92.556.473 1.457.473 2.012 0l7.312-6.248c.555-.474.555-1.243 0-1.717-2.45-2.092-4.906-4.175-7.356-6.267-.556-.474-1.456-.474-2.012 0L8.71 7.344Z" clip-rule="evenodd"></path>
            </svg>
        </a>
    </div>
</section>
<div class="px-5">
    <hr class="mx-auto w-full max-w-[1440px] border-t-[#d9d9d9]" />
</div>