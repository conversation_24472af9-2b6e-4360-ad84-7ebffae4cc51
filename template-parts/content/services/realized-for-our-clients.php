<?php

use Um<PERSON>weby\UWTheme\Settings\UW_Sections\UW_Mini_Case_Studies_Settings;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;

$title = rwmb_meta(UW_Settings_Page::PREFIX . UW_Mini_Case_Studies_Settings::FIELD_TITLE, ['object_type' => 'setting'], UW_Mini_Case_Studies_Settings::OPTION_NAME);

$description = rwmb_meta(UW_Settings_Page::PREFIX . UW_Mini_Case_Studies_Settings::FIELD_DESCRIPTION, ['object_type' => 'setting'], UW_Mini_Case_Studies_Settings::OPTION_NAME);

$link = rwmb_meta(UW_Settings_Page::PREFIX . UW_Mini_Case_Studies_Settings::FIELD_LINK_MORE, ['object_type' => 'setting'], UW_Mini_Case_Studies_Settings::OPTION_NAME);

$link_text_value = rwmb_meta(UW_Settings_Page::PREFIX . UW_Mini_Case_Studies_Settings::FIELD_LINK_MORE_TEXT, ['object_type' => 'setting'], UW_Mini_Case_Studies_Settings::OPTION_NAME);
$link_text = !empty($link_text_value) ? $link_text_value : 'Zobrazit všechny reference';

$group = rwmb_meta(UW_Settings_Page::PREFIX . UW_Mini_Case_Studies_Settings::FIELD_GROUP, ['object_type' => 'setting'], UW_Mini_Case_Studies_Settings::OPTION_NAME);

$group_image =  UW_Settings_Page::PREFIX . UW_Mini_Case_Studies_Settings::FIELD_GROUP_IMAGE;
$group_firm = UW_Settings_Page::PREFIX . UW_Mini_Case_Studies_Settings::FIELD_GROUP_FIRM;
$group_detail = UW_Settings_Page::PREFIX . UW_Mini_Case_Studies_Settings::FIELD_GROUP_DETAIL;
$group_date = UW_Settings_Page::PREFIX . UW_Mini_Case_Studies_Settings::FIELD_GROUP_DATE;


?>

<section class="mb-16 px-1 py-10 sm:px-5 md:mb-0 md:py-[71px]">
    <div class="mx-auto w-full max-w-[1170px] text-center md:text-left">
        <h3 class="mb-3.5 text-2xl font-bold text-theme-purple md:mb-[22px] md:text-3xl">
            <?= $title ?? '' ?>
        </h3>
        <p class="mb-6 text-base leading-normal">
            <?= $description ?? '' ?>
        </p>

        <div class="px-6">
            <div class="flex flex-wrap justify-center gap-6">
                <?php
                if (!empty($group)) :
                    foreach ($group as $firm) :
                        $images = $firm[$group_image] ?? null;
                        $logo = !empty($images) ? reset($images) : null;
                ?>
                        <div class="flex w-full sm:w-[calc(50%-12px)] md:w-[calc(33.333%-16px)] lg:w-[calc(25%-18px)] min-w-[250px]">
                            <div class="flex flex-col gap-4 w-full rounded-[50px] bg-white px-[30px] pb-7 pt-[38px] text-center shadow-[0px_0px_20px_0px_rgba(0,0,0,0.1)]">
                                <?php if ($logo) : ?>
                                    <div class="h-[100px] flex items-center justify-center">
                                        <img src="<?= wp_get_attachment_image_url($logo, 'large') ?>"
                                            alt="<?= $firm[$group_firm] ?? '' ?>"
                                            class="object-contain object-center h-full"
                                            loading="lazy"
                                            decoding="async" />
                                    </div>
                                <?php endif; ?>

                                <div class="flex-grow">
                                    <h4 class="mb-3 text-lg font-extrabold leading-tight text-theme-purple">
                                        <?= $firm[$group_firm] ?? '' ?>
                                    </h4>
                                    <p class="text-xs leading-normal text-theme-purple sm:text-sm sm:leading-tight md:text-sm">
                                        <?= $firm[$group_detail] ?? '' ?>
                                    </p>
                                </div>

                                <p class="text-xs leading-tight">
                                    <?= $firm[$group_date] ?? '' ?>
                                </p>
                            </div>
                        </div>
                <?php
                    endforeach;
                endif;
                ?>
            </div>
            <?php if (!empty($link)): ?>
                <a href="/reference"
                    class="not-prose mx-auto mt-15 flex w-full max-w-[300px] items-center justify-center gap-1.5 rounded-[10px] border-2 border-dark-purple p-2.5 text-base font-bold leading-loose text-dark-purple transition-all duration-200 hover:bg-dark-purple hover:text-white lg:gap-2.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" fill="none">
                        <path fill="currentColor" fill-rule="evenodd" d="M.416 6.603c-.555.474-.555 1.243 0 1.717 2.45 2.092 4.907 4.175 7.357 6.266.555.475 1.456.475 2.012 0l2.258-1.93c-2.036-1.73-4.055-3.492-6.104-5.201C7.955 5.722 9.98 4 12.001 2.275L9.74.355c-.555-.473-1.457-.473-2.011 0C5.29 2.437 2.855 4.521.416 6.603Z" clip-rule="evenodd"></path>
                        <path fill="currentColor" fill-rule="evenodd" d="M8.465 7.344c2.036 1.73 4.058 3.483 6.103 5.201l-6.062 5.18c.754.64 1.51 1.28 2.261 1.92.555.473 1.457.473 2.011 0 2.439-2.082 4.874-4.166 7.313-6.248.555-.474.555-1.243 0-1.717-2.45-2.092-4.907-4.175-7.357-6.267-.555-.474-1.456-.474-2.012 0L8.465 7.344Z" clip-rule="evenodd"></path>
                    </svg>
                    <span><?php echo ($link_text); ?></span>
                </a>
            <?php endif; ?>
        </div>
    </div>

</section>