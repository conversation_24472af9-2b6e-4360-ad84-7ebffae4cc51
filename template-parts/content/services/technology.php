<?php

use Um<PERSON>weby\UWTheme\Settings\UW_Sections\UW_Technologies_Settings;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;

$image = rwmb_meta(UW_Settings_Page::PREFIX . UW_Technologies_Settings::FIELD_IMAGE, ['object_type' => 'setting'], UW_Technologies_Settings::OPTION_NAME);

$title = rwmb_meta(UW_Settings_Page::PREFIX . UW_Technologies_Settings::FIELD_TITLE, ['object_type' => 'setting'], UW_Technologies_Settings::OPTION_NAME);

$description = rwmb_meta(UW_Settings_Page::PREFIX . UW_Technologies_Settings::FIELD_DESCRIPTION, ['object_type' => 'setting'], UW_Technologies_Settings::OPTION_NAME);

$group = rwmb_meta(UW_Settings_Page::PREFIX . UW_Technologies_Settings::FIELD_GROUP, ['object_type' => 'setting'], UW_Technologies_Settings::OPTION_NAME);

$group_image = UW_Settings_Page::PREFIX . UW_Technologies_Settings::FIELD_GROUP_IMAGE;

?>
<div class="relative mx-auto mb-12 w-full max-w-[1440px] rounded-[50px] bg-purple-gradient bg-cover bg-no-repeat px-6 pt-14 pb-6 md:mb-[66px] md:min-h-[360px] md:pb-[74px] lg:rounded-[100px]">
    <div class="mx-auto w-full max-w-[1170px]">
        <?php if (!empty($image)) : ?>
            <?php $images = reset( $image ) ?>
            <img src="<?= $images['url'] ?>"
                 alt="technology"
                 class="pointer-events-none absolute right-20 -top-20 h-auto w-32 object-contain lg:right-[140px] lg:-top-[35%] lg:w-auto" loading="lazy" decoding="async"/>
        <?php endif; ?>
        <div class="w-full text-center md:text-left">
            <h2 class="text-2xl font-bold leading-normal text-white md:text-3xl">
                <?= $title ?? '' ?>
            </h2>
            <p class="mt-3.5 mb-4 text-base font-normal leading-5 text-white/90 md:mt-[22px] md:mb-[30px]">
                <?= $description ?>
            </p>
        </div>
        <div class="flex flex-wrap items-center justify-center w-full gap-4 pb-3 md:flex-nowrap md:justify-between md:overflow-hidden md:pb-0">
            <?php if (!empty($group)) : ?>
                <?php foreach ($group as $one_logo) : ?>
                    <?php
                    $images = $one_logo[$group_image];
                    $logo = reset($images)
                    ?>
                    <img src="<?= wp_get_attachment_image_url($logo, 'medium') ?>"
                         alt="technology"
                         class="h-auto w-[59px] object-contain sm:w-20 lg:w-28 xl:w-auto" loading="lazy" decoding="async"/>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>
