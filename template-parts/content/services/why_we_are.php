<?php

use Umimeweby\UWTheme\Settings\UW_Sections\UW_Why_SelectUs_Cards_Settings;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;



$title = rwmb_meta(UW_Settings_Page::PREFIX . UW_Why_SelectUs_Cards_Settings::FIELD_SECTION_TITLE, ['object_type' => 'setting'], UW_Why_SelectUs_Cards_Settings::OPTION_NAME);
$description = rwmb_meta(UW_Settings_Page::PREFIX . UW_Why_SelectUs_Cards_Settings::FIELD_SECTION_DESCRIPTION, ['object_type' => 'setting'], UW_Why_SelectUs_Cards_Settings::OPTION_NAME);
$group = rwmb_meta(UW_Settings_Page::PREFIX . UW_Why_SelectUs_Cards_Settings::FIELD_GROUP, ['object_type' => 'setting'], UW_Why_SelectUs_Cards_Settings::OPTION_NAME);

$group_image = UW_Settings_Page::PREFIX . UW_Why_SelectUs_Cards_Settings::FIELD_GROUP_IMAGE;
$group_title = UW_Settings_Page::PREFIX . UW_Why_SelectUs_Cards_Settings::FIELD_GROUP_TITLE;
$group_description = UW_Settings_Page::PREFIX . UW_Why_SelectUs_Cards_Settings::FIELD_GROUP_DESCRIPTION;

?>
<section>
    <div class="mx-auto mt-12 w-full max-w-[1170px] pb-9 md:mt-[70px] md:pb-[74px]">
        <div class="w-full text-center md:text-left">
            <h2 class="text-3xl font-bold leading-normal text-theme-purple">
                <?= $title ?? '' ?>
            </h2>
            <p class="mt-2 text-base font-normal leading-5 text-theme-purple/80">
                <?= $description ?? '' ?>
            </p>
        </div>
        <!-- ball slier -->
        <div class="flex flex-wrap items-stretch justify-center w-full gap-5 mt-10 text-center md:mt-12 md:text-left">
            <?php if (!empty($group)) : ?>
                <?php foreach ($group as $why_we_are) : ?>
                    <div class="w-full max-w-[250px] flex flex-col"> <!-- Přidán flex-col -->
                    <div class="h-[100px] flex items-center justify-center md:justify-start">
                            <?php
                            $images = $why_we_are[$group_image] ?? null;
                            $icon = !empty($images) ? reset($images) : null;
                            ?>
                            <?php if ($icon) : ?>
                                <img src="<?= wp_get_attachment_image_url($icon, 'large') ?>"
                                    alt="ball"
                                    class="aspect-square max-w-[100px] object-contain"
                                    loading="lazy"
                                    decoding="async" />
                            <?php endif; ?>
                        </div>
                        <div class="mt-[19px] min-h-40 flex flex-col flex-grow">
                            <h2 class="text-lg font-bold leading-6 text-theme-purple">
                                <?= $why_we_are[$group_title] ?? '' ?>
                            </h2>
                            <div class="mt-3.5"> 
                                <p class="text-base font-normal leading-5">
                                    <?= $why_we_are[$group_description] ?? '' ?>
                                </p>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</section>