<!-- title section  -->
<?php

use Umimeweby\UWTheme\CustomPostType\Our_Services_Custom_Post_Type;

use Umimeweby\UWTheme\CustomFields\Header_Custom_Fields;
use Umimeweby\UWTheme\CustomFields\Reference\Single\Single_Reference_Custom_Fields;

$subtitle = rwmb_get_value(Single_Reference_Custom_Fields::PREFIX . Single_Reference_Custom_Fields::SINGLE_REFERENCE_FIELD_SUBTITLE);
$subtitle_single = rwmb_get_value(Our_Services_Custom_Post_Type::PREFIX . Our_Services_Custom_Post_Type::OUR_SERVICES_CUSTOM_FIELDS_SUBTITLE);

$header_description = rwmb_get_value(Header_Custom_Fields::PREFIX . Header_Custom_Fields::HEADER_CUSTOM_FIELDS_DESCRIPTION);

global $post;


$title_string = $post?->post_title;
$tag_name = 'div';
if (is_post_type_archive(Our_Services_Custom_Post_Type::CPT_KEY)) {
    $title_string = 'Naše služby pro váš web';
    $tag_name = 'h1';
}
if (is_home() || is_search()) {
    $title_string =  'Jak na web';
}
?>
<<?php echo $tag_name ?> class="heading-secondary z-10 relative mb-1.5 text-theme-purple text-2xl">
    <?= $title_string  ?>
    </<?php echo $tag_name ?>>
    <p class="text-base leading-6 uppercase">
        <?php if (get_post_type() === 'nase-sluzby') : ?>
            Navrhneme nový design, modernizujeme technologie webové rakety, vylepšíme její pohon  a doděláme co je třeba.
        <?php elseif (is_single(get_the_ID()) || is_page(['pripojime-se-za-letu', 'startujeme-od-nuly'])) : ?>
            <?= $subtitle ?? '' ?>
            <?= $subtitle_single ?? '' ?>
        <?php else : ?>
            <?= is_home() || is_archive() || is_search() ? 'aneb Trendy a tipy pro váš webdesign, e-mailing, webové stránky a copywriting' : $header_description ?? '' ?>
        <?php endif; ?>
        &nbsp;
    </p>
<!-- end title section  -->