<?php

use Um<PERSON>weby\UWTheme\Settings\UW_CTA_Settings;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;

$categories = get_categories([
    'parent' => 0
]);
?>

<main class="w-full px-4 pt-4 mb-12 md:mb-0 md:px-5 md:pt-0">

    <!-- blog-list-hero -->
    <div class="mx-auto block h-full w-full max-w-5xl mb-10 text-center">
        <div class="max-w-xl mx-auto p-6 bg-white border border-purple-400 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <div class="heading-secondary mb-4 text-2xl text-theme-purple md:text-40">
                Jak na web
            </div>
            <p class="text-base leading-6 uppercase md:text-lg">
                aneb Trendy a tipy pro váš online bysnys, webov<PERSON> str<PERSON>, webové aplikace, e-mailing a copywriting
            </p>

        </div>
    </div>
    <!-- end - blog-list-hero -->

    <div class="relative z-30 mx-auto w-full max-w-[1050px] bg-white xl:max-w-[1172px]">


        <!-- single column blog list -->
        <div class="flex flex-col items-start justify-between w-full gap-3 mb-8" id="posts-container">

            <?php get_template_part('template-parts/components/blog/_index_list_loop'); ?>


        </div>
        <?php
        $value = rwmb_meta(
            UW_Settings_Page::PREFIX . UW_CTA_Settings::FIELD_CTA_ABOVE_PAGINATION_BLOG_LIST,
            ['object_type' => 'setting'],
            UW_CTA_Settings::OPTION_CTA_SETTING
        );

        if (!empty($value)) {
            foreach ($value as $one_shortcode) {
                if (shortcode_exists($one_shortcode[0])) {
                    echo do_shortcode('[' . $one_shortcode[0] . ']');
                }
            }
        }
        ?>

    </div>




    <?php get_template_part('template-parts/components/blog/_index_pagination_mobile');
    ?>
    <?php get_template_part('template-parts/components/blog/_index_pagination_desktop');
    ?>
</main>