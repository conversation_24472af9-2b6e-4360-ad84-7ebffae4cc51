<?php

use Um<PERSON>weby\UWTheme\Settings\UW_Settings_Page;

$title = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::WORDPRESS_CMS_REFERENCE_FIELD_TITLE, ['object_type' => 'setting'], UW_Settings_Page::OPTION_CMS_WORDPRESS_SETTING);
$description = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::WORDPRESS_CMS_REFERENCE_FIELD_DESCRIPTION, ['object_type' => 'setting'], UW_Settings_Page::OPTION_CMS_WORDPRESS_SETTING);
$group = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::WORDPRESS_CMS_REFERENCE_FIELD_GROUP, ['object_type' => 'setting'], UW_Settings_Page::OPTION_CMS_WORDPRESS_SETTING);
$group_image = UW_Settings_Page::PREFIX . UW_Settings_Page::WORDPRESS_CMS_REFERENCE_GROUP_IMAGE;
$btn_text = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::WORDPRESS_CMS_REFERENCE_FIELD_BTN_TEXT, ['object_type' => 'setting'], UW_Settings_Page::OPTION_CMS_WORDPRESS_SETTING);

?>
<div class="mx-auto mb-[50px] w-full max-w-[1250px] md:mb-[78px]">
    <div class="md:px-10">
        <h2 class="text-center text-[22px] font-bold leading-normal text-theme-purple md:text-left md:text-3xl">
            <?= $title ?? '' ?>
        </h2>
        <p class="mb-1.5 mt-4 text-center text-sm font-normal leading-5 text-light-purple md:mt-[22px] md:text-left">
            <?= $description ?? '' ?>
        </p>
    </div>
    <div class="mb-4 grid grid-cols-[repeat(3,240px)] gap-5 overflow-auto pt-10 pb-6 md:mb-[54px] md:w-full md:grid-cols-2 md:pb-0 lg:grid-cols-3 xl:gap-[50px] xl:px-10">
        <?php $count = 0; ?>
        <?php foreach ($group as $reference) : ?>
            <div class="w-full rounded-[20px] bg-white p-4 shadow-form <?= $count++ >= 3 ? 'hidden' : '' ?>">
                <?php
                $images = $reference[$group_image];
                $image = reset($images);
                ?>
                <a href="#"
                   class="group relative">
                    <img src="<?= wp_get_attachment_image_url($image, 'large') ?>"
                         alt="card"
                         class="block h-auto w-full rounded-lg object-cover" loading="lazy" decoding="async"/>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
    <button class="group mx-auto flex h-10 w-full max-w-[198px] items-center justify-center gap-1.5 rounded-[10px] border-2 border-light-purple p-2.5 text-base font-bold leading-loose text-light-purple transition-all duration-200 hover:bg-light-purple hover:text-white md:h-[62px] lg:gap-2.5"
    id="referenceButton">
        <span><?= $btn_text ?? '' ?></span>
        <svg xmlns="http://www.w3.org/2000/svg"
             width="21"
             height="20"
             fill="none">
            <path fill="currentColor"
                  fill-rule="evenodd"
                  d="M.416 6.603c-.555.474-.555 1.243 0 1.717 2.45 2.092 4.907 4.175 7.357 6.266.555.475 1.456.475 2.012 0l2.258-1.93c-2.036-1.73-4.055-3.492-6.104-5.201C7.955 5.722 9.98 4 12.001 2.275L9.74.355c-.555-.473-1.457-.473-2.011 0C5.29 2.437 2.855 4.521.416 6.603Z"
                  clip-rule="evenodd"/>
            <path fill="currentColor"
                  fill-rule="evenodd"
                  d="M8.465 7.344c2.036 1.73 4.058 3.483 6.103 5.201l-6.062 5.18c.754.64 1.51 1.28 2.261 1.92.555.473 1.457.473 2.011 0 2.439-2.082 4.874-4.166 7.313-6.248.555-.474.555-1.243 0-1.717-2.45-2.092-4.907-4.175-7.357-6.267-.555-.474-1.456-.474-2.012 0L8.465 7.344Z"
                  clip-rule="evenodd"/>
        </svg>
    </button>
</div>