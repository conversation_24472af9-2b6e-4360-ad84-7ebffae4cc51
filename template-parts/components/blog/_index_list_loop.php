<?php
if (have_posts()) :
    while (have_posts()) : the_post();
?>

        <div class="w-full mb-12 border-l-[1px] border-light-100 pl-5" data-type="one-post">
            <a href="<?= trailingslashit(get_permalink()) ?>">
                <div class="w-full">
                    <h2 class="mb-5 text-3xl font-bold leading-9 text-theme-purple">
                        <?php echo(get_the_title()) ?>
                    </h2>
                </div>
            </a>
            <div class="flex items-center gap-2 mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" class="fill-dark-purple">
                    <path d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8Zm3.36 11.36L7.2 8.8V4h1.2v4.16l3.6 2.16-.64 1.04Z" />
                </svg>
                <p class="text-sm font-normal leading-normal">
                    <?= date('d F, Y', strtotime($post->post_date)); ?>
                </p>
            </div>
            <div class="flex flex-col md:flex-row gap-4">
                <?php if (has_post_thumbnail()): ?>
                    <a class="block w-full md:w-80 h-48 md:h-64 overflow-hidden rounded-lg flex-shrink-0" href="<?php the_permalink(); ?>">
                        <img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'medium_large'); ?>" 
                             alt="<?php echo get_the_title(); ?>" 
                             class="w-full h-full object-cover object-center">
                    </a>
                <?php endif; ?>
                <a href="<?php the_permalink(); ?>">
                    <div class="text-base text-theme-purple">
                        <?= wp_trim_words($post->post_content, 50); ?>
                    </div>
                </a>
            </div>
        </div>
<?php
    endwhile;
endif;
?>
