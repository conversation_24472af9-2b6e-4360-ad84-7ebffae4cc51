<?php
while (have_posts()) :
    the_post();
?>
    <div class="relative z-30 mx-auto w-full max-w-5xl bg-white">
        <div class="flex flex-col items-start justify-between w-full mb-8 gap-7">
            <!-- :::::::::::: post content full width :::::::::::: -->
            <div class="w-full" data-type="uw-one-post">
                <h1 class="heading-secondary mb-1.5 text-3xl text-theme-purple leading-10 xl:leading-uw-relaxed xl:text-5xl  normal-case">
                    <?= $post->post_title ?>
                </h1>
                <?php if (get_the_post_thumbnail_url(get_the_ID(), 'large')) : ?>
                    <div class="max-h-[300px] overflow-hidden relative w-full rounded-[20px] object-cover shadow-blog mb-2 mt-6 not-prose">
                        <img src="<?= get_the_post_thumbnail_url(get_the_ID(), 'large'); ?>" alt="blog" class=" w-full" />
                    </div>
                <?php else : ?>
                    <div class="h-auto w-full rounded-[20px]"></div>
                <?php endif; ?>

                <div class="w-full pt-3">
                    <div class="flex flex-col items-center justify-center gap-4 pt-4 pb-6 sm:flex-row sm:justify-start sm:gap-10">
                        <div class="flex items-center gap-7">
                            <div class="flex items-center gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" class="fill-dark-purple">
                                    <path d="M10 0C4.5 0 0 4.5 0 10s4.5 10 10 10 10-4.5 10-10S15.5 0 10 0Zm4.2 14.2L9 11V5h1.5v5.2l4.5 2.7-.8 1.3Z" />
                                </svg>
                                <p class="text-base font-normal leading-normal text-dark-purple">
                                    <?= get_the_date() ?>
                                </p>
                            </div>
                        </div>
                        <p class="flex items-center gap-2.5 text-base font-normal text-dark-purple">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" class="fill-dark-purple">
                                <path d="M10 0C4.48 0 0 4.48 0 10s4.48 10 10 10 10-4.48 10-10S15.52 0 10 0Zm0 4c1.93 0 3.5 1.57 3.5 3.5S11.93 11 10 11 6.5 9.43 6.5 7.5 8.07 4 10 4Zm0 14c-2.03 0-4.43-.82-6.14-2.88a9.947 9.947 0 0 1 12.28 0C14.43 17.18 12.03 18 10 18Z" />
                            </svg>
                            <?= get_the_author() ?>
                        </p>
                        <?php if(get_categories()): ?>
                        <p class="flex flex-wrap"><span class="font-semibold">Rubriky:</span> <span class="text-theme-purple pl-2"> <?= the_category(', ') ?></span></p>
                        <?php endif; ?>
                    </div>
                    <div class="text-lg font-normal leading-6 text-theme-purple wp-block-content">
                        <?= the_content() ?>
                    </div>
                    <div class="flex flex-col">
                        <?php if(get_the_tags()): ?>
                        <p class="flex flex-wrap"><span class="font-semibold">Štítky:</span> <span class="text-theme-purple pl-2"> <?= the_tags('') ?></span></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <!-- :::::::::::: back to index button part :::::::::::: -->
            <div class="mt-20 block mx-auto pb-12">
                <a href="<?= esc_url(get_permalink(get_option('page_for_posts'))) ?>" class="not-prose w-full max-w-[280px] mx-auto flex h-[42px]  items-center justify-center gap-2.5 rounded-[10px] border-2 border-dark-purple text-base font-bold leading-normal text-dark-purple hover:bg-dark-purple hover:text-white px-2">
                    Zpět na seznam článků
                    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" fill="none">
                        <path fill="#8078A0" fill-rule="evenodd" d="M.663 6.603c-.555.474-.555 1.243 0 1.717 2.449 2.092 4.906 4.175 7.356 6.266.556.475 1.457.475 2.012 0l2.258-1.93c-2.036-1.73-4.054-3.492-6.103-5.201C8.2 5.722 10.226 4 12.248 2.275L9.986.355c-.554-.473-1.456-.473-2.01 0C5.535 2.437 3.1 4.521.662 6.603Z" clip-rule="evenodd" />
                        <path fill="#8078A0" fill-rule="evenodd" d="M8.711 7.344c2.037 1.73 4.059 3.483 6.103 5.201l-6.061 5.18c.753.64 1.51 1.28 2.26 1.92.556.473 1.457.473 2.012 0l7.312-6.248c.555-.474.555-1.243 0-1.717-2.45-2.092-4.906-4.175-7.356-6.267-.556-.474-1.456-.474-2.012 0L8.71 7.344Z" clip-rule="evenodd" />
                    </svg>
                </a>
            </div>
        </div>
    </div>
<?php endwhile; ?>