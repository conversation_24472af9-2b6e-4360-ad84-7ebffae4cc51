<!-- :::::::::::: header header default   :::::::::::: -->
<header class="relative xl:min-h-[320px] w-full bg-theme-purple bg-cover bg-no-repeat px-5 pr-2.5 md:mb-[30px] md:bg-hero md:pr-5">
    <!-- ::::::::::::navbar:::::::::::: -->
    <?php get_template_part('template-parts/components/_nav_top') ?>
    <!-- :::::::::::: plocha pod navbarem :::::::::::: -->
    <?php
    $file_to_load = 'template-parts/components/header-section/_header_for_pages';
    if (get_post_type() === 'post')
    {
        
        $file_to_load = 'template-parts/single-list-hero';
    }

    // remove breadcrumbs and title for specific page templates

    if (is_page_template('page-templates/landing-with-top-menu.php'))
    {
        $file_to_load = '';
    }

    if (!empty($file_to_load))
    {
        get_template_part($file_to_load);
    }
    ?>
</header>