<?php

use Um<PERSON>weby\UWTheme\CustomFields\Contact_Form_Custom_Fields;
use Umimeweby\UWTheme\Form\Generic_Form;

$title = rwmb_get_value(Contact_Form_Custom_Fields::PREFIX . Contact_Form_Custom_Fields::CONTACT_FORM_FIELD_TITLE);
$description = rwmb_get_value(Contact_Form_Custom_Fields::PREFIX . Contact_Form_Custom_Fields::CONTACT_FORM_FIELD_DESCRIPTION);

$form = new Generic_Form();


?>
<section class="relative z-20 mx-auto w-full max-w-[1440px]">
    <div class="w-full bg-[#eaeaea] px-5 pt-[60px] md:rounded-t-[100px] ">
        <div class="relative mx-auto w-full max-w-[1170px] text-center md:text-left">
            <div class="relative">
                <h2 class="mb-4 text-3xl font-bold leading-normal text-theme-purple">
                    <?= $title ?? '' ?>
                </h2>
                <p class="text-sm font-normal leading-5 mb-8 md:text-base">
                    <?= $description ?? '' ?>
                </p>
            </div>
            <?= $form->get_form_html_code() ?>
        </div>
    </div>

    <svg class="hidden h-auto w-full md:block" xmlns="http://www.w3.org/2000/svg" width="1440" height="181"
         fill="none" viewBox="0 0 1440 181">
        <path fill="#EAEAEA"
              d="M71.469 129.359C89.072 161.22 122.598 181 158.999 181H1281c36.4 0 69.93-19.78 87.53-51.641L1440 0H0l71.469 129.359Z" />
    </svg>
</section>
