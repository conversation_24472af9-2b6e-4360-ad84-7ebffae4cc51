<?php

use Umimeweby\UWTheme\CustomFields\Reference\Single\Single_Reference_Custom_Fields;
use Umimeweby\UWTheme\CustomPostType\Reference_Custom_Post_Type;
use Umimeweby\UWTheme\Taxonomies\Reference_Custom_Taxonomy;

$reference_id = $args['reference_id'] ?? '';
$card_css_classes = $args['card_css_classes'] ?? 'w-11/12 mx-auto md:mx-0 md:w-[calc(50%-10px)] lg:w-[calc(33.333%-14px)] xl:w-[calc(25%-38px)]';

$type_reference = Reference_Custom_Taxonomy::TAXONOMY_CATEGORY;

$title = get_post_meta($reference_id, Reference_Custom_Post_Type::PREFIX . Reference_Custom_Post_Type::REFERENCE_CARDS_FIELD_TITLE, true);
$description = get_post_meta($reference_id, Reference_Custom_Post_Type::PREFIX . Reference_Custom_Post_Type::REFERENCE_CARDS_FIELD_DESCRIPTION, true);
$btn_text = get_post_meta($reference_id, Reference_Custom_Post_Type::PREFIX . Reference_Custom_Post_Type::REFERENCE_CARDS_FIELD_BTN_TEXT, true);
$reference_single_description = get_post_meta($reference_id, Single_Reference_Custom_Fields::PREFIX . Single_Reference_Custom_Fields::SINGLE_REFERENCE_FIELD_DESCRIPTION, true);

$terms = get_the_terms($reference_id, $type_reference);
$term_names = [];
if (!is_wp_error($terms) && !empty($terms)) {
    foreach ($terms as $term) {
        $term_names[] = $term->name;
    }
}
$term_name = !empty($term_names) ? implode(', ', $term_names) : '';



?>
<!-- single card with hover  -->
<div class="<?= $card_css_classes ?> rounded-[20px] bg-white p-4 shadow-form">
    <div class="flex flex-col justify-between h-full">
        <div class="relative group overflow-hidden rounded-lg">
            <img src="<?= get_the_post_thumbnail_url($reference_id, 'full'); ?>" alt="<?= $title ?? '' ?>" class="block object-cover w-full h-72" loading="lazy" decoding="async" />
            <div class="absolute inset-0 flex flex-col items-center justify-center bg-theme-purple p-4 opacity-0 transition-opacity duration-300 group-hover:opacity-100 md:p-6">
                <p class="text-center text-xs font-normal text-light-purple md:text-sm">
                    <?= $description ?? '' ?>
                </p>
                <?php if (!empty($term_name)) : ?>
                <p class="mt-4 mb-4 hidden w-fit items-center justify-center rounded-full border border-dashed border-light-purple py-1.5 px-4 text-xs font-bold text-light-purple md:flex">
                    <?= $term_name ?>
                </p>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="mt-6">
            <h3 class="text-center text-xl font-bold leading-7 text-dark-purple border-b-2 border-theme-pink pb-2 mb-3">
                <?= $title ?? '' ?>
            </h3>
            
            <?php if (!empty($reference_single_description)) { ?>
                <div class="flex justify-center items-center">

                
                <a href="<?= get_permalink($reference_id); ?>" class="btn-primary inline-flex items-center mt-2 text-sm font-semibold  !text-white hover:text-dark-purple shadow-none">
                    <?= $btn_text ?? 'Zobrazit detail' ?>
                    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" fill="none" class="ml-2">
                        <path fill="currentColor" fill-rule="evenodd" d="M.663 6.603c-.555.474-.555 1.243 0 1.717 2.449 2.092 4.906 4.175 7.356 6.266.556.475 1.457.475 2.012 0l2.258-1.93c-2.036-1.73-4.054-3.492-6.103-5.201C8.2 5.722 10.226 4 12.248 2.275L9.986.355c-.554-.473-1.456-.473-2.01 0C5.535 2.437 3.1 4.521.662 6.603Z" clip-rule="evenodd" />
                        <path fill="currentColor" fill-rule="evenodd" d="M8.711 7.344c2.037 1.73 4.059 3.483 6.103 5.201l-6.061 5.18c.753.64 1.51 1.28 2.26 1.92.556.473 1.457.473 2.012 0l7.312-6.248c.555-.474.555-1.243 0-1.717-2.45-2.092-4.906-4.175-7.356-6.267-.556-.474-1.456-.474-2.012 0L8.71 7.344Z" clip-rule="evenodd" />
                    </svg>
                </a>
            </div>
            <?php } ?>
        </div>
    </div>
</div>
<!-- single card with hover  END-->
