<div class="w-full rounded-[50px] bg-white p-5 py-12 shadow-form lg:p-12">
    <div lang="cs-CZ" dir="ltr">
        <form id="<?= $form_id ?>" action="<?= $this->get_form_action() ?>" method="post">
            <input type="hidden" name="action" value="<?= $this->get_form_name() ?>">
            <input type="hidden" name="<?= $spam_control_field ?>" value="" id="<?= $spam_control_field ?>">
            <?php wp_nonce_field($nonce_name); ?>
            <div class="contact-us">
                <div class="contact-us-content">
                    <div class="w-full">
                        <input size="40" id="first-name" required placeholder="Jméno..." value="" type="text" name="first-name">
                    </div>
                    <div class="w-full">
                        <input size="40" id="last-name" required placeholder="Příjmení..." value="" type="text" name="last-name">
                    </div>
                </div>
                <div class="contact-us-content">
                    <div class="w-full">
                        <input size="40" id="tel" required placeholder="Telefon..." value="" type="text" name="tel">
                    </div>
                    <div class="w-full">
                        <input size="40" id="contact-email" required placeholder="Email..." value="" type="text" name="email">
                    </div>
                </div>
                <div class="w-full">
                    <textarea cols="40" rows="10" id="contact-message" required placeholder="Zpráva..." name="message"></textarea>
                </div>
                <!--   info buttons -->
                <div class="contact-info-button">
                    <!-- Info texty -->
                    <div class="flex flex-col sm:flex-row items-center gap-4"> <!-- přidáno items-center -->
                        <div class="flex flex-row gap-2 items-center"> <!-- přidáno items-center -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none">
                                <path fill="#8078A0" d="M10 0C4.48 0 0 4.48 0 10s4.48 10 10 10 10-4.48 10-10S15.52 0 10 0Zm0 15c-.55 0-1-.45-1-1v-4c0-.55.45-1 1-1s1 .45 1 1v4c0 .55-.45 1-1 1Zm1-8H9V5h2v2Z"></path>
                            </svg>
                            <p>Všechna pole povinná.</p>
                        </div>

                        <div class="flex flex-row gap-2 items-center"> <!-- přidáno items-center -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="20" fill="none">
                                <path fill="#8078A0" d="M13.333 6.667h-.952V4.762A4.764 4.764 0 0 0 7.619 0a4.764 4.764 0 0 0-4.762 4.762v1.905h-.952A1.91 1.91 0 0 0 0 8.57v9.524A1.91 1.91 0 0 0 1.905 20h11.428a1.91 1.91 0 0 0 1.905-1.905V8.571a1.91 1.91 0 0 0-1.905-1.904ZM7.62 15.238a1.91 1.91 0 0 1-1.905-1.905A1.91 1.91 0 0 1 7.62 11.43a1.91 1.91 0 0 1 1.905 1.904 1.91 1.91 0 0 1-1.905 1.905ZM4.762 6.667V4.762a2.853 2.853 0 0 1 2.857-2.857 2.853 2.853 0 0 1 2.857 2.857v1.905H4.762Z"></path>
                            </svg>
                            <p>SSL zabezpečení</p>
                        </div>
                    </div>

                    <!-- Submit button -->
                    <button type="submit" class="contact-submit-button w-full"> <!-- přidána responzivní šířka -->
                        Odeslat
                        <img src="/wp-content/themes/umimeweby-cosmic-theme/assets/images/blog/submit.png" alt="man" class="aspect-square w-[34px] rounded-full object-cover">
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.getElementById('<?= $form_id ?>').addEventListener('submit', function(event) {
        let email = document.getElementsByName("email")[0].value.trim();
        let message = document.getElementById("contact-message").value.trim();

        let isValid = true;

        if (email === "") {
            isValid = false;
        }

        if (message === "") {
            isValid = false;
        }

        if (isValid) {
            document.getElementById('<?= $spam_control_field ?>').value = '<?= $spam_control_string ?>';
        }

        if (!isValid) {
            document.getElementById('<?= $spam_control_field ?>').value = '';
        }
        return isValid;
    });
</script>