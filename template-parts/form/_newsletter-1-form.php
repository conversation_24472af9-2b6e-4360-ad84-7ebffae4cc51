<form id="<?= $form_id ?>" action="<?= $form_action ?>" method="POST" class="relative z-50 w-full">
    <input type="hidden" name="action" value="<?= $wp_action ?>">
    <input type="hidden" name="<?= $spam_control_field ?>" value="" id="<?= $spam_control_field ?>">
    <?php wp_nonce_field($nonce_name); ?>

    <input type="email" name="email" placeholder="Email..." id="email_blog" class="h-full w-full rounded-[10px] border border-light-gray-100 px-5 py-3 outline-none" />
    <div class="flex flex-col items-center">
        <button class="btn-primary leading-normal shadow-none px-6 mt-3">
            Získat
            <span class="pl-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="20" fill="none">
                    <path fill="#FFFFFF" d="m.011 20 23.322-10L.011 0 0 7.778 16.667 10 0 12.222.011 20Z" />
                </svg>
            </span>
        </button>
    </div>
</form>


<script>
    document.getElementById('<?= $form_id ?>').addEventListener('submit', function(event) {        
        let email = document.getElementsByName("email")[0].value.trim();
        let control = document.getElementsByName('<?= $spam_control_field ?>')[0].value.trim();

        let isValid = true;

        if (email === "") {
            isValid = false;
        }

        if (control != "") {
            isValid = false;
        }


        if (isValid) {
            document.getElementById('<?= $spam_control_field ?>').value = '<?= $spam_control_string ?>';
        }

        if (!isValid) {
            document.getElementById('<?= $spam_control_field ?>').value = '';
        }

        return isValid;
    });
</script>