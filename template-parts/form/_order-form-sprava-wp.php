<?php

?>

<form action="<?= $this->get_form_action() ?>" id="order-form-wp" method="POST" class="relative z-50 w-full">
    <input type="hidden" name="action" value="<?= $this->get_form_name() ?>">
    <input type="hidden" name="dulezita_kontrola_objednavky" value="" id="dulezita_kontrola_objednavky">
    <?php wp_nonce_field('order-form-sprava-wp-nonce'); ?>
    <div class="grid w-full grid-cols-1 gap-5 mb-5 sm:grid-cols-2">
        <input type="text" name="order-name" placeholder="Jméno..." class="h-10 w-full rounded-[10px] border-none bg-white px-5 py-1 text-sm font-normal leading-5 text-light-purple outline-none placeholder:text-current" required />
        <input type="text" name="order-lname" placeholder="Příjmení..." class="h-10 w-full rounded-[10px] border-none bg-white px-5 py-1 text-sm font-normal leading-5 text-light-purple outline-none placeholder:text-current" required />
    </div>
    <div class="grid w-full grid-cols-1 gap-5 mb-5 sm:grid-cols-2">
        <input type="tel" name="order-telephone" placeholder="Telefon..." class="h-10 w-full rounded-[10px] border-none bg-white px-5 py-1 text-sm font-normal leading-5 text-light-purple outline-none placeholder:text-current" />
        <input type="email" name="order-email" placeholder="Email..." class="h-10 w-full rounded-[10px] border-none bg-white px-5 py-1 text-sm font-normal leading-5 text-light-purple outline-none placeholder:text-current" required />
    </div>
    <div class="grid w-full grid-cols-1 gap-5 mb-5 sm:grid-cols-2">
        <input type="text" name="order-jmeno" placeholder="Jméno společnosti..." class="h-10 w-full rounded-[10px] border-none bg-white px-5 py-1 text-sm font-normal leading-5 text-light-purple outline-none placeholder:text-current" required />
        <input type="text" name="order-ic" placeholder="IČ..." class="h-10 w-full rounded-[10px] border-none bg-white px-5 py-1 text-sm font-normal leading-5 text-light-purple outline-none placeholder:text-current" />
    </div>
    <input type="text" name="order-url" placeholder="URL adresa webu..." class="mb-5 h-10 w-full rounded-[10px] border-none bg-white px-5 py-1 text-sm font-normal leading-5 text-light-purple outline-none placeholder:text-current" required />
    <input type="text" name="order-text" id="selected-plan" placeholder="Vyber hodnotu..." class="mb-5 h-10 w-full rounded-[10px] border-none bg-light-purple px-5 py-1 text-sm font-normal leading-5 text-white/50 outline-none placeholder:text-current" readonly />
    <div class="flex flex-wrap gap-8 mb-7">
        <div>
            <input type="checkbox" name="agree-terms" value="ano" id="type-1" class="hidden peer" />
            <label for="type-1" class="flex items-center gap-2.5 text-sm leading-normal text-light-purple before:flex before:h-5 before:w-5 before:items-center before:justify-center before:rounded-[5px] before:border before:border-light-purple before:text-transparent before:content-['\2713'] peer-checked:before:text-light-purple">
                <span><a href="/obchodni-podminky/" class="no-underline hover:underline" target="_blank" rel="noopener noreferrer">Souhlasím s obchodními podmínkami</a></span>
            </label>
        </div>
    </div>
    <!------------form privacy------------>
    <div class="flex flex-col items-center justify-between w-full gap-7 md:flex-row md:gap-4">
        <div class="w-full max-w-[135px]">
            <p class="flex items-center gap-2.5 text-sm font-normal leading-5 text-light-purple">
                <svg width="16" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13.333 6.667h-.952V4.762A4.764 4.764 0 0 0 7.619 0a4.764 4.764 0 0 0-4.762 4.762v1.905h-.952A1.91 1.91 0 0 0 0 8.57v9.524A1.91 1.91 0 0 0 1.905 20h11.428a1.91 1.91 0 0 0 1.905-1.905V8.571a1.91 1.91 0 0 0-1.905-1.904ZM7.62 15.238a1.91 1.91 0 0 1-1.905-1.905A1.91 1.91 0 0 1 7.62 11.43a1.91 1.91 0 0 1 1.905 1.904 1.91 1.91 0 0 1-1.905 1.905ZM4.762 6.667V4.762a2.853 2.853 0 0 1 2.857-2.857 2.853 2.853 0 0 1 2.857 2.857v1.905H4.762Z" fill="#8078A0" />
                </svg>SSL zabezpečení
            </p>
        </div>
        <button class="flex h-[62px] w-full max-w-full items-center justify-center gap-2.5 rounded-[20px] bg-light-purple p-2 text-base font-bold leading-normal text-white sm:max-w-[314px]">Odeslat objednávku Richardovi <img src="<?= get_stylesheet_directory_uri() ?>/assets/images/sprava/form-btn.png" alt="user" class="object-cover w-auto h-auto" /></button>
    </div>

    <p class="mt-7 flex items-center justify-center gap-2.5 text-sm font-normal leading-5 text-light-purple">
        <svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 0C4.48 0 0 4.48 0 10s4.48 10 10 10 10-4.48 10-10S15.52 0 10 0Zm0 15c-.55 0-1-.45-1-1v-4c0-.55.45-1 1-1s1 .45 1 1v4c0 .55-.45 1-1 1Zm1-8H9V5h2v2Z" fill="#8078A0" />
        </svg>Pole jsou povinná
    </p>
</form>

<script>
    document.getElementById('order-form-wp').addEventListener('submit', function(event) {
        let name = document.getElementsByName("order-name")[0].value.trim();
        let email = document.getElementsByName("order-email")[0].value.trim();
        let ic = document.getElementsByName("order-ic")[0].value.trim();
        let url = document.getElementsByName("order-url")[0].value.trim();


        let isValid = true;

        if (name === "") {
            isValid = false;
        }

        if (email === "") {
            isValid = false;
        }

        if (ic === "") {
            isValid = false;
        }

        if (isValid) {
            document.getElementById('dulezita_kontrola_objednavky').value = 'Zdá se, že uživatel není robot.';
        }

        if (!isValid) {
            document.getElementById('dulezita_kontrola_objednavky').value = '';
        }

        return isValid;
    });
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('order-form-wp');

        form.addEventListener('submit', function(event) {
            const checkbox = document.querySelector('#type-1');

            if (!checkbox.checked) {
                event.preventDefault();
                const messageElement = document.createElement('div');
                messageElement.classList.add('text-red-500', 'text-sm', 'error-message');
                messageElement.textContent = 'Je nutné souhlasit s podmínkami.';

                const parentElement = checkbox.parentElement;
                if (!parentElement.querySelector('.error-message')) {
                    parentElement.appendChild(messageElement);
                }
            }
        });
    });
</script>