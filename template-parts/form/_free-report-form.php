<div class="w-full rounded-[50px] bg-white p-5 py-12 shadow-form lg:p-12">

    <form id="<?= $form_id ?>" action="<?= $form_action ?>" method="POST" class="relative z-50 w-full">
        <input type="hidden" name="action" value="<?= $wp_action ?>">
        <input type="hidden" name="<?= $spam_control_field ?>" value="" id="<?= $spam_control_field ?>">
        <?php wp_nonce_field($nonce_name); ?>
        <div class="gap-5 mb-5">
            <input type="text" name="web" placeholder="adresa webu..." required class="h-10 w-full rounded-[10px]  px-5 py-1 text-sm font-normal leading-5 text-light-purple outline-none placeholder:text-current" />
        </div>

        <div class="grid w-full grid-cols-1 gap-5 mb-5 sm:grid-cols-2">
            <input type="text" name="name" placeholder="vaše jméno..." required class="h-10 w-full rounded-[10px]  px-5 py-1 text-sm font-normal leading-5 text-light-purple outline-none placeholder:text-current" />
            <input type="text" name="lname" placeholder="vaše příjmení..." required class="h-10 w-full rounded-[10px]  px-5 py-1 text-sm font-normal leading-5 text-light-purple outline-none placeholder:text-current" />
        </div>
        <div class="grid w-full grid-cols-1 gap-5 mb-5 sm:grid-cols-2">
            <input type="tel" name="telephone" placeholder="váš telefon..." class="h-10 w-full rounded-[10px]  px-5 py-1 text-sm font-normal leading-5 text-light-purple outline-none placeholder:text-current" />
            <input type="email" name="email" placeholder="váš email..." required class="h-10 w-full rounded-[10px]  px-5 py-1 text-sm font-normal leading-5 text-light-purple outline-none placeholder:text-current" />
        </div>

        <!------------form privacy------------>
        <div class="flex flex-col items-start justify-between w-full mt-4 gap-7 md:flex-row md:gap-4">
            <div class="w-full max-w-[205px]">
                <p class="mb-3.5 flex items-center gap-2.5 text-sm font-normal leading-5 text-light-purple">
                    <svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10 0C4.48 0 0 4.48 0 10s4.48 10 10 10 10-4.48 10-10S15.52 0 10 0Zm0 15c-.55 0-1-.45-1-1v-4c0-.55.45-1 1-1s1 .45 1 1v4c0 .55-.45 1-1 1Zm1-8H9V5h2v2Z" fill="#8078A0" />
                    </svg>Pole jsou povinná
                </p>
                <p class="flex items-center gap-2.5 text-sm font-normal leading-5 text-light-purple">
                    <svg width="16" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M13.333 6.667h-.952V4.762A4.764 4.764 0 0 0 7.619 0a4.764 4.764 0 0 0-4.762 4.762v1.905h-.952A1.91 1.91 0 0 0 0 8.57v9.524A1.91 1.91 0 0 0 1.905 20h11.428a1.91 1.91 0 0 0 1.905-1.905V8.571a1.91 1.91 0 0 0-1.905-1.904ZM7.62 15.238a1.91 1.91 0 0 1-1.905-1.905A1.91 1.91 0 0 1 7.62 11.43a1.91 1.91 0 0 1 1.905 1.904 1.91 1.91 0 0 1-1.905 1.905ZM4.762 6.667V4.762a2.853 2.853 0 0 1 2.857-2.857 2.853 2.853 0 0 1 2.857 2.857v1.905H4.762Z" fill="#8078A0" />
                    </svg>SSL zabezpečení
                </p>
            </div>
            <button class="flex h-[62px] w-full max-w-full items-center justify-center gap-2.5 rounded-[20px] bg-light-purple p-2 text-base font-bold leading-normal text-white sm:max-w-[314px]">Odeslat žádost o bezplatný report</button>
        </div>
    </form>
</div>

<script>
    document.getElementById('<?= $form_id ?>').addEventListener('submit', function(event) {        
        let name = document.getElementsByName("name")[0].value.trim();
        let email = document.getElementsByName("email")[0].value.trim();

        let isValid = true;

        if (name === "") {
            isValid = false;
        }

        if (email === "") {
            isValid = false;
        }


        if (isValid) {
            document.getElementById('<?= $spam_control_field ?>').value = '<?= $spam_control_string ?>';
        }

        if (!isValid) {
            document.getElementById('<?= $spam_control_field ?>').value = '';
        }
        return isValid;
    });
</script>