/**
 * The JavaScript code you place here will be processed by esbuild, and the
 * output file will be created at `../theme/js/script.min.js` and enqueued in
 * `../theme/functions.php`.
 *
 * For esbuild documentation, please see:
 * https://esbuild.github.io/
 */
import Swiper, { Autoplay } from 'swiper';
import {
	Navigation,
	Pagination,
	Scrollbar,
	EffectCoverflow,
	EffectFade,
	FreeMode,
	Thumbs,
} from 'swiper';

// Navbar
document.addEventListener('DOMContentLoaded', () => {
	const navBtn = document.querySelector('.nav-toggle');
	const navbar = document.querySelector('.nav-menu');
	navBtn.addEventListener('click', () => {
		navbar.classList.toggle('nav-menu-active');
		navBtn.classList.toggle('bg-light-purple');
		navBtn.firstElementChild.classList.toggle('text-theme-pink');
	})
});

const facilityswiper = new Swiper('.facility-slider', {
	modules: [Navigation, Pagination, Scrollbar],
	loop: false,
	spaceBetween: 120,
	navigation: {
		nextEl: '.facility-right',
		prevEl: '.facility-left',
	},
	pagination: {
		el: '.facility-dots',
		clickable: true,
		bulletClass: 'slider-dot-light slider-dot',
		bulletActiveClass: 'slider-dot-active',
	},
});

const mezitimswiper = new Swiper('.mezitim-slider', {
	modules: [Navigation, Pagination, Scrollbar],
	loop: true,
	spaceBetween: 12,
	slidesPerView: 1.5,
	slidesPerGroup: 1,

	breakpoints: {
		640: {
			slidesPerView: 3,
			slidesPerGroup: 3,
		},
		1024: {
			slidesPerView: 5,
			slidesPerGroup: 5,
		},
	},

	pagination: {
		el: '.mezitim-dots',
		clickable: true,
		bulletClass: 'slider-dot-light slider-dot',
		bulletActiveClass: 'slider-dot-active',
	},
});

const vybraneswiper = new Swiper('.vybrane-reference', {
	modules: [Navigation, Pagination, Scrollbar, EffectCoverflow, FreeMode],
	loop: true,
	slidesPerView: 1,
	// initialSlide: 3,
	simulateTouch: true,
	allowTouchMove: true,
	freeMode: true,
	effect: 'coverflow',
	watchSlidesProgress: true,

	coverflowEffect: {
		rotate: 0,
		slideShadows: false,
		depth: 450,
		modifier: 1.5,
		stretch: 80,
	},
	breakpoints: {
		768: {
			coverflowEffect: {
				rotate: 0,
				slideShadows: false,
				depth: 350,
				modifier: 1,
				stretch: 80,
			},
		},
	},

	on: {
		init: function () {
			updateWebsiteLiveLink(this);
		},
		activeIndexChange: function () {
			updateWebsiteLiveLink(this);
		},
	},
});

function updateWebsiteLiveLink(slider) {
	let webBtn = document.querySelector('#live-url-link');
	let webLink =
		slider.slides[slider.activeIndex].getAttribute('live-website-link');
	if (webLink) {
		webBtn.classList.remove('hide-it');
		webBtn.setAttribute('href', webLink);
	} else {
		webBtn.classList.add('hide-it');
	}
}

const ellipseswiper = new Swiper('.ellipse-slider', {
	modules: [Navigation, Pagination, Scrollbar, Thumbs, Autoplay],
	autoplay: {
		delay: 5000,
		disableOnInteraction: false,
	},
	loop: false,
	watchSlidesProgress: true,
	initialSlide: 3,

	// slidesPerView: 1,
	navigation: {
		prevEl: '.ellipse-prev',
		nextEl: '.ellipse-next',
	},
	thumbs: {
		swiper: vybraneswiper,
	},
});

const heroSwiper = new Swiper('.hero-slider', {
	modules: [Navigation, Pagination, Scrollbar, EffectFade, Autoplay],
	autoplay: {
		delay: 5000,
		disableOnInteraction: false,
	},
	loop: true,
	slidesPerView: 1,
	spaceBetween: 600,
	effect: 'fade',
	parallex: true,
	fadeEffect: {
		crossFade: true,
	},
	pagination: {
		el: '.hero-dots',
		clickable: true,
		bulletClass: 'slider-dot',
		bulletActiveClass: 'slider-dot-active',
	},
});

// nase-slider
const naseSwiper = new Swiper('.nase-slider', {
	modules: [Navigation, Pagination, Scrollbar],
	loop: false,
	spaceBetween: 20,
	slidesPerView: 1.2,

	breakpoints: {
		360: {
			slidesPerView: 1.5,
		},
		550: {
			slidesPerView: 2,
		},
		1024: {
			slidesPerView: 5,
		},
	},

	pagination: {
		el: '.nase-dots',
		clickable: true,
		bulletClass: 'slider-dot',
		bulletActiveClass: 'slider-dot-active',
	},
});

// nase-slider
const cardSwiper = new Swiper('.card-slider', {
	modules: [Navigation, Pagination, Scrollbar],
	loop: false,
	spaceBetween: 20,
	slidesPerView: 1.2,

	breakpoints: {
		360: {
			slidesPerView: 1.5,
		},
		550: {
			slidesPerView: 2,
		},
		1024: {
			slidesPerView: 5,
		},
	},

	pagination: {
		el: '.nase-dots',
		clickable: true,
		bulletClass: 'slider-dot',
		bulletActiveClass: 'slider-dot-active',
	},
});

var tabButtons = new Swiper('.pop-tags', {
	modules: [Navigation],
	slidesPerView: 'auto',
	navigation: {
		nextEl: '.pop-next',
		prevEl: '.pop-prev',
	},
});

var tabContent = new Swiper('.pop-content', {
	modules: [Thumbs],
	slidesPerView: 1,
	slideThumbActiveClass: 'group',
	thumbs: {
		swiper: tabButtons,
		slideThumbActiveClass: 'group',
	},
});



let ballSwiper = new Swiper('.ball-slider', {
	modules: [Pagination],
	loop: true,
	spaceBetween: 48,
	slidesPerView: 1.2,
	breakpoints: {
		550: {
			slidesPerView: 2,
		},
		1024: {
			slidesPerView: 4,
		},
	},

	pagination: {
		el: '.partner-dots',
		clickable: true,
		bulletClass: 'slider-dot-light slider-dot',
		bulletActiveClass: 'slider-dot-active',
	},
});

document.addEventListener('DOMContentLoaded', () => {
	if (window.location.href.includes('o-nas')) {
		const showMoreButton = document.querySelector('.team-btn');
		const hiddenCover = document.querySelector('.team-cover');

		showMoreButton.addEventListener('click', () => {
			const hiddenMembers = document.querySelectorAll('.uw-team .hidden');
			hiddenMembers.forEach((member) => {
				member.classList.remove('hidden');
			});
			hiddenCover.style.height = '0px';
			showMoreButton.style.display = 'none';
		});
	}
})

document.addEventListener('DOMContentLoaded', () => {
	const boxes = document.querySelectorAll('.box');
	const allContents = document.querySelectorAll('.content');

	boxes.forEach((box) => {
		box.addEventListener('click', function (e) {
			e.preventDefault();
			allContents.forEach((content) => content.classList.add('hidden'));
			boxes.forEach((innerBox) => {
				innerBox.classList.remove('active');
			});
			box.classList.add('active');

			const contentId = box.getAttribute('data-content-id');
			const contentToShow = document.getElementById(contentId);
			contentToShow.classList.remove('hidden');
		});
	});
});

if (window.location.href.includes('/nase-reference/')) {
	document.addEventListener('DOMContentLoaded', function () {
		const moreBtn = document.getElementById('referenceButton');
		const hiddenItems = document.querySelectorAll('.hidden');

		moreBtn.addEventListener('click', function () {
			hiddenItems.forEach((item) => {
				item.classList.remove('hidden');
			});
			moreBtn.style.display = 'none';
		});
	});
}

document.addEventListener('DOMContentLoaded', function () {
	if (window.location.href.includes('nase-sluzby/sprava-wordpress-webu/')) {
		const contactBtns = document.querySelectorAll('.contact-btn');
		const bglayer = document.querySelector('.black-layer');
		const contactPopup = document.querySelector('.contact-popup');
		const contactclose = document.querySelector('.contact-close');
		const body = document.body;

		contactclose.addEventListener('click', () => {
			bglayer.classList.add('hidden');
			contactPopup.classList.add('hidden');
			contactPopup.classList.remove('flex');
			body.classList.remove('h-screen');
			body.classList.remove('overflow-hidden');
		});

		contactBtns.forEach((btn) => {
			btn.addEventListener('click', (event) => {
				event.preventDefault();
				bglayer.classList.toggle('hidden');
				contactPopup.classList.toggle('hidden');
				contactPopup.classList.toggle('flex');
				body.classList.toggle('overflow-hidden', 'h-screen');
			});
		});

		bglayer.addEventListener('click', () => {
			bglayer.classList.add('hidden');
			contactPopup.classList.add('hidden');
			contactPopup.classList.remove('flex');
			body.classList.remove('h-screen');
			body.classList.remove('overflow-hidden');
		});

		const objeBtns = document.querySelectorAll('.obje-btn');
		const bglayerobje = document.querySelector('.obje-black-layer');
		const objePopup = document.querySelector('.obje-popup');
		const objeclose = document.querySelector('.obje-close');

		objeclose.addEventListener('click', () => {
			bglayerobje.classList.add('hidden');
			objePopup.classList.add('hidden');
			objePopup.classList.remove('grid');
			body.classList.remove('h-screen');
			body.classList.remove('overflow-hidden');
		});

		objeBtns.forEach((btn) => {
			btn.addEventListener('click', (event) => {
				event.preventDefault();
				bglayerobje.classList.toggle('hidden');
				objePopup.classList.toggle('hidden');
				objePopup.classList.toggle('grid');
				body.classList.toggle('overflow-hidden', 'h-screen');
			});
		});

		bglayerobje.addEventListener('click', () => {
			bglayerobje.classList.add('hidden');
			objePopup.classList.add('hidden');
			objePopup.classList.remove('grid');
			body.classList.remove('h-screen');
			body.classList.remove('overflow-hidden');
		});

		let planBoxes = document.querySelectorAll('.plan-box');
		let input = document.getElementById('selected-plan');

		planBoxes.forEach((box) => {
			box.addEventListener('click', () => {
				let planName = box.getAttribute('data-plan');
				input.placeholder = planName;
				input.value = planName;
			});
		});

		document
			.getElementById('box-starter')
			.addEventListener('click', function () {
				updatePlanDetails(
					'Objednávka STARTOVACÍ VÝBAVA',
					'2 900 Kč/měs.',
					'Nutný servis pro spolehlivý let'
				);
			});

		document
			.getElementById('box-standard')
			.addEventListener('click', function () {
				updatePlanDetails(
					'Objednávka ZÁKLADNÍ VÝBAVA',
					'4 800 Kč/měs.',
					'Rozšířený servis pro delší dolet'
				);
			});

		document
			.getElementById('box-premium')
			.addEventListener('click', function () {
				updatePlanDetails(
					'Objednávka STANDARDNÍ VÝBAVA',
					'9 800 Kč/měs.',
					'Turbo let zaručen'
				);
			});

		function updatePlanDetails(title, subtitle, description) {
			document.getElementById('plan-title').textContent = title;
			document.getElementById('plan-subtitle').textContent = subtitle;
			document.getElementById('plan-description').textContent =
				description;
		}
	}
});

if (window.location.pathname === '/nase-sluzby/') {
	document.addEventListener('DOMContentLoaded', function () {
		var tlacitko = document.getElementById('button-scroll-to');
		var kotva = document.getElementById('anchor-for-scroll');

		tlacitko.addEventListener('click', function (e) {
			e.preventDefault();

			kotva.scrollIntoView({ behavior: 'smooth' });
		});
	});
}
