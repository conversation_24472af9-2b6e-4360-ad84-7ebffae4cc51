<!doctype html>
<html <?php

use Umimeweby\UWTheme\CustomPostType\Reference_Custom_Post_Type;
use Umimeweby\UWTheme\CustomPostType\UW_Service_Custom_Post_Type;
use Umimeweby\UWTheme\Taxonomies\UW_Service_Custom_Taxonomy;

 language_attributes(); ?>>

<head>
    <?php get_template_part('template-parts/components/_head') ?>
</head>

<body <?php body_class(); ?>>

    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MN5ZJ4V" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->

    <?php wp_body_open(); ?>
    <!-- ::::::::::::container:::::::::::: -->
    <div class="w-full overflow-hidden">
        <!-- ::::::::::::header:::::::::::: -->

        <?php
        $currentPage = get_post()?->post_name;




        $template = match (true) {
            is_tax('', UW_Service_Custom_Taxonomy::TAXONOMY_KEY) => 'taxonomy_archives/_header_uw-service-category',
            is_post_type_archive( Reference_Custom_Post_Type::CPT_KEY ) => 'cpt_archives/_header_nase_reference_archive',
            is_post_type_archive( UW_Service_Custom_Post_Type::CPT_KEY ) => 'cpt_archives/_header_sluzby',            
            is_single() && Reference_Custom_Post_Type::CPT_KEY == get_post_type() => '_header_single_nase_reference',
            is_single() && UW_Service_Custom_Post_Type::CPT_KEY == get_post_type() => 'singles/_header_single_uw_services',
            is_home() || is_archive() || is_search() || is_404() => '_header_blog_single',
            is_single() && 'post' == get_post_type() => '_header_blog_single',
            is_page_template('page-templates/page-with-hero.php') => '/pages/_header_page_with_hero', 
            $currentPage === 'homepage' => '/pages/_header_homepage',
            $currentPage === 'podekovani' => '_header_thanks',
            $currentPage === 'email-potvrzeni' => '_header_email_confirmation',
            in_array($currentPage, ['landing-a', 'landing-b', 'landing-c']) => '_header_landing',
            default => '_header'
        };

        get_template_part('template-parts/components/header-section/' . $template);
        ?>
        <!-- ::::::::::::end - header:::::::::::: -->