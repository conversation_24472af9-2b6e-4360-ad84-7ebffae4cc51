<?php
/**
 * The main template file
 *
 * @package Umíme_Weby
 */

get_header();

$categories = get_categories(['parent' => 0]);
$category_archive = is_category();
$tag_archive = is_tag();

// ID of current category/tag for filtering
$archive_term_id = $category_archive ? get_queried_object_id() : ($tag_archive ? get_queried_object_id() : 0);
?>

    <main class="w-full px-4 pt-4 mb-12 md:mb-0 md:px-5 md:pt-0">

        <?php get_template_part('template-parts/components/blog/_blog-list-hero_mobile'); ?>

        <div class="relative z-30 mx-auto w-full max-w-[1050px] bg-white xl:max-w-[1172px]">
            <div class="flex items-start justify-between w-full gap-3 mb-8">
                <?php
                // Pagination + search
                $paged = max(1, get_query_var('paged'));
                $search = get_search_query();
                $args = [
                    'post_type' => ['post', 'nase-sluzby'],
                    's' => $search,
                    'paged' => $paged,
                    'orderby' => 'date',
                    'order' => 'DESC',
                ];
                if ($category_archive) {
                    $args['cat'] = $archive_term_id;
                } elseif ($tag_archive) {
                    $args['tag_id'] = $archive_term_id;
                }
                $wp_query = new WP_Query($args);
                ?>

                <?php if ($wp_query->have_posts()) : ?>
                    <div class="w-full min-w-full">
                        <?php if ($category_archive || $tag_archive) : ?>
                            <div class="max-w-5xl mx-auto text-center mb-6">
                                <div class="mx-auto block h-full w-full max-w-5xl mb-10 text-center">
                                    <div class="max-w-xl mx-auto p-6 bg-white border border-purple-400 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                                        <div class="heading-secondary mb-4 text-2xl text-theme-purple md:text-40">
                                            <?php
                                            if ($category_archive) {
                                                printf('Rubrika: %s', esc_html(single_cat_title('', false)));
                                            } else if ($tag_archive) {
                                                printf('Štítek: %s', esc_html(single_tag_title('', false)));
                                            }
                                            ?>
                                        </div>
                                        <p class="text-base leading-6 uppercase md:text-lg">
                                            aneb Trendy a tipy pro váš online bysnys, webové stránky, webové aplikace,
                                            e-mailing a copywriting
                                        </p>

                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php while ($wp_query->have_posts()) : $wp_query->the_post(); ?>
                            <div class="w-full mb-10">
                                <?php if ($category_archive || $tag_archive) : ?>
                                    <?php get_template_part('template-parts/components/blog/_archive_list'); ?>
                                <?php else : ?>
                                    <?php if (has_post_thumbnail() && get_post_type() !== 'nase-sluzby') : ?>
                                        <img
                                                src="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'large')); ?>"
                                                alt="<?php the_title_attribute(); ?>"
                                                class="h-auto w-full rounded-[20px] object-cover shadow-blog"
                                        />
                                    <?php else : ?>
                                        <div class="h-[300px] w-full bg-light-gray-100 rounded-[20px]"></div>
                                    <?php endif; ?>

                                    <div class="mt-8 mb-6">
                                        <div class="flex flex-wrap gap-2.5">
                                            <?php foreach (get_the_category() as $cat) : ?>
                                                <a href="<?php echo esc_url(get_category_link($cat)); ?>"
                                                   class="py-2.5 px-5 bg-light-gray-100 rounded-full text-sm text-light-gray-200">
                                                    <?php echo esc_html($cat->name); ?>
                                                </a>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>

                                    <h2 class="text-3xl font-bold text-theme-purple mb-4">
                                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                    </h2>
                                    <div class="flex items-center gap-4 mb-6">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none">
                                            <path fill="#D9D9D9"
                                                  d="M10 0C4.5 0 0 4.5 0 10s4.5 10 10 10 10-4.5 10-10S15.5 0 10 0Zm4.2 14.2L9 11V5h1.5v5.2l4.5 2.7-.8 1.3Z"/>
                                        </svg>
                                        <time datetime="<?php echo get_the_date('c'); ?>"
                                              class="text-base text-[#d9d9d9]">
                                            <?php echo get_the_date('d F, Y'); ?>
                                        </time>
                                    </div>
                                    <p class="text-base text-light-purple mb-[54px]">
                                        <?php echo wp_trim_words(get_the_content(), 40); ?>
                                    </p>
                                <?php endif; ?>
                            </div>


                        <?php endwhile;
                        wp_reset_postdata(); ?>
                    </div>
                <?php else : ?> --
                    <p class="font-bold text-theme-purple text-xl mt-1">
                        Dle zadaného dotazu jsme nenašli žádné příspěvky. Zkuste zadat jiný text.
                    </p>
                <?php endif; ?>
            </div>

        </div><!-- /.relative z-30 -->
        <?php get_template_part('template-parts/components/blog/_index_pagination_mobile');
        ?>
        <?php get_template_part('template-parts/components/blog/_index_pagination_desktop');
        ?>
    </main>

<?php
get_footer();