<?php

namespace Umimeweby\UWTheme\Settings;
class UW_Settings_Page
{

    const PREFIX = 'uw_settings_';

    const SETTINGS_PAGE_MAIN = 'uw-settings';
    const SETTINGS_CONTACT_PAGE_MAIN = 'uw-settings-contact';
    const SETTINGS_OUR_SERVICES_PAGE_MAIN = 'uw-settings-our_services';
    const SETTINGS_CMS_WORDPRESS_PAGE_MAIN = 'uw-settings-cms_wordpress';

    const OPTION_MAIN_SETTINGS = 'logo-settings';
    const OPTION_CONTACT_SETTING = 'contact-setting';
    const OPTION_OUR_SERVICES_SETTING = 'our-services-setting';
    const OPTION_CMS_WORDPRESS_SETTING = 'cms-wordpress-setting';

    const FIELD_CONTACT_LATITUDE = 'contact_latitude';
    const FIELD_CONTACT_LONGITUDE = 'contact_long';
    const FIELD_CONTACT_COMPANY_REGISTERED = 'contact_company_registered';

    const FIELD_UW_LOGO_MAIN = 'logo_main';
    const FIELD_LOGO_PAGELINKED = 'logo_page_linked';
    const FIELD_GROUP_HEADER_CONTENT_CAROUSEL = 'group_header';
    const FIELD_HEADER_CONTENT_TITLE = 'header_content_title';
    const FIELD_HEADER_CONTENT_SUBTITLE = 'header_content_subtitle';
    const FIELD_HEADER_CONTENT_DESCRIPTION = 'header_content_description';
    const FIELD_HEADER_CONTENT_BTN_TEXT = 'header_content_btn_text';
    const FIELD_HEADER_CONTENT_BTN_LINK = 'header_content_btn_link';
    const FIELD_HEADER_CONTENT_CAROUSEL_IMAGE = 'header_content_carousel_image';
    const FIELD_HEADER_CONTENT_UNDER_CAROUSEL_TITLE = 'header_content_under_carousel_title';
    const FIELD_HEADER_CONTENT_UNDER_CAROUSEL_DESCRIPTION = 'header_content_under_carousel_description';
    const FIELD_HEADER_CONTENT_UNDER_CAROUSEL_BTN_TEXT = 'heading_content_under_carousel_btn_text';
    const FIELD_HEADER_CONTENT_UNDER_CAROUSEL_BTN_LINK = 'heading_content_under_carousel_btn_link';

    const FIELD_CONTACT_ADRESS_FIELD = 'contact_address';
    const FIELD_CONTACT_TEL_FIELD = 'tel_field';
    const FIELD_CONTACT_EMAIL_FIELD = 'email_field';

    const OUR_SERVICES_FIELD_GROUP = 'group';
    const OUR_SERVICES_FIELD_LINK = 'group_link';    
    const OUR_SERVICES_FIELD_ANOTHER_SERVICES_TITLE = 'another_services_title';
    const OUR_SERVICES_FIELD_ANOTHER_SERVICES_DESCRIPTION = 'another_services_description';
    const OUR_SERVICES_FIELD_LEFT_TO_BUTTON_TITLE = 'left_to_button_title';
    const OUR_SERVICES_FIELD_LEFT_TO_BUTTON_DESCRIPTION = 'left_to_button_description';

    const WORDPRESS_CMS_REFERENCE_FIELD_TITLE = 'title';
    const WORDPRESS_CMS_REFERENCE_FIELD_DESCRIPTION = 'description';
    const WORDPRESS_CMS_REFERENCE_FIELD_GROUP = 'group';
    const WORDPRESS_CMS_REFERENCE_GROUP_IMAGE = 'group_image';
    const WORDPRESS_CMS_REFERENCE_FIELD_BTN_TEXT = 'btn_text';


    public function __construct()
    {
        add_filter('mb_settings_pages', [$this, 'create_settings_pages']);
        add_filter('rwmb_meta_boxes', [$this, 'define_settings_fields']);
        add_filter('mb_settings_pages', [$this, 'create_contact_settings_pages']);
        add_filter('rwmb_meta_boxes', [$this, 'define_settings_contact_fields']);
        add_filter('mb_settings_pages', [$this, 'create_our_services_settings_pages']);
        add_filter('rwmb_meta_boxes', [$this, 'define_settings_our_services_fields']);
        add_filter('mb_settings_pages', [$this, 'create_cms_wordpress_settings_pages']);
        add_filter('rwmb_meta_boxes', [$this, 'custom_fields_cms_wordpress_reference']);
    }

    /**
     * @param array<mixed> $setting_pages
     */
    public function create_settings_pages($setting_pages): mixed
    {
        $setting_pages[] = [
            'menu_title' => __('UW Nastavení', 'uw-web'),
            'id' => self::SETTINGS_PAGE_MAIN,
            'option_name' => self::OPTION_MAIN_SETTINGS,
            'position' => 25,
            'style' => 'no-boxes',
            'parent' => '',
            'columns' => 1,
            'icon-url' => 'dashicons-admin-generic',
            'capability'      => 'delete_others_pages',
        ];
        return $setting_pages;
    }

    /**
     * @param array<mixed> $settings_contact_pages
     */
    public function create_contact_settings_pages(mixed $settings_contact_pages): mixed
    {
        $settings_contact_pages[] = [
            'menu_title' => __('Kontakt', 'uw-child-theme'),
            'id' => self::SETTINGS_CONTACT_PAGE_MAIN,
            'option_name' => self::OPTION_CONTACT_SETTING,
            'position' => 25,
            'parent' => self::SETTINGS_PAGE_MAIN,
            'style' => 'no-boxes',
            'columns' => 1,
            'capability'      => 'delete_others_pages',
        ];

        return $settings_contact_pages;
    }

    /**
     * @param array<mixed> $settings_our_services_pages
     */
    public function create_our_services_settings_pages(mixed $settings_our_services_pages): mixed
    {
        $settings_our_services_pages[] = [
            'menu_title' => __('Naše služby', 'uw-child-theme'),
            'id' => self::SETTINGS_OUR_SERVICES_PAGE_MAIN,
            'option_name' => self::OPTION_OUR_SERVICES_SETTING,
            'position' => 25,
            'parent' => self::SETTINGS_PAGE_MAIN,
            'style' => 'no-boxes',
            'columns' => 1,
            'capability'      => 'delete_others_pages',
        ];

        return $settings_our_services_pages;
    }

    /**
     * @param array<mixed> $settings_cms_wordpress
     */
    public function create_cms_wordpress_settings_pages(mixed $settings_cms_wordpress): mixed
    {
        $settings_cms_wordpress[] = [
            'menu_title' => __('CMS Wordpress Reference', 'uw-child-theme'),
            'id' => self::SETTINGS_CMS_WORDPRESS_PAGE_MAIN,
            'option_name' => self::OPTION_CMS_WORDPRESS_SETTING,
            'position' => 25,
            'parent' => self::SETTINGS_PAGE_MAIN,
            'style' => 'no-boxes',
            'columns' => 1,
            'capability'      => 'delete_others_pages',
        ];

        return $settings_cms_wordpress;
    }

    /**
     * @param array<mixed> $meta_boxes
     */
    public function define_settings_fields($meta_boxes): mixed
    {
        $meta_boxes[] = [
            'title' => __('UW Main Settings', 'uw-web'),
            'settings_pages' => ['uw-settings'],
            'fields' => [
                [
                    'name' => __('Hlavní logo', 'uw-web'),
                    'id' => self::PREFIX . self::FIELD_UW_LOGO_MAIN,
                    'type' => 'image_advanced',
                ],
                [
                    'name' => __('Header Content - Obsah v Carousel', 'uw-child-theme'),
                    'id' => self::PREFIX . self::FIELD_GROUP_HEADER_CONTENT_CAROUSEL,
                    'type' => 'group',
                    'clone' => true,
                    'collapsible' => true,
                    'sort_clone' => true,
                    'default_state' => 'collapsed',
                    'group_title' => 'Carousel {#}',
                    'fields' => [
                        [
                            'name' => __('Nadpis', 'uw-child-theme'),
                            'id' => self::PREFIX . self::FIELD_HEADER_CONTENT_TITLE,
                            'type' => 'textarea'
                        ],
                        [
                            'name' => __('Podnadpis', 'uw-child-theme'),
                            'id' => self::PREFIX . self::FIELD_HEADER_CONTENT_SUBTITLE,
                            'type' => 'text'
                        ],
                        [
                            'name' => __('Popis', 'uw-child-theme'),
                            'id' => self::PREFIX . self::FIELD_HEADER_CONTENT_DESCRIPTION,
                            'type' => 'textarea'
                        ],
                        [
                            'name' => __('Text v tlačítku', 'uw-child-theme'),
                            'id' => self::PREFIX . self::FIELD_HEADER_CONTENT_BTN_TEXT,
                            'type' => 'text'
                        ],
                        [
                            'name' => __('Odkaz v tlačítku', 'uw-child-theme'),
                            'id' => self::PREFIX . self::FIELD_HEADER_CONTENT_BTN_LINK,
                            'type' => 'post',
                            'post_type' => ['page', 'nase-reference'],
                            'field_type' => 'select_advanced',
                            'placeholder' => 'Select a page',
                            'query_args' => [
                                'post_status' => 'publish',
                                'posts_per_page' => -1,
                            ],
                        ],
                        [
                            'name' => __('Obrázek v carousel', 'uw-web'),
                            'id' => self::PREFIX . self::FIELD_HEADER_CONTENT_CAROUSEL_IMAGE,
                            'type' => 'image_advanced',
                        ],
                    ]
                ],
                [
                    'name' => __('Nadpis pod carousel', 'uw-child-theme'),
                    'id' => self::PREFIX . self::FIELD_HEADER_CONTENT_UNDER_CAROUSEL_TITLE,
                    'type' => 'text'
                ],
                [
                    'name'  => __('Popis pod carousel', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::FIELD_HEADER_CONTENT_UNDER_CAROUSEL_DESCRIPTION,
                    'type'    => 'wysiwyg',
                    'raw'     => true,
                    'options' => [
                        'textarea_rows'       => 2,
                        'media_buttons'       => false,
                    ],
                ],
                [
                    'name' => __('Text v tlačítku', 'uw-child-theme'),
                    'id' => self::PREFIX . self::FIELD_HEADER_CONTENT_UNDER_CAROUSEL_BTN_TEXT,
                    'type' => 'text'
                ],
                [
                    'name'  => __('Odkaz v tlačítku', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::FIELD_HEADER_CONTENT_UNDER_CAROUSEL_BTN_LINK,
                    'type'        => 'post',
                    'post_type' => 'page',
                    'field_type'  => 'select_advanced',
                    'placeholder' => 'Vyberte si stránku',
                    'query_args'  => [
                        'post_status'    => 'publish',
                        'posts_per_page' => - 1,
                    ],
                ],
            ]
        ];

        return $meta_boxes;
    }

    public function define_settings_contact_fields(mixed $meta_define_contact): mixed
    {
        $meta_define_contact[] = [
            'title' => __('Kontakt settings', 'uw-child-theme'),
            'settings_pages' => ['uw-settings-contact'],
            'fields' => [
                [
                    'name' => __('Adresa', 'uw-child-theme'),
                    'id' => self::PREFIX . self::FIELD_CONTACT_ADRESS_FIELD,
                    'type' => 'textarea'
                ],
                [
                    'name' => __('Telefonní číslo', 'uw-child-theme'),
                    'id' => self::PREFIX . self::FIELD_CONTACT_TEL_FIELD,
                    'type' => 'text'
                ],
                [
                    'name' => __('Email', 'uw-child-theme'),
                    'id' => self::PREFIX . self::FIELD_CONTACT_EMAIL_FIELD,
                    'type' => 'text'
                ],
                [
                    'name' => __('Latitude', 'uw-child-theme'),
                    'id' => self::PREFIX . self::FIELD_CONTACT_LATITUDE,
                    'type' => 'text'
                ],
                [
                    'name' => __('Longitude', 'uw-child-theme'),
                    'id' => self::PREFIX . self::FIELD_CONTACT_LONGITUDE,
                    'type' => 'text'
                ],
                [
                    'name' => __('Společnost je zapsána', 'uw-child-theme'),
                    'id' => self::PREFIX . self::FIELD_CONTACT_COMPANY_REGISTERED,
                    'type' => 'textarea'
                ],
            ]
        ];

        return $meta_define_contact;
    }

    public function define_settings_our_services_fields(mixed $meta_define_our_services): mixed
    {
        $meta_define_our_services[] = [
            'title' => __('Kontakt settings', 'uw-child-theme'),
            'settings_pages' => ['uw-settings-our_services'],
            'fields' => [
                [
                    'name'  => __('Nadpis sekce - Naše další služby', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::OUR_SERVICES_FIELD_ANOTHER_SERVICES_TITLE,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Popis sekce - Naše další služby', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::OUR_SERVICES_FIELD_ANOTHER_SERVICES_DESCRIPTION,
                    'type'  => 'textarea',
                ],
                [
                    'name'          => __( 'Reference', 'uw-child-theme' ),
                    'id'            => self::PREFIX . self::OUR_SERVICES_FIELD_GROUP,
                    'type'          => 'group',
                    'clone'         => true,
                    'collapsible'   => true,
                    'sort_clone'    => true,
                    'default_state' => 'collapsed',
                    'group_title'   => 'Naše služby {#}',
                    'fields'        => [
                        [
                            'name'  => __('Naše služby - odkaz', 'uw-child-theme'),
                            'id'    => self::PREFIX . self::OUR_SERVICES_FIELD_LINK,
                            'type'        => 'post',
                            'post_type' => 'nase-sluzby',
                            'field_type'  => 'select_advanced',
                            'placeholder' => 'Vyberte si stránku',
                            'query_args'  => [
                                'post_status'    => 'publish',
                                'posts_per_page' => - 1,
                            ],
                        ],
                    ]
                ],
                [
                    'name'  => __('Nadpis sekce - vlevo od tlačítka kontaktujte nás', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::OUR_SERVICES_FIELD_LEFT_TO_BUTTON_TITLE,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Popis sekce - vlevo od tlačítka kontaktujte nás', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::OUR_SERVICES_FIELD_LEFT_TO_BUTTON_DESCRIPTION,
                    'type'    => 'wysiwyg',
                    'raw'     => true,
                    'options' => [
                        'textarea_rows'       => 2,
                        'media_buttons'       => false,
                    ],
                ],
            ]
        ];
        return $meta_define_our_services;
    }

    public function custom_fields_cms_wordpress_reference(mixed $meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => __('CMS Wordpress reference', 'uw-child-theme'),
            'settings_pages' => ['uw-settings-cms_wordpress'],
            'fields' => [
                [
                    'name' => __('Nadpis sekce', 'uw-child-theme'),
                    'id' => self::PREFIX . self::WORDPRESS_CMS_REFERENCE_FIELD_TITLE,
                    'type' => 'text',
                ],
                [
                    'name' => __('Popis sekce', 'uw-child-theme'),
                    'id' => self::PREFIX . self::WORDPRESS_CMS_REFERENCE_FIELD_DESCRIPTION,
                    'type' => 'textarea',
                ],
                [
                    'name' => __('Reference', 'uw-child-theme'),
                    'id' => self::PREFIX . self::WORDPRESS_CMS_REFERENCE_FIELD_GROUP,
                    'type' => 'group',
                    'clone' => true,
                    'collapsible' => true,
                    'sort_clone' => true,
                    'default_state' => 'collapsed',
                    'group_title' => 'Reference {#}',
                    'fields' => [
                        [
                            'name'             => __( 'Obrázek', 'uw-child-theme' ),
                            'id'               => self::PREFIX . self::WORDPRESS_CMS_REFERENCE_GROUP_IMAGE,
                            'type'             => 'image_advanced',
                            'force_delete'     => false,
                            'max_file_uploads' => 1,
                            'max_clone' => 1,
                            'max_status'       => false,
                        ],
                    ]
                ],
                [
                    'name' => __('Text tlačítka', 'uw-child-theme'),
                    'id' => self::PREFIX . self::WORDPRESS_CMS_REFERENCE_FIELD_BTN_TEXT,
                    'type' => 'text',
                ],
            ]
        ];
        return $meta_boxes;
    }


}