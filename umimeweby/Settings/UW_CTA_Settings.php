<?php

namespace Umimeweby\UWTheme\Settings;

class UW_CTA_Settings
{
    const SETTINGS_CTA = 'uw-settings-cta';
    const OPTION_CTA_SETTING = 'uw-cta-setting';

    const FIELD_CTA_ABOVE_PAGINATION_BLOG_LIST = 'above_pagination';



    public function __construct()
    {
     
        add_filter('mb_settings_pages', [$this, 'create_cta_settings_pages']);
        add_filter('rwmb_meta_boxes', [$this, 'define_settings_cta_fields']);
     
    }

    /**
     * @param array<mixed> $settings_cta_pages
     */
    public function create_cta_settings_pages(mixed $settings_cta_pages): mixed
    {
        $settings_cta_pages[] = [
            'menu_title' => __('CTA sekce', 'uw-child-theme'),
            'id' => self::SETTINGS_CTA,
            'option_name' => self::OPTION_CTA_SETTING,
            'position' => 25,
            'parent' => UW_Settings_Page::SETTINGS_PAGE_MAIN,
            'style' => 'no-boxes',
            'columns' => 1,
            'capability'      => 'delete_others_pages',
        ];

        return $settings_cta_pages;
    }

    public function define_settings_cta_fields(mixed $meta_define_cta): mixed
    {
        $meta_define_cta[] = [
            'title' => __('CTA Short kódy', 'uw-child-theme'),
            'settings_pages' => [self::SETTINGS_CTA],
            'fields' => [
                [
                    'id'      => UW_Settings_Page::PREFIX . self::FIELD_CTA_ABOVE_PAGINATION_BLOG_LIST,
                    'name'    => __('BlogList Nad stránkováním', 'uw-child-theme'),
                    'type'    => 'text_list',
                    'clone' => true,
                    'options' => [
                        'short-code-name'      => 'název shortkódu bez []',
                    ],
                ],
                [
                    'type' => 'divider',
                ],
            ]
        ];

        return $meta_define_cta;
    }
}
