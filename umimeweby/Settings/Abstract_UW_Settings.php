<?php

namespace Umimeweby\UWTheme\Settings;

abstract class Abstract_UW_Settings
{

    protected bool $isActive =  false;

    /**
     * 
     *  
     * 
     * @param mixed $settings_pages 
     * @return mixed 
     */
    abstract public function create_settings_page(mixed $settings_pages): mixed;
    abstract public function define_settings_fields(mixed $meta_boxes): mixed;

    abstract public function get_settings_id(): string;

    public function __construct()
    {
        add_filter('mb_settings_pages', [$this, 'create_settings_page']);
        add_filter('rwmb_meta_boxes', [$this, 'define_settings_fields']);
        
        add_action( 'mb_settings_page_load',[$this, 'check_this_setting_page'],20);
    }

    /**
     * 
     * check and set isActive if actually loaded settings page is related to this class
     * 
     * @param mixed $args 
     * @return void 
     */
    public function check_this_setting_page($args):void
    {
        $this->isActive = false;
        if ( $args['id'] === $this->get_settings_id() ) {
            $this->isActive = true;
        }
    }

    

}
