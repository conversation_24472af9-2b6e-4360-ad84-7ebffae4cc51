<?php

namespace Umimeweby\UWTheme\Settings;

class UW_Services_Settings extends Abstract_UW_Settings
{
    /**
     *  Settings page and option definition
     */
    public const SETTINGS_ID = 'uw-settings-uw-services';
    public const OPTION_NAME = 'uw-settings-uw-services';

    const FIELD_TITLE = 'uwservices_title';
    const FIELD_SUBTITLE = 'uwservices_subtitle';
    const FIELD_SUBHEADER_AREA = 'uwservices_subheader_area';

    const FIELD_FORM_TITLE = 'uwservices_form_title';
    const FIELD_FORM_SUBTITLE = 'uwservices__form_subtitle';

    public function get_settings_id(): string
    {
        return self::SETTINGS_ID;
    }

    public function create_settings_page(mixed $settings_pages): mixed
    {
        $settings_pages[] = [
            'menu_title' => __('Nastavení - UW Služby', 'uw-child-theme'),
            'id' => self::SETTINGS_ID,
            'option_name' => self::OPTION_NAME,
            'position' => 25,
            'parent'          => 'edit.php?post_type=uw-service',
            'style' => 'no-boxes',
            'columns' => 1,
            'tabs'            => [
                'archive-page-header' => 'Archive Page Header',
                'contact-form'       => 'Contact Form Settings',
            ],
            'customizer'      => false,
            'customizer_only' => false,
            'network'         => false
        ];

        return $settings_pages;
    }

    public function define_settings_fields(mixed $meta_boxes): mixed
    {
        $meta_boxes[] = [
            'title' => __('Nadpisy pro souhrnnou stránku služeb', 'uw-child-theme'),
            'settings_pages' => [self::SETTINGS_ID],
            'tab'            => 'archive-page-header',
            'fields' => [
                [
                    'name' => __('1- Hlavní nadpis', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_TITLE,
                    'type' => 'textarea',
                ],
                [
                    'name' => __('2 - Podnadpis', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_SUBTITLE,
                    'type' => 'textarea',
                ],
                [
                    'name' => __('3 - prostor pod oběma nadpisy', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_SUBHEADER_AREA,
                    'type' => 'textarea',
                    'rows'        => 5,
                ],
            ]
        ];

        $meta_boxes[] = [
            'title' => __('Texty pro Contact Form', 'uw-child-theme'),
            'settings_pages' => [self::SETTINGS_ID],
            'tab'            => 'contact-form',
            'fields' => [
                [
                    'name' => __('Hlavní nadpis formuláře', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_FORM_TITLE,
                    'type' => 'textarea',
                ],
                [
                    'name' => __('Text pod nadpisem', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_FORM_SUBTITLE,
                    'type'    => 'wysiwyg',
                    'raw'     => true,
                    'options' => [
                        'textarea_rows'       => 2,
                        'media_buttons'       => false,
                    ],
                ],
            ]
        ];


        return $meta_boxes;
    }
}
