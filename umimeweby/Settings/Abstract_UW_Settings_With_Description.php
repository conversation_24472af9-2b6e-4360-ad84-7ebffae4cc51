<?php

namespace Umimeweby\UWTheme\Settings;

abstract class Abstract_UW_Settings_With_Description extends Abstract_UW_Settings
{

    abstract protected function get_description_text(): string;

    public function __construct()
    {
        parent::__construct();

        add_action('mb_settings_page_after_title', [$this, 'show_additional_description_for_section']);

    }  

    public function show_additional_description_for_section(): void {
        if (!$this->isActive) {
            return;
        }
        echo $this->get_description_text();
    }
}
