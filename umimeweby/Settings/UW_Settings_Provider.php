<?php

namespace Umimeweby\UWTheme\Settings;

use Um<PERSON>weby\UWTheme\Settings\UW_Sections\UW_Carousel_References;
use Umimeweby\UWTheme\Settings\UW_Sections\UW_Client_Logos_Settings;
use Um<PERSON>weby\UWTheme\Settings\UW_Sections\UW_Mini_Case_Studies_Settings;
use Umimeweby\UWTheme\Settings\UW_Sections\UW_New_Or_Existing_Settings;
use Umimeweby\UWTheme\Settings\UW_Sections\UW_References_By_Category_Settings;
use Umimeweby\UWTheme\Settings\UW_Sections\UW_Sections_Top_Level_Settings;
use Umimeweby\UWTheme\Settings\UW_Sections\UW_Technologies_Settings;
use Umimeweby\UWTheme\Settings\UW_Sections\UW_Why_SelectUs_Cards_Settings;

class UW_Settings_Provider
{
    /**
     * 
     * @var array<mixed>
     */
    private array $uw_settings = [];

    public function register_uw_settings(): void
    {
        $this->uw_settings = [
            new UW_Settings_Page(),
            new UW_GetReponse_Settings(),
            new UW_CTA_Settings(),
            new UW_Client_Logos_Settings(),
            new UW_Mini_Case_Studies_Settings(),
            new UW_Sections_Top_Level_Settings(),
            new UW_Why_SelectUs_Cards_Settings(),
            new UW_Technologies_Settings(),
            new UW_Carousel_References(),
            new UW_References_By_Category_Settings(),
            new UW_New_Or_Existing_Settings(),
            new UW_Services_Settings()
        ];
    }





    /**
     * Get all registered UW settings
     * @return array<mixed>
     */
    public function getUwSettings(): array
    {
        return $this->uw_settings;
    }
}
