<?php

namespace Umimeweby\UWTheme\Settings\UW_Sections;

use Um<PERSON>weby\UWTheme\Settings\Abstract_UW_Settings_With_Description;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;
use Umimeweby\UWTheme\ShortCodes\Components\Client_Logos_Shortcode;

class UW_Client_Logos_Settings extends Abstract_UW_Settings_With_Description
{

    const SETTINGS_ID = 'uw-settings-client-logos';
    const OPTION_NAME = 'uw-settings-client-logos';

    const FIELD_TITLE = 'clientlogos_title';
    const FIELD_DESCRIPTION = 'clientlogos_description';
    const FIELD_LOGO_GROUP = 'clientlogos_group';
    const FIELD_LOGO_GROUP_IMAGE = 'clientlogos_group_image';


    public function get_settings_id(): string
    {
        return self::SETTINGS_ID;
    }

    /**
     * @param array<mixed> $settings_pages
     */
    public function create_settings_page(mixed $settings_pages): mixed
    {
        $settings_pages[] = [
            'menu_title' => __('Loga klientů', 'uw-child-theme'),
            'id' => self::SETTINGS_ID,
            'option_name' => self::OPTION_NAME,
            'position' => 25,
            'parent' => UW_Sections_Top_Level_Settings::SETTINGS_ID,
            'style' => 'no-boxes',
            'columns' => 1,
            'capability'      => 'delete_others_pages',
        ];

        return $settings_pages;
    }

    public function define_settings_fields(mixed $meta_boxes): mixed
    {
        $meta_boxes[] = [
            'title' => __('Loga klientů', 'uw-child-theme'),
            'settings_pages' => [self::SETTINGS_ID],
            'fields' => [
                [
                    'name' => __('Nadpis sekce - Naši vážení zákaznící', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_TITLE,
                    'type' => 'text',
                ],
                [
                    'name' => __('Popis sekce - Naši vážení zákaznící', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_DESCRIPTION,
                    'type' => 'textarea',
                ],
                [
                    'type' => 'divider',
                ],
                [
                    'name' => __('Logo', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_LOGO_GROUP,
                    'type' => 'group',
                    'clone' => true,
                    'collapsible' => true,
                    'sort_clone' => true,
                    'default_state' => 'collapsed',
                    'group_title' => 'Logo {#}',
                    'fields' => [
                        [
                            'name'             => __('Logo', 'uw-child-theme'),
                            'id'               => UW_Settings_Page::PREFIX . self::FIELD_LOGO_GROUP_IMAGE,
                            'type'             => 'image_advanced',
                            'force_delete'     => false,
                            'max_file_uploads' => 1,
                            'max_clone' => 1,
                            'max_status'       => false,
                        ],
                    ]
                ],
            ]
        ];

        return $meta_boxes;
    }


    protected function get_description_text(): string
    {
        $url = get_template_directory_uri() . '/assets/images/shortcodes/loga-klientu.png';

        $text = '
        Tato funkcionalita umožňuje přidání sekce s logy klientů. Je použita na některých statických stránkách. Současně definuje shortkód, kterým lze tuto sekci generovat podle potřeby.<br>
        <br>
        Shortkód je: <strong>[' . Client_Logos_Shortcode::SHORTCODE . ']</strong><br>
        <br>
         Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a>
        <br>
        <br>
        ';

        return $text;
    }
}
