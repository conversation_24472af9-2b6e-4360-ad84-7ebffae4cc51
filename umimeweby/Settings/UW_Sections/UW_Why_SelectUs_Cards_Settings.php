<?php

namespace Umimeweby\UWTheme\Settings\UW_Sections;

use Um<PERSON>weby\UWTheme\Settings\Abstract_UW_Settings_With_Description;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;
use Umimeweby\UWTheme\ShortCodes\Components\Why_Select_Us_Shortcode;

class UW_Why_SelectUs_Cards_Settings extends Abstract_UW_Settings_With_Description
{
    /**
     *  Settings page and option definition
     */
    public const SETTINGS_ID = 'uw-settings-whyselectuscards';
    public const OPTION_NAME = 'uw-settings-whyselectuscards';

    public const FIELD_SECTION_TITLE = 'whyselectuscards_title';
    public const FIELD_SECTION_DESCRIPTION = 'whyselectuscards_description';

    const FIELD_GROUP = 'whyselectuscards_group';
    const FIELD_GROUP_IMAGE = 'whyselectuscards_group_image';
    const FIELD_GROUP_TITLE = 'whyselectuscards_group_title';
    const FIELD_GROUP_DESCRIPTION = 'whyselectuscards_group_description';


    public function create_settings_page(mixed $settings_pages): mixed
    {
        $settings_pages[] = [
            'menu_title' => __('Proč zvolit nás', 'uw-child-theme'),
            'id' => self::SETTINGS_ID,
            'option_name' => self::OPTION_NAME,
            'position' => 25,
            'parent' => UW_Sections_Top_Level_Settings::SETTINGS_ID,
            'style' => 'no-boxes',
            'columns' => 1,
            'capability'      => 'delete_others_pages',
        ];

        return $settings_pages;
    }

    public function define_settings_fields(mixed $meta_boxes): mixed
    {
        $meta_boxes[] = [
            'title' => __('Obsah sekce - proč si vybrat právě nás ', 'uw-child-theme'),
            'settings_pages' => [self::SETTINGS_ID],
            'fields' => [
                [
                    'name' => __('Nadpis sekce - Proč právě my', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_SECTION_TITLE,
                    'type' => 'text',
                ],
                [
                    'name' => __('Popis sekce - Proč právě my', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_SECTION_DESCRIPTION,
                    'type' => 'textarea',
                ],
                [
                    'name' => __('Karty - Proč právě my', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_GROUP,
                    'type' => 'group',
                    'clone' => true,
                    'collapsible' => true,
                    'sort_clone' => true,
                    'default_state' => 'collapsed',
                    'group_title' => 'Proč právě my {#}',
                    'fields' => [
                        [
                            'name'             => __('Obrázek', 'uw-child-theme'),
                            'id'               => UW_Settings_Page::PREFIX . self::FIELD_GROUP_IMAGE,
                            'type'             => 'image_advanced',
                            'force_delete'     => false,
                            'max_file_uploads' => 1,
                            'max_clone' => 1,
                            'max_status'       => false,
                        ],
                        [
                            'name' => __('Nadpis', 'uw-child-theme'),
                            'id' => UW_Settings_Page::PREFIX . self::FIELD_GROUP_TITLE,
                            'type' => 'text',
                        ],
                        [
                            'name' => __('Popis', 'uw-child-theme'),
                            'id' => UW_Settings_Page::PREFIX . self::FIELD_GROUP_DESCRIPTION,
                            'type' => 'textarea',
                        ],
                    ]
                ],
            ]
        ];
        return $meta_boxes;
    }

    public function get_settings_id(): string
    {
        return self::SETTINGS_ID;
    }

    protected function get_description_text(): string
    {
        $url = get_template_directory_uri() . '/assets/images/shortcodes/why-selectus.png';

        $text = '
        Tato funkcionalita umožňuje přidání sekce s důvody proč si vybrat právě nás. Současně definuje shortkód, kterým lze tuto sekci generovat podle potřeby.<br>
        <br>
        Shortkód je: <strong>[' . Why_Select_Us_Shortcode::SHORTCODE . ']</strong><br>
        <br>
         Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a>
        <br>
        <br>
        ';

        return $text;
    }
}
