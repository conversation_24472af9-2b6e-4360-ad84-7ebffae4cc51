<?php

namespace Umimeweby\UWTheme\Settings\UW_Sections;

use Um<PERSON>weby\UWTheme\Settings\Abstract_UW_Settings;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;
use Umimeweby\UWTheme\ShortCodes\Components\Technologies_Shortcode;

class UW_Technologies_Settings extends Abstract_UW_Settings
{

    const SETTINGS_ID = 'uw-settings-technologies';
    const OPTION_NAME = 'uw-settings-technologies';

    const FIELD_IMAGE = 'technology_image';
    const FIELD_TITLE = 'technology_title';
    const FIELD_DESCRIPTION = 'technology_description';
    const FIELD_GROUP = 'technology_group';
    const FIELD_GROUP_IMAGE = 'technology_group_image';

    public function __construct()
    {
        parent::__construct();

        add_action('mb_settings_page_after_title', [$this, 'show_additional_description_for_section']);
     
     
    }


    public function create_settings_page(mixed $settings_pages): mixed
    {
        $settings_pages[] = [
            'menu_title' => __('Technologie', 'uw-child-theme'),
            'id' => self::SETTINGS_ID,
            'option_name' => self::OPTION_NAME,
            'position' => 25,
            'parent' => UW_Sections_Top_Level_Settings::SETTINGS_ID,
            'style' => 'no-boxes',
            'columns' => 1,
            'capability'      => 'delete_others_pages',
        ];

        return $settings_pages;
    }

    public function define_settings_fields(mixed $meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => __('Loga klientů', 'uw-child-theme'),
            'settings_pages' => [self::SETTINGS_ID],
            'fields' => [
                [
                    'name'             => __('Obrázek', 'uw-child-theme'),
                    'id'               => UW_Settings_Page::PREFIX . self::FIELD_IMAGE,
                    'type'             => 'image_advanced',
                    'force_delete'     => false,
                    'max_file_uploads' => 1,
                    'max_clone' => 1,
                    'max_status'       => false,
                ],
                [
                    'name' => __('Nadpis sekce - Naše technologie', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_TITLE,
                    'type' => 'text',
                ],
                [
                    'name' => __('Popis sekce - Naše technologie', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_DESCRIPTION,
                    'type' => 'textarea',
                ],
                [
                    'name' => __('Konkrétní Technologie', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_GROUP,
                    'type' => 'group',
                    'clone' => true,
                    'collapsible' => true,
                    'sort_clone' => true,
                    'default_state' => 'collapsed',
                    'group_title' => 'Technologie {#}',
                    'fields' => [
                        [
                            'name'             => __('Technologie', 'uw-child-theme'),
                            'id'               => UW_Settings_Page::PREFIX . self::FIELD_GROUP_IMAGE,
                            'type'             => 'image_advanced',
                            'force_delete'     => false,
                            'max_file_uploads' => 1,
                            'max_clone' => 1,
                            'max_status'       => false,
                        ],
                    ]
                ],
            ]


        ];

        return $meta_boxes;
    }

    public function get_settings_id(): string
    {
        return self::SETTINGS_ID;
    }

    public function show_additional_description_for_section(): void
    {

        if (!$this->isActive) {
            return;
        }

        $url = get_template_directory_uri() . '/assets/images/shortcodes/technologie.png';

        $text = '
        Tato funkcionalita umožňuje přidání sekce s technologiemi, které používáme. Je použita na některých statických stránkách. Současně definuje shortkód, kterým lze tuto sekci generovat podle potřeby. Shortkód má mírně upravený design, protože šířka běžné stránky je užší. <br>
        <br>
        Shortkód je: <strong>['.Technologies_Shortcode::SHORTCODE.']</strong><br>
        <br>
         Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a>
        <br>
        <br>
        ';

        echo ($text);
    }    
}
