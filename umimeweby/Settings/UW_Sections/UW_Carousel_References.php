<?php

namespace Umimeweby\UWTheme\Settings\UW_Sections;

use Um<PERSON>weby\UWTheme\Settings\Abstract_UW_Settings_With_Description;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;
use Umimeweby\UWTheme\ShortCodes\Components\Carousel_References_Shortcode;

class UW_Carousel_References extends Abstract_UW_Settings_With_Description
{

    /**
     *  Settings page and option definition
     */
    public const SETTINGS_ID = 'uw-settings-carousel-references';
    public const OPTION_NAME = 'uw-settings-carousel-references';

    /**
     *  Fields, always define globally unique field name
     */
    const FIELD_TITLE = 'carouselreferences_title';
    const FIELD_DESCRIPTION = 'carouselreferences_description';
    const FIELD_GROUP = 'carouselreferences_group';
    const FIELD_GROUP_IMAGE = 'carouselreferences_group_image';
    const FIELD_GROUP_TITLE = 'carouselreferences_group_title';
    const FIELD_GROUP_DESCRIPTION = 'carouselreferences_group_description';
    const FIELD_GROUP_BTN_LINK = 'carouselreferences_group_btn_link';

    public function create_settings_page(mixed $settings_pages): mixed
    {
        $settings_pages[] = [
            'menu_title' => __('Reference Carousel', 'uw-child-theme'),
            'id' => self::SETTINGS_ID,
            'option_name' => self::OPTION_NAME,
            'position' => 25,
            'parent' => UW_Sections_Top_Level_Settings::SETTINGS_ID,
            'style' => 'no-boxes',
            'columns' => 1,
            'capability'      => 'delete_others_pages',
        ];

        return $settings_pages;
    }

    public function define_settings_fields(mixed $meta_boxes): mixed
    {
        $meta_boxes[] = [
            'title' => __('Obsah sekce - Carousel vybrané reference', 'uw-child-theme'),
            'settings_pages' => [self::SETTINGS_ID],
            'fields' => [
                [
                    'name'  => __('Nadpis sekce', 'uw-child-theme'),
                    'id'    => UW_Settings_Page::PREFIX . self::FIELD_TITLE,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Popis sekce', 'uw-child-theme'),
                    'id'    => UW_Settings_Page::PREFIX . self::FIELD_DESCRIPTION,
                    'type'  => 'text',
                ],
                [
                    'name'          => __('Reference', 'uw-child-theme'),
                    'id'            => UW_Settings_Page::PREFIX . self::FIELD_GROUP,
                    'type'          => 'group',
                    'clone'         => true,
                    'collapsible'   => true,
                    'sort_clone'    => true,
                    'default_state' => 'collapsed',
                    'group_title'   => 'Reference {#}',
                    'fields'        => [
                        [
                            'name'             => __('Obrázek', 'uw-child-theme'),
                            'id'               => UW_Settings_Page::PREFIX . self::FIELD_GROUP_IMAGE,
                            'type'             => 'image_advanced',
                            'force_delete'     => false,
                            'max_file_uploads' => 1,
                            'max_clone' => 1,
                            'max_status'       => false,
                        ],
                        [
                            'name'  => __('Nadpis reference', 'uw-child-theme'),
                            'id'    => UW_Settings_Page::PREFIX . self::FIELD_GROUP_TITLE,
                            'type'  => 'text',
                        ],
                        [
                            'name'  => __('Popis reference', 'uw-child-theme'),
                            'id'    => UW_Settings_Page::PREFIX . self::FIELD_GROUP_DESCRIPTION,
                            'type'    => 'wysiwyg',
                            'raw'     => true,
                            'options' => [
                                'textarea_rows'       => 2,
                                'media_buttons'       => false,
                            ],
                        ],
                        [
                            'name'  => __('Odkaz v tlačítku', 'uw-child-theme'),
                            'id'    => UW_Settings_Page::PREFIX . self::FIELD_GROUP_BTN_LINK,
                            'type'        => 'post',
                            'post_type' => 'nase-reference',
                            'field_type'  => 'select_advanced',
                            'placeholder' => 'Vyberte si stránku - pokud se nevybere, tlačítko se nezobrazí',
                            'query_args'  => [
                                'post_status'    => 'publish',
                                'posts_per_page' => -1,
                            ],
                        ],
                    ]
                ],

            ]
        ];


        return $meta_boxes;
    }

    public function get_settings_id(): string
    {
        return self::SETTINGS_ID;
    }

    protected function get_description_text(): string 
    {
        $url = get_template_directory_uri() . '/assets/images/shortcodes/carousel-reference.png';

        $text = '
        Tato funkcionalita umožňuje přidání sekce s carousel s vybranými referencemi. Je použita na některých statických stránkách. Současně definuje shortkód, kterým lze tuto sekci generovat podle potřeby.<br>
        <br>
        Shortkód je: <strong>['.Carousel_References_Shortcode::SHORTCODE.']</strong><br>
        <br>
         Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a>
        <br>
        <br>
        ';

        return $text;

    }
    
    
}
