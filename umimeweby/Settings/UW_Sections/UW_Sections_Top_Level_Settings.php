<?php

namespace Umimeweby\UWTheme\Settings\UW_Sections;

use Umimeweby\UWTheme\Settings\Abstract_UW_Settings;

class UW_Sections_Top_Level_Settings extends Abstract_UW_Settings
{


    /**
     *  Settings page and option definition
     */
    public const SETTINGS_ID = 'uw-settings-sections';
    public const OPTION_NAME = 'uw-settings-sections';

    public function create_settings_page(mixed $settings_pages): mixed 
    {
        $settings_pages[] = [
            'menu_title' => __('UW Sekce na webu', 'uw-child-theme'),
            'id' => self::SETTINGS_ID,
            'option_name' => self::OPTION_NAME,
            'position' => 25,
            'parent' => '',
            'style' => 'no-boxes',
            'columns' => 1,
            'icon_url'        => 'dashicons-menu-alt',
            'submenu_title' => __('Přehled', 'uw-child-theme'),
            'capability'      => 'delete_others_pages',
        ];
        return $settings_pages;
    }

    public function define_settings_fields(mixed $meta_boxes): mixed 
    {
        return $meta_boxes;
    }

    public function get_settings_id(): string 
    {
        return self::SETTINGS_ID;
    }
}
