<?php

namespace Umimeweby\UWTheme\Settings\UW_Sections;

use Um<PERSON>weby\UWTheme\Settings\Abstract_UW_Settings_With_Description;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;
use Umimeweby\UWTheme\ShortCodes\Components\References_By_Category_Shortcode;

class UW_References_By_Category_Settings extends Abstract_UW_Settings_With_Description
{

    /**
     *  Settings page and option definition
     */
    public const SETTINGS_ID = 'uw-settings-references-by-category';
    public const OPTION_NAME = 'uw-settings-references-by-category';

    /**
     *  Fields, always define globally unique field name
     */
    const FIELD_TITLE = 'referencesbycategory_title';
    const FIELD_DESCRIPTION = 'referencesbycategory_description';

    protected function get_description_text(): string
    {
        $url = get_template_directory_uri() . '/assets/images/shortcodes/reference-filtr-kategorie.png';

        $text = '
        Tato funkcionalita umožňuje přid<PERSON> sekce s referencemi, kde je ároveň možnost filtrování podle kategorií.Současně definuje shortkód, kterým lze tuto sekci generovat podle potřeby.<br>
        <br>
        Shortkód je: <strong>[' . References_By_Category_Shortcode::SHORTCODE . ']</strong><br>
        <br>
         Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a>
        <br>
        <br>
        ';

        return $text;
    }

    public function create_settings_page(mixed $settings_pages): mixed
    {
        $settings_pages[] = [
            'menu_title' => __('Reference s filtrem kategorií', 'uw-child-theme'),
            'id' => self::SETTINGS_ID,
            'option_name' => self::OPTION_NAME,
            'position' => 25,
            'parent' => UW_Sections_Top_Level_Settings::SETTINGS_ID,
            'style' => 'no-boxes',
            'columns' => 1,
            'capability'      => 'delete_others_pages',
        ];

        return $settings_pages;
    }

    public function define_settings_fields(mixed $meta_boxes): mixed
    {
        $meta_boxes[] = [
            'title' => __('Obsah sekce - Reference s výběrem kategorií', 'uw-child-theme'),
            'settings_pages' => [self::SETTINGS_ID],
            'fields' => [
                [
                    'name'  => __('Nadpis pro sekci', 'uw-child-theme'),
                    'id'    => UW_Settings_Page::PREFIX . self::FIELD_TITLE,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Podnadpisový text', 'uw-child-theme'),
                    'id'    => UW_Settings_Page::PREFIX . self::FIELD_DESCRIPTION,
                    'type'  => 'textarea',
                ],
            ]
        ];
        return $meta_boxes;
    }

    public function get_settings_id(): string
    {
        return self::SETTINGS_ID;
    }
}
