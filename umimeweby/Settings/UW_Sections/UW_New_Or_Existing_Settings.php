<?php

namespace Umimeweby\UWTheme\Settings\UW_Sections;

use Umimeweby\UWTheme\CustomPostType\UW_Service_Custom_Post_Type;
use Umimeweby\UWTheme\Settings\Abstract_UW_Settings_With_Description;
use Um<PERSON>weby\UWTheme\Settings\UW_Settings_Page;
use Umimeweby\UWTheme\ShortCodes\Components\New_Or_Existing_Projects_Shortcode;

class UW_New_Or_Existing_Settings extends Abstract_UW_Settings_With_Description
{

    /**
     *  Settings page and option definition
     */
    public const SETTINGS_ID = 'uw-settings-new-or-existing-cta';
    public const OPTION_NAME = 'uw-settings-new-or-existing-cta';

    /**
     *  Fields, always define globally unique field name
     */
    const FIELD_LINK_NEW = 'neworexisting_link_new';
    const FIELD_LINK_EXISTING = 'neworexisting_link_existing';

    protected function get_description_text(): string
    {
        $url = get_template_directory_uri() . '/assets/images/shortcodes/new-or-existing.png';

        $text = '
        Tato funkcionalita umožňuje přid<PERSON>í sekce s CTA na přechod na stránku Nový projekt nebo Předat existující projektSoučasně definuje shortkód, kterým lze tuto sekci generovat podle potřeby.<br>
        <br>
         Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a>
        <br>
        Shortkód je: <strong>[' . New_Or_Existing_Projects_Shortcode::SHORTCODE . ']</strong><br>
        <br>
        ';

        return $text;
    }

    public function create_settings_page(mixed $settings_pages): mixed
    {
        $settings_pages[] = [
            'menu_title' => __('New or Existing Project CTA', 'uw-child-theme'),
            'id' => self::SETTINGS_ID,
            'option_name' => self::OPTION_NAME,
            'position' => 25,
            'parent' => UW_Sections_Top_Level_Settings::SETTINGS_ID,
            'style' => 'no-boxes',
            'columns' => 1,
            'capability'      => 'delete_others_pages',
        ];

        return $settings_pages;
    }

    public function define_settings_fields(mixed $meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => __('Obsah sekce - New or Existing Project CTA', 'uw-child-theme'),
            'settings_pages' => [self::SETTINGS_ID],
            'fields' => [
                [
                    'name' => __('NEW - Vyberte page nebo post pro link', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_LINK_NEW,
                    'type'        => 'post',
                    'post_type' => ['post', 'page', UW_Service_Custom_Post_Type::CPT_KEY],
                    'field_type'  => 'select_advanced',
                    'placeholder' => 'Select a page',
                    'query_args'  => [
                        'post_status'    => 'publish',
                        'posts_per_page' => -1,
                    ],
                    'desc' => __('Odkaz z odkazu Nový projekt.', 'uw-child-theme'),
                ],
                [
                    'name' => __('EXISTING - Vyberte page nebo post pro link', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_LINK_EXISTING,
                    'type'        => 'post',
                    'post_type' => ['post', 'page', UW_Service_Custom_Post_Type::CPT_KEY],
                    'field_type'  => 'select_advanced',
                    'placeholder' => 'Select a page',
                    'query_args'  => [
                        'post_status'    => 'publish',
                        'posts_per_page' => -1,
                    ],
                    'desc' => __('Odkaz z odkazu Předat existující projekt.', 'uw-child-theme'),
                ],                
            ]
        ];

        return $meta_boxes;
    }

    public function get_settings_id(): string
    {
        return self::SETTINGS_ID;
    }
}
