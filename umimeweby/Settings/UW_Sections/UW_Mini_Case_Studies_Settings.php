<?php

namespace Umimeweby\UWTheme\Settings\UW_Sections;

use Um<PERSON>weby\UWTheme\Settings\Abstract_UW_Settings_With_Description;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;
use Umimeweby\UWTheme\ShortCodes\Components\Mini_Case_Studies_Shortcode;

class UW_Mini_Case_Studies_Settings extends Abstract_UW_Settings_With_Description
{

    /**
     *  Settings page and option definition
     */
    public const SETTINGS_ID = 'uw-settings-mini-casestudies';
    public const OPTION_NAME = 'uw-settings-mini-casestudies';


    /**
     *  Fields, always define globally unique field name
     */
    const FIELD_TITLE = 'casestudies_title';
    const FIELD_DESCRIPTION = 'casestudies_description';
    const FIELD_GROUP = 'casestudies_group';
    const FIELD_GROUP_IMAGE = 'casestudies_group_image';
    const FIELD_GROUP_FIRM = 'casestudies_group_firm';
    const FIELD_GROUP_DETAIL = 'casestudies_group_detail';
    const FIELD_GROUP_DATE = 'casestudies_group_date';
    const FIELD_LINK_MORE = 'casestudies_link_more';
    const FIELD_LINK_MORE_TEXT = 'casestudies_link_more_text';


    public function get_settings_id(): string 
    {
        return self::SETTINGS_ID;
    }

    public function create_settings_page(mixed $settings_pages): mixed
    {
        $settings_pages[] = [
            'menu_title' => __('Mini případovky', 'uw-child-theme'),
            'id' => self::SETTINGS_ID,
            'option_name' => self::OPTION_NAME,
            'position' => 25,
            'parent' => UW_Sections_Top_Level_Settings::SETTINGS_ID,
            'style' => 'no-boxes',
            'columns' => 1,
            'capability'      => 'delete_others_pages',
        ];

        return $settings_pages;
    }

    public function define_settings_fields(mixed $meta_boxes): mixed
    {
        $meta_boxes[] = [
            'title' => __('Obsah sekce - Pro naše klienty jsme realizovali', 'uw-child-theme'),
            'settings_pages' => [self::SETTINGS_ID],
            'fields' => [
                [
                    'name' => __('Nadpis sekce', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_TITLE,
                    'type' => 'text',
                    'desc' => __('Hlavní nadpis pro tuto sekci', 'uw-child-theme'),
                ],
                [
                    'name' => __('Popis sekce - Pro naše klienty jsme realizovali', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_DESCRIPTION,
                    'type' => 'textarea',
                    'desc' => __('Popisný text pod nadpis pro tuto sekci', 'uw-child-theme'),
                ],
                [
                    'name' => __('Klient', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_GROUP,
                    'type' => 'group',
                    'clone' => true,
                    'collapsible' => true,
                    'sort_clone' => true,
                    'default_state' => 'collapsed',
                    'group_title' => 'Klient {#}',
                    'fields' => [
                        [
                            'name'             => __('Logo', 'uw-child-theme'),
                            'id'               => UW_Settings_Page::PREFIX . self::FIELD_GROUP_IMAGE,
                            'type'             => 'image_advanced',
                            'force_delete'     => false,
                            'max_file_uploads' => 1,
                            'max_clone' => 1,
                            'max_status'       => false,
                        ],
                        [
                            'name' => __('Firma - název', 'uw-child-theme'),
                            'id' => UW_Settings_Page::PREFIX . self::FIELD_GROUP_FIRM,
                            'type' => 'text',
                        ],
                        [
                            'name' => __('O co šlo v rámci řešení', 'uw-child-theme'),
                            'id' => UW_Settings_Page::PREFIX . self::FIELD_GROUP_DETAIL,
                            'type' => 'textarea',
                        ],
                        [
                            'name' => __('Datum realizace ', 'uw-child-theme'),
                            'id' => UW_Settings_Page::PREFIX . self::FIELD_GROUP_DATE,
                            'type' => 'text',
                        ],
                    ]
                ],
                [
                    'name' => __('Vyberte page nebo post pro případný link', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_LINK_MORE,
                    'type'        => 'post',
                    'post_type' => ['post', 'page'],
                    'desc' => __('Pokud chcete zobrazit odkaz na nějakou stránku, pak vyberte zde page nebo post. Pokud je prázdné, link se nezobrazí.', 'uw-child-theme'),
                ],
                [
                    'name' => __('text odkazu', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_LINK_MORE_TEXT,
                    'type' => 'text',
                    'desc' => __('zadejte vlastní text pokud chcete jiný text než defaultní : Zobrazit všechny reference', 'uw-child-theme'),
                    'std' => 'Zobrazit všechny reference'
                ],
            ]
        ];


        return $meta_boxes;
    }
  

    protected function get_description_text(): string 
    {
        $url = get_template_directory_uri() . '/assets/images/shortcodes/mini-pripadovky.jpg';

        $text = '
        Tato funkcionalita umožňuje přidání sekce s mini případovkami. Je použita na některých statických stránkách. Současně definuje shortkód, kterým lze tuto sekci generovat podle potřeby.<br>
        <br>
        Shortkód je: <strong>['.Mini_Case_Studies_Shortcode::SHORTCODE.']</strong><br>
        <br>
         Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a>
        <br>
        <br>
        ';

        return $text;
    }
}
