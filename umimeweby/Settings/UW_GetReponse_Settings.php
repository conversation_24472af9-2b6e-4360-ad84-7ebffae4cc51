<?php

namespace Umimeweby\UWTheme\Settings;

class UW_GetReponse_Settings
{

    const SETTINGS_GETRESPONSE = 'uw-settings-getresponse';
    const OPTION_GETRESPONSE_SETTING = 'uw-getresponse-setting';

    const FIELD_GETRESPONSE_APIKEY = 'getresponse_apikey';
    const FIELD_GETRESPONSE_THANKYOUPAGE = 'getresponse_typage';

    const FIELD_GETRESPONSE_NLLISTID = 'getresponse_nllistid';


    public function __construct()
    {
     
        add_filter('mb_settings_pages', [$this, 'create_getresponse_settings_pages']);
        add_filter('rwmb_meta_boxes', [$this, 'define_settings_fields']);
     
    }




    /**
     * @param array<mixed> $settings_getresponse_pages
     */
    public function create_getresponse_settings_pages(mixed $settings_getresponse_pages): mixed
    {
        $settings_getresponse_pages[] = [
            'menu_title' => __('GetResponse', 'uw-child-theme'),
            'id' => self::SETTINGS_GETRESPONSE,
            'option_name' => self::OPTION_GETRESPONSE_SETTING,
            'position' => 25,
            'parent' => UW_Settings_Page::SETTINGS_PAGE_MAIN,
            'style' => 'no-boxes',
            'columns' => 1,
            'capability'      => 'delete_others_pages',
        ];

        return $settings_getresponse_pages;
    }


    public function define_settings_fields(mixed $meta_define_fields): mixed
    {
        $meta_define_fields[] = [
            'title' => __('GetResponse settings', 'uw-child-theme'),
            'settings_pages' => [self::SETTINGS_GETRESPONSE],
            'fields' => [
                [
                    'name' => __('API Key', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_GETRESPONSE_APIKEY,
                    'type' => 'text'
                ],
                [
                    'name'        => __('Vyberte defaultní stránku pro přesměrování po odeslání formuláře', 'uw-child-theme'),
                    'id'          => UW_Settings_Page::PREFIX . self::FIELD_GETRESPONSE_THANKYOUPAGE,
                    'type'        => 'post',
                    'post_type'   => 'page',
                    'field_type'  => 'select_advanced',
                    'placeholder' => 'Vyberte stránku',
                    'query_args'  => [
                        'post_status'    => 'publish',
                        'posts_per_page' => - 1,
                    ],
                ],      
                [
                    'name' => __('ID Listu pro Newsletter', 'uw-child-theme'),
                    'id' => UW_Settings_Page::PREFIX . self::FIELD_GETRESPONSE_NLLISTID,
                    'type' => 'text'
                ],                          
            ]
        ];

        return $meta_define_fields;
    }

}
