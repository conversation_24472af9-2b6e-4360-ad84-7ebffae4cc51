<?php

namespace Umimeweby\UWTheme\CustomPostType;

class Custom_Post_Type_Provider_Service
{

  /**
   * 
   * @var array<mixed>
   */
  private array $mb_custom_post_types = [];
  /**
   * 
   * @var array<mixed>
   */
  private array $wp_custom_post_types = [];

  public function register_metabox_related_cpt(): void
  {
    $this->mb_custom_post_types = [
      new UW_Service_Custom_Post_Type(),
      new Our_Services_Custom_Post_Type(),
      new Reference_Custom_Post_Type()
    ];
  }

  public function register_wordpress_based_cpt(): void
  {
    $this->wp_custom_post_types[] = [];
  }

  /**
   * Get array of all registered custom post types that rely on to metabox plugin
   * @return array<mixed>
   */
  public function get_mb_custom_post_types(): array
  {
    return $this->mb_custom_post_types;
  }

  /**
   * Get array of all registered custom post types that rely on plain wordpress
   * @return array<mixed>
   */
  public function get_wp_custom_post_types(): array
  {
    return $this->wp_custom_post_types;
  }
}
