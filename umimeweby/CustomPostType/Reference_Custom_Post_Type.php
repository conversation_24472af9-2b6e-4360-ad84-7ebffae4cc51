<?php

namespace Umimeweby\UWTheme\CustomPostType;

class Reference_Custom_Post_Type
{
    const CPT_KEY = 'nase-reference';

    const PREFIX = 'reference_cards_';

    const REFERENCE_CARDS_FIELD_TITLE = 'title';
    const REFERENCE_CARDS_FIELD_DESCRIPTION = 'description';
    const REFERENCE_CARDS_FIELD_LINK = 'link';
    const REFERENCE_CARDS_FIELD_BTN_TEXT = 'btn_text';

    public function __construct()
    {
        add_action('init', [$this, 'custom_post_type_reference']);
        add_filter('rwmb_meta_boxes', [$this, 'reference_card_custom_fields']);
    }

    function custom_post_type_reference(): void
    {
        $labels = [
            'name' => esc_html__('Reference', 'uw-child-theme'),
            'singular_name' => esc_html__('Reference', 'uw-child-theme'),
            'add_new' => esc_html__('Add New', 'uw-child-theme'),
            'add_new_item' => esc_html__('Add New Reference', 'uw-child-theme'),
            'edit_item' => esc_html__('Edit Reference', 'uw-child-theme'),
            'new_item' => esc_html__('New Reference', 'uw-child-theme'),
            'view_item' => esc_html__('View Reference', 'uw-child-theme'),
            'view_items' => esc_html__('View Reference', 'uw-child-theme'),
            'search_items' => esc_html__('Search Reference', 'uw-child-theme'),
            'not_found' => esc_html__('No reference found.', 'uw-child-theme'),
            'not_found_in_trash' => esc_html__('No reference found in Trash.', 'uw-child-theme'),
            'parent_item_colon' => esc_html__('Parent Reference:', 'uw-child-theme'),
            'all_items' => esc_html__('All Reference', 'uw-child-theme'),
            'archives' => esc_html__('Reference Archives', 'uw-child-theme'),
            'attributes' => esc_html__('Reference Attributes', 'uw-child-theme'),
            'insert_into_item' => esc_html__('Insert into reference', 'uw-child-theme'),
            'uploaded_to_this_item' => esc_html__('Uploaded to this reference', 'uw-child-theme'),
            'featured_image' => esc_html__('Featured image', 'uw-child-theme'),
            'set_featured_image' => esc_html__('Set featured image', 'uw-child-theme'),
            'remove_featured_image' => esc_html__('Remove featured image', 'uw-child-theme'),
            'use_featured_image' => esc_html__('Use as featured image', 'uw-child-theme'),
            'menu_name' => esc_html__('Reference', 'uw-child-theme'),
            'filter_items_list' => esc_html__('Filter reference list', 'uw-child-theme'),
            'filter_by_date' => esc_html__('', 'uw-child-theme'),
            'items_list_navigation' => esc_html__('Reference list navigation', 'uw-child-theme'),
            'items_list' => esc_html__('Reference list', 'uw-child-theme'),
            'item_published' => esc_html__('Reference published.', 'uw-child-theme'),
            'item_published_privately' => esc_html__('Reference published privately.', 'uw-child-theme'),
            'item_reverted_to_draft' => esc_html__('Reference reverted to draft.', 'uw-child-theme'),
            'item_scheduled' => esc_html__('Reference scheduled.', 'uw-child-theme'),
            'item_updated' => esc_html__('Reference updated.', 'uw-child-theme'),
            'text_domain' => esc_html__('uw-child-theme', 'uw-child-theme'),
        ];
        $args = [
            'label' => esc_html__('Reference', 'uw-child-theme'),
            'labels' => $labels,
            'description' => '',
            'public' => true,
            'hierarchical' => false,
            'exclude_from_search' => false,
            'publicly_queryable' => true,
            'show_ui' => true,
            'show_in_nav_menus' => true,
            'show_in_admin_bar' => true,
            'show_in_rest' => true,
            'query_var' => true,
            'can_export' => true,
            'delete_with_user' => true,
            'has_archive' => true,
            'rest_base' => '',
            'show_in_menu' => true,
            'menu_icon' => 'dashicons-feedback',
            'capability_type' => 'post',
            'supports' => ['title', 'editor', 'thumbnail'],
            'taxonomies' => ['kategorie', 'post_tag'],
            'rewrite' => [
                'slug'       => 'reference',
                'with_front' => false,
            ],
        ];

        register_post_type(self::CPT_KEY, $args);
    }

    /**
     * @param array<mixed> $meta_boxes
     */
    public function reference_card_custom_fields(mixed $meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => __('Obsah sekce - Reference karta', 'uw-child-theme'),
            'post_types' => self::CPT_KEY,
            'fields' => [
                [
                    'name'  => __('Nadpis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::REFERENCE_CARDS_FIELD_TITLE,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Doména', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::REFERENCE_CARDS_FIELD_LINK,
                    'type'  => 'url',
                ],
                [
                    'name'  => __('Popis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::REFERENCE_CARDS_FIELD_DESCRIPTION,
                    'type'  => 'textarea',
                ],
                [
                    'name'  => __('Text v tlačítku', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::REFERENCE_CARDS_FIELD_BTN_TEXT,
                    'type'  => 'text',
                ],
            ]
        ];
        return $meta_boxes;
    }
}