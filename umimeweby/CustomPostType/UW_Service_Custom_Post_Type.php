<?php

namespace Umimeweby\UWTheme\CustomPostType;

use Umimeweby\UWTheme\Taxonomies\UW_Service_Custom_Taxonomy;

class UW_Service_Custom_Post_Type
{

    const CPT_KEY = 'uw-service';

    const PREFIX = 'uw_service_';

    const SERVICE_SHORT_DESCRIPTION_FIELD = 'description';    

    const FORM_CTA_LINE_1_FIELD = 'form_line_1';
    const FORM_DESCRIPTION_LINE_2_FIELD = 'form_line_2';    
    

    public function __construct()
    {
        add_action( 'init', [$this, 'uw_service_register_post_type']);
        add_filter('rwmb_meta_boxes', [$this, 'register_taxonomy_metabox']);

    }

    public function uw_service_register_post_type():void
    {
        $labels = [
            'name'                     => esc_html__('UW Služby', 'uw-child-theme'),
            'singular_name'            => esc_html__('U<PERSON> Služba', 'uw-child-theme'),
            'add_new'                  => esc_html__('Add New', 'uw-child-theme'),
            'add_new_item'             => esc_html__('Add New UW Služba', 'uw-child-theme'),
            'edit_item'                => esc_html__('Edit UW Služba', 'uw-child-theme'),
            'new_item'                 => esc_html__('New UW Služba', 'uw-child-theme'),
            'view_item'                => esc_html__('View UW Služba', 'uw-child-theme'),
            'view_items'               => esc_html__('View UW Služby', 'uw-child-theme'),
            'search_items'             => esc_html__('Search UW Služby', 'uw-child-theme'),
            'not_found'                => esc_html__('No uw služby found.', 'uw-child-theme'),
            'not_found_in_trash'       => esc_html__('No uw služby found in Trash.', 'uw-child-theme'),
            'parent_item_colon'        => esc_html__('Parent UW Služba:', 'uw-child-theme'),
            'all_items'                => esc_html__('All UW Služby', 'uw-child-theme'),
            'archives'                 => esc_html__('UW Služba Archives', 'uw-child-theme'),
            'attributes'               => esc_html__('UW Služba Attributes', 'uw-child-theme'),
            'insert_into_item'         => esc_html__('Insert into uw služba', 'uw-child-theme'),
            'uploaded_to_this_item'    => esc_html__('Uploaded to this uw služba', 'uw-child-theme'),
            'featured_image'           => esc_html__('Featured image', 'uw-child-theme'),
            'set_featured_image'       => esc_html__('Set featured image', 'uw-child-theme'),
            'remove_featured_image'    => esc_html__('Remove featured image', 'uw-child-theme'),
            'use_featured_image'       => esc_html__('Use as featured image', 'uw-child-theme'),
            'menu_name'                => esc_html__('UW Služby', 'uw-child-theme'),
            'filter_items_list'        => esc_html__('Filter uw služby list', 'uw-child-theme'),
            'filter_by_date'           => esc_html__('', 'uw-child-theme'),
            'items_list_navigation'    => esc_html__('UW Služby list navigation', 'uw-child-theme'),
            'items_list'               => esc_html__('UW Služby list', 'uw-child-theme'),
            'item_published'           => esc_html__('UW Služba published.', 'uw-child-theme'),
            'item_published_privately' => esc_html__('UW Služba published privately.', 'uw-child-theme'),
            'item_reverted_to_draft'   => esc_html__('UW Služba reverted to draft.', 'uw-child-theme'),
            'item_scheduled'           => esc_html__('UW Služba scheduled.', 'uw-child-theme'),
            'item_updated'             => esc_html__('UW Služba updated.', 'uw-child-theme'),
            'text_domain'              => esc_html__('uw-child-theme', 'uw-child-theme'),
        ];
        $args = [
            'label'               => esc_html__('UW Služby', 'uw-child-theme'),
            'labels'              => $labels,
            'description'         => '',
            'public'              => true,
            'hierarchical'        => false,
            'exclude_from_search' => false,
            'publicly_queryable'  => true,
            'show_ui'             => true,
            'show_in_nav_menus'   => true,
            'show_in_admin_bar'   => true,
            'show_in_rest'        => true,
            'query_var'           => true,
            'can_export'          => true,
            'delete_with_user'    => true,
            'has_archive'         => true,
            'rest_base'           => '',
            'show_in_menu'        => true,
            'menu_position'       => 25,
            'menu_icon'           => 'dashicons-products',
            'capability_type'     => 'post',
            'supports'            => ['title', 'editor', 'thumbnail', 'custom-fields'],
            'taxonomies'          => ['kategorie-uw-sluzeb'],
            'rewrite'             => [
                'slug'       => 'sluzby',
                'with_front' => false,
            ],
        ];

        register_post_type(self::CPT_KEY, $args);
    }

    /**
     * 
     * @param mixed $meta_boxes 
     * @return mixed 
     */
    public function register_taxonomy_metabox($meta_boxes) 
    {
        $meta_boxes[] = [
            'title'      => __('Kategorie služby', 'uw-child-theme'),
            'id'         => 'uw-service-category-select',
            'post_types' => [self::CPT_KEY], // Tvůj CPT key
            'context'    => 'side', // Zobrazí se v pravém sloupci
            'priority'   => 'high',
            'fields'     => [
                [
                    'name'        => __('Vyber kategorii', 'uw-child-theme'),
                    'id'         => 'uw_service_category_select',
                    'type'       => 'taxonomy',
                    'taxonomy'   => UW_Service_Custom_Taxonomy::TAXONOMY_KEY,
                    'field_type' => 'select_advanced', // nebo 'select' pro jednodušší verzi
                    'placeholder'=> __('Vyber kategorii služby', 'uw-child-theme'),
                    'multiple'   => false, // true pokud chceš povolit více hodnot
                ]
            ]
        ];

         $meta_boxes[] = [
            'title' => __('Sluzba - detaily', 'uw-child-theme'),
            'post_types' => self::CPT_KEY,
            'fields' => [

                [
                    'name'  => __('Zkrácený popis pro kartu', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::SERVICE_SHORT_DESCRIPTION_FIELD,
                    'type'  => 'textarea',
                ],

                [
                    'name'  => __('Contact form Line 1 - main CTA', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::FORM_CTA_LINE_1_FIELD,
                    'type'  => 'textarea',
                ],
                [
                    'name'  => __('Contact form Line 1 - main CTA', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::FORM_DESCRIPTION_LINE_2_FIELD,
                    'type'    => 'wysiwyg',
                    'raw'     => true,
                    'options' => [
                        'textarea_rows'       => 4,
                        'media_buttons'       => false,
                    ],
                ],                                

            ]
        ];
    
        return $meta_boxes;
    }

}
