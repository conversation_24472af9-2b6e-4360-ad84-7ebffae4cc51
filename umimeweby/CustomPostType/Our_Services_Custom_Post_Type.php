<?php

namespace Umimeweby\UWTheme\CustomPostType;

class Our_Services_Custom_Post_Type
{
    const CPT_KEY = 'nase-sluzby';

    const PREFIX = 'nase_sluzby_';
    const OUR_SERVICES_CUSTOM_FIELDS_SUBTITLE = 'subtitle';
    const OUR_SERVICES_CUSTOM_FIELDS_TITLE = 'title';
    const OUR_SERVICES_CUSTOM_FIELDS_DESCRIPTION = 'description';

    public function __construct()
    {
        add_action( 'init', [$this, 'custom_post_type_our_services']);
        add_filter('rwmb_meta_boxes', [$this, 'custom_fields_our_services_header']);
    }

    function custom_post_type_our_services(): void {
        $labels = [
            'name'                     => esc_html__( 'OLD Naše služby', 'uw-child-theme' ),
            'singular_name'            => esc_html__( 'Naše <PERSON>lužby', 'uw-child-theme' ),
            'add_new'                  => esc_html__( 'Add New', 'uw-child-theme' ),
            'add_new_item'             => esc_html__( 'Add New Naše Služby', 'uw-child-theme' ),
            'edit_item'                => esc_html__( 'Edit Naše Služby', 'uw-child-theme' ),
            'new_item'                 => esc_html__( 'New Naše Služby', 'uw-child-theme' ),
            'view_item'                => esc_html__( 'View Naše Služby', 'uw-child-theme' ),
            'view_items'               => esc_html__( 'View Naše služby', 'uw-child-theme' ),
            'search_items'             => esc_html__( 'Search Naše služby', 'uw-child-theme' ),
            'not_found'                => esc_html__( 'No naše služby found.', 'uw-child-theme' ),
            'not_found_in_trash'       => esc_html__( 'No naše služby found in Trash.', 'uw-child-theme' ),
            'parent_item_colon'        => esc_html__( 'Parent Naše Služby:', 'uw-child-theme' ),
            'all_items'                => esc_html__( 'All Naše služby', 'uw-child-theme' ),
            'archives'                 => esc_html__( 'Naše Služby Archives', 'uw-child-theme' ),
            'attributes'               => esc_html__( 'Naše Služby Attributes', 'uw-child-theme' ),
            'insert_into_item'         => esc_html__( 'Insert into naše služby', 'uw-child-theme' ),
            'uploaded_to_this_item'    => esc_html__( 'Uploaded to this naše služby', 'uw-child-theme' ),
            'featured_image'           => esc_html__( 'Featured image', 'uw-child-theme' ),
            'set_featured_image'       => esc_html__( 'Set featured image', 'uw-child-theme' ),
            'remove_featured_image'    => esc_html__( 'Remove featured image', 'uw-child-theme' ),
            'use_featured_image'       => esc_html__( 'Use as featured image', 'uw-child-theme' ),
            'menu_name'                => esc_html__( 'OLD-Naše služby', 'uw-child-theme' ),
            'filter_items_list'        => esc_html__( 'Filter naše služby list', 'uw-child-theme' ),
            'filter_by_date'           => esc_html__( '', 'uw-child-theme' ),
            'items_list_navigation'    => esc_html__( 'Naše služby list navigation', 'uw-child-theme' ),
            'items_list'               => esc_html__( 'Naše služby list', 'uw-child-theme' ),
            'item_published'           => esc_html__( 'Naše Služby published.', 'uw-child-theme' ),
            'item_published_privately' => esc_html__( 'Naše Služby published privately.', 'uw-child-theme' ),
            'item_reverted_to_draft'   => esc_html__( 'Naše Služby reverted to draft.', 'uw-child-theme' ),
            'item_scheduled'           => esc_html__( 'Naše Služby scheduled.', 'uw-child-theme' ),
            'item_updated'             => esc_html__( 'Naše Služby updated.', 'uw-child-theme' ),
            'text_domain'              => esc_html__( 'uw-child-theme', 'uw-child-theme' ),
        ];
        $args = [
            'label'               => esc_html__( 'OLD Naše služby', 'uw-child-theme' ),
            'labels'              => $labels,
            'description'         => '',
            'public'              => true,
            'hierarchical'        => false,
            'exclude_from_search' => false,
            'publicly_queryable'  => true,
            'show_ui'             => true,
            'show_in_nav_menus'   => true,
            'show_in_admin_bar'   => true,
            'show_in_rest'        => true,
            'query_var'           => true,
            'can_export'          => true,
            'delete_with_user'    => true,
            'has_archive'         => true,
            'rest_base'           => '',
            'show_in_menu'        => true,
            'menu_position'       => '',
            'menu_icon'           => 'dashicons-book',
            'capability_type'     => 'post',
            'supports'            => ['title', 'editor', 'thumbnail'],
            'taxonomies'          => [],
            'rewrite'             => [
                'with_front' => false,
            ],
        ];

        register_post_type( self::CPT_KEY, $args );
    }

    public function custom_fields_our_services_header(mixed $meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => __('Obsah headeru a sekce chci se nalodit', 'uw-child-theme'),
            'post_types' => [self::CPT_KEY, 'page'],
            'fields' => [
                [
                    'name'  => __('Podnadpis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::OUR_SERVICES_CUSTOM_FIELDS_SUBTITLE,
                    'type'  => 'textarea',
                ],
                [
                    'name'  => __('Nadpis pro sekci - Chci se nalodit', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::OUR_SERVICES_CUSTOM_FIELDS_TITLE,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Popis pro sekci - Chci se nalodit', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::OUR_SERVICES_CUSTOM_FIELDS_DESCRIPTION,
                    'type'  => 'textarea',
                ],
            ]
        ];
        return $meta_boxes;
    }
}