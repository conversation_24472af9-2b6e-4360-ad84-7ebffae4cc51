<?php

namespace Umimeweby\UWTheme\CustomPostType;

class Text_Testimonials_Custom_Post_Type
{
    const CPT_KEY = 'uwtexttestimonials';

    const PREFIX = 'text_testimonials_';

    const TEXT_TESTIMONIALS_FIELD_PERSON_NAME = 'person_name';
    const TEXT_TESTIMONIALS_FIELD_POSITION_COMPANY = 'position_company';
    const TEXT_TESTIMONIALS_FIELD_PHOTO_LOGO = 'photo_logo';
    const TEXT_TESTIMONIALS_FIELD_OPTIONAL_TITLE = 'optional_title';

    public function __construct()
    {
        add_action('init', [$this, 'custom_post_type_text_testimonials']);
        add_filter('rwmb_meta_boxes', [$this, 'text_testimonials_custom_fields']);
    }

    function custom_post_type_text_testimonials(): void
    {
        $labels = [
            'name' => esc_html__('Textové Reference', 'uw'),
            'singular_name' => esc_html__('Textová Reference', 'uw'),
            'add_new' => esc_html__('Přidat novou', 'uw'),
            'add_new_item' => esc_html__('Přidat novou textovou referenci', 'uw'),
            'edit_item' => esc_html__('Upravit textovou referenci', 'uw'),
            'new_item' => esc_html__('Nová textová reference', 'uw'),
            'view_item' => esc_html__('Zobrazit textovou referenci', 'uw'),
            'view_items' => esc_html__('Zobrazit textové reference', 'uw'),
            'search_items' => esc_html__('Hledat textové reference', 'uw'),
            'not_found' => esc_html__('Žádné textové reference nenalezeny.', 'uw'),
            'not_found_in_trash' => esc_html__('Žádné textové reference nenalezeny v koši.', 'uw'),
            'parent_item_colon' => esc_html__('Nadřazená textová reference:', 'uw'),
            'all_items' => esc_html__('Všechny textové reference', 'uw'),
            'archives' => esc_html__('Archiv textových referencí', 'uw'),
            'attributes' => esc_html__('Atributy textové reference', 'uw'),
            'insert_into_item' => esc_html__('Vložit do textové reference', 'uw'),
            'uploaded_to_this_item' => esc_html__('Nahráno k této textové referenci', 'uw'),
            'featured_image' => esc_html__('Hlavní obrázek', 'uw'),
            'set_featured_image' => esc_html__('Nastavit hlavní obrázek', 'uw'),
            'remove_featured_image' => esc_html__('Odstranit hlavní obrázek', 'uw'),
            'use_featured_image' => esc_html__('Použít jako hlavní obrázek', 'uw'),
            'menu_name' => esc_html__('Textové Reference', 'uw'),
            'filter_items_list' => esc_html__('Filtrovat seznam textových referencí', 'uw'),
            'filter_by_date' => esc_html__('', 'uw'),
            'items_list_navigation' => esc_html__('Navigace seznamu textových referencí', 'uw'),
            'items_list' => esc_html__('Seznam textových referencí', 'uw'),
            'item_published' => esc_html__('Textová reference publikována.', 'uw'),
            'item_published_privately' => esc_html__('Textová reference publikována soukromě.', 'uw'),
            'item_reverted_to_draft' => esc_html__('Textová reference převedena na koncept.', 'uw'),
            'item_scheduled' => esc_html__('Textová reference naplánována.', 'uw'),
            'item_updated' => esc_html__('Textová reference aktualizována.', 'uw'),
            'text_domain' => esc_html__('uw', 'uw'),
        ];
        
        $args = [
            'label' => esc_html__('Textové Reference', 'uw'),
            'labels' => $labels,
            'description' => 'Systém pro správu textových referencí od klientů',
            'public' => false,
            'hierarchical' => false,
            'exclude_from_search' => true,
            'publicly_queryable' => false,
            'show_ui' => true,
            'show_in_nav_menus' => false,
            'show_in_admin_bar' => true,
            'show_in_rest' => false,
            'query_var' => false,
            'can_export' => true,
            'delete_with_user' => false,
            'has_archive' => false,
            'rest_base' => '',
            'show_in_menu' => true,
            'menu_icon' => 'dashicons-testimonial',
            'menu_position' => 26,
            'capability_type' => 'post',
            'supports' => ['title', 'editor'],
            'taxonomies' => [],
            'rewrite' => false,
        ];

        register_post_type(self::CPT_KEY, $args);
    }

    /**
     * Register custom fields for text testimonials
     * 
     * @param array<mixed> $meta_boxes
     * @return array<mixed>
     */
    public function text_testimonials_custom_fields(mixed $meta_boxes): mixed
    {
        $meta_boxes[] = [
            'title' => __('Informace o autorovi reference', 'uw'),
            'post_types' => self::CPT_KEY,
            'context' => 'normal',
            'priority' => 'high',
            'fields' => [
                [
                    'name' => __('Celé jméno osoby', 'uw'),
                    'id' => self::PREFIX . self::TEXT_TESTIMONIALS_FIELD_PERSON_NAME,
                    'type' => 'text',
                    'required' => true,
                    'desc' => __('Zadejte celé jméno osoby, která poskytla referenci', 'uw'),
                ],
                [
                    'name' => __('Pozice a název firmy', 'uw'),
                    'id' => self::PREFIX . self::TEXT_TESTIMONIALS_FIELD_POSITION_COMPANY,
                    'type' => 'text',
                    'required' => true,
                    'desc' => __('Např: "Marketingový manažer, ABC s.r.o."', 'uw'),
                ],
                [
                    'name' => __('Fotografie nebo logo', 'uw'),
                    'id' => self::PREFIX . self::TEXT_TESTIMONIALS_FIELD_PHOTO_LOGO,
                    'type' => 'single_image',
                    'required' => false,
                    'desc' => __('Nahrajte fotografii osoby nebo logo firmy (doporučená velikost: 80x80px)', 'uw'),
                ],
                [
                    'name' => __('Volitelný nadpis reference', 'uw'),
                    'id' => self::PREFIX . self::TEXT_TESTIMONIALS_FIELD_OPTIONAL_TITLE,
                    'type' => 'text',
                    'required' => false,
                    'desc' => __('Volitelný nadpis, který se zobrazí nad textem reference', 'uw'),
                ],
            ]
        ];
        
        return $meta_boxes;
    }

    /**
     * Get custom field value
     * 
     * @param string $field_name
     * @param int $post_id
     * @return mixed
     */
    public static function get_field_value(string $field_name, int $post_id): mixed
    {
        return rwmb_meta(self::PREFIX . $field_name, [], $post_id);
    }
}
