<?php

namespace Umimeweby\UWTheme\AdminDashboard;

use Um<PERSON>weby\UWTheme\ShortCodes\Components\Carousel_References_Shortcode;
use Um<PERSON>weby\UWTheme\ShortCodes\Components\Client_Logos_Shortcode;
use Um<PERSON><PERSON>by\UWTheme\ShortCodes\Components\Mini_Case_Studies_Shortcode;
use Umimeweby\UWTheme\ShortCodes\Components\New_Or_Existing_Projects_Shortcode;
use Umimeweby\UWTheme\ShortCodes\Components\References_By_Category_Shortcode;
use Umimeweby\UWTheme\ShortCodes\Components\References_Filtered_Shortcode;
use Umimeweby\UWTheme\ShortCodes\Components\Technologies_Shortcode;
use Umimeweby\UWTheme\ShortCodes\Components\Text_Testimonials_Shortcode;
use Umimeweby\UWTheme\ShortCodes\Components\Why_Select_Us_Shortcode;

class Dashboard_UW_Widgets_Manager
{


  public function __construct()
  {
    add_action('wp_dashboard_setup', [$this, 'add_widget_shortcode_list']);
  }


  public function add_widget_shortcode_list(): void
  {
    wp_add_dashboard_widget(
      'wporg_dashboard_widget',                               // Widget slug.
      esc_html__('UW Theme - Available Shortcodes', 'wporg'), // Title.
      [$this, 'render_shortcode_list_widget_content']         // Display function.
    );
  }


  public function render_shortcode_list_widget_content(): void
  {


    /**
     * Mini Case Studies
     */

    echo ('<strong>Mini Case Studies</strong><br>');
    echo ('<strong>[' . Mini_Case_Studies_Shortcode::SHORTCODE . ']</strong><br>');
    $url = get_template_directory_uri() . '/assets/images/shortcodes/mini-pripadovky.jpg';

    $text = 'Zobrazení seznamu mini-případovek.<br>Seznam se definuje <a href="/wp-admin/admin.php?page=uw-settings-mini-casestudies">v administraci zde.</a><br>
        Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a>
        <br>
        <br>
        ';
    echo ($text);


    /**
     * CTA Cards
     */

    echo ('<strong>CTA Card pro přihlášení k newsletteru</strong><br>');
    echo ('<strong>[cta-nl-card]</strong><br>');
    $url = get_template_directory_uri() . '/assets/images/shortcodes/cta-nl-card.png';

    $text = 'Zobrazí na stránce card s formulářem pro přihlášení se k newsletteru<br>
         <br>
         Shortcode má následující parametry:<br>
         title - hlavní nadpis karty (volitelný, výchozí: "Každý nový článek&lt;br&gt;u vás v emailu")<br>
         subtitle - popisný text pod nadpisem (volitelný, výchozí: "Užitečné tipy z oblasti využití webu a online světa pro váš byznys a firmu.")<br>
         <br>
         Příklad použití shortcode:<br>
         [cta-nl-card]<br>
         [cta-nl-card title="Vlastní nadpis" subtitle="Vlastní popisek"]<br>
         <br>
         Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a><br>
         <br>
         <br>
        ';
    echo ($text);


    /**
     * Client Logo Section
     */

    echo ('<strong>Client Logo section</strong><br>');
    echo ('<strong>[' . Client_Logos_Shortcode::SHORTCODE . ']</strong><br>');
    $url = get_template_directory_uri() . '/assets/images/shortcodes/loga-klientu.png';

    $text = 'Zobrazení seznamu log našich klientů.<br>Seznam se definuje <a href="/wp-admin/admin.php?page=uw-settings-client-logos">v administraci zde.</a><br>
         Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a>
         <br>
         <br>
         ';
    echo ($text);


    /**
     * Why Select US Section
     */

    echo ('<strong>Why Select US Section</strong><br>');
    echo ('<strong>[' . Why_Select_Us_Shortcode::SHORTCODE . ']</strong><br>');
    $url = get_template_directory_uri() . '/assets/images/shortcodes/why-selectus.png';

    $text = 'Zobrazení sloupečků s důvody , proč si vybrat právě naši firmu jako dodavatele.<br>Seznam se definuje <a href="/wp-admin/admin.php?page=uw-settings-whyselectuscards">v administraci zde.</a><br>
          Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a>
          <br>
          <br>
          ';
    echo ($text);


    /**
     * Technologie section
     */

    echo ('<strong>Technologie Section</strong><br>');
    echo ('<strong>[' . Technologies_Shortcode::SHORTCODE . ']</strong><br>');
    $url = get_template_directory_uri() . '/assets/images/shortcodes/technologie.png';

    $text = 'Tato funkcionalita umožňuje přidání sekce s technologiemi, které používáme.<br>Seznam se definuje <a href="/wp-admin/admin.php?page=uw-settings-technologies">v administraci zde.</a><br>
           Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a>
           <br>
           <br>
           ';
    echo ($text);


    /**
     * Carousel references Section 
     */

    echo ('<strong>Carousel s vybranými referencemi</strong><br>');
    echo ('<strong>[' . Carousel_References_Shortcode::SHORTCODE . ']</strong><br>');
    $url = get_template_directory_uri() . '/assets/images/shortcodes/carousel-reference.png';

    $text = 'Tato funkcionalita umožňuje přidání sekce s carousel s vybranými referencemi.<br>Seznam se definuje <a href="/wp-admin/admin.php?page=uw-settings-carousel-references">v administraci zde.</a><br>
            Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a>
            <br>
            <br>
            ';
    echo ($text);



    /**
     * Carousel references Section 
     */

    echo ('<strong>Reference s filtrem kategorií</strong><br>');
    echo ('<strong>[' . References_By_Category_Shortcode::SHORTCODE . ']</strong><br>');
    $url = get_template_directory_uri() . '/assets/images/shortcodes/reference-filtr-kategorie.png';

    $text = '
        Tata funkcionalita umožňuje přidání sekce s referencemi, kde je ároveň možnost filtrování podle kategorií.Současně definuje shortkód, kterým lze tuto sekci generovat podle potřeby.<br>
        <br>
         Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a>
        <br>
        <br>
        ';
    echo ($text);


    /**
     * References Filtered Section (Universal) 
     */

    echo ('<strong>Reference - univerzální filtrování</strong><br>');
    echo ('<strong>[' . References_Filtered_Shortcode::SHORTCODE . ']</strong><br>');

    $text = '
        Univerzální shortcode pro zobrazení referencí s různými typy filtrování. Kombinuje funkcionalitu shortcodů uw-references-by-ids a uw-references-by-tag do jednoho.<br>
        <br>
        Shortcode má následující parametry:<br>
        type - typ filtrování: "ids" nebo "tag" (povinný)<br>
        ids - seznam ID referencí oddělených čárkou (pro type="ids", např. ids="123,456,789")<br>
        tagname - název tagu (pro type="tag", např. tagname="wordpress")<br>
        limit - maximální počet zobrazených referencí (volitelný, výchozí: všechny pro tag, ignoruje se pro ids)<br>
        title - nadpis sekce (volitelný)<br>
        description - popis sekce (volitelný)<br>
        <br>
        Příklad použití shortcode:<br>
        [' . References_Filtered_Shortcode::SHORTCODE . ' type="ids" ids="123,456,789" title="Vybrané projekty"]<br>
        [' . References_Filtered_Shortcode::SHORTCODE . ' type="tag" tagname="wordpress" limit="3" title="WordPress projekty"]<br>
        <br>
        Layout se automaticky přizpůsobuje počtu referencí - všechny karty mají stejnou šířku a jsou vycentrované.<br>
        <br>
        <br>
        ';
    echo ($text);


    /**
     * New or Existing CTA Section 
     */

    echo ('<strong>CTA s odkazem na Nový projekt x předat existující projekt</strong><br>');
    echo ('<strong>[' . New_Or_Existing_Projects_Shortcode::SHORTCODE . ']</strong><br>');
    $url = get_template_directory_uri() . '/assets/images/shortcodes/new-or-existing.png';

    $text = '
        Tato funkcionalita umožňuje přidání sekce s CTA na přechod na stránku Nový projekt nebo Předat existující projektSoučasně definuje shortkód, kterým lze tuto sekci generovat podle potřeby.<br>
        <br>
         Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a>
        <br>
        <br>
        ';
    echo ($text);


    /**
     * Text testimonials section
     */

    echo ('<strong>Textové reference</strong><br>');
    echo ('<strong>[' . Text_Testimonials_Shortcode::SHORTCODE . ']</strong><br>');
    $url = get_template_directory_uri() . '/assets/images/shortcodes/new-or-existing.png';

    $text = '
        Zobrazuje seznam textových referencí. textové reference se zadávají v administraci <a href="/wp-admin/edit.php?post_type=uwtexttestimonials">v administraci zde.</a><br>
        <br>
        Shortcode může mít následující  parametry<br>
        ids - seznam ID referencí oddělených čárkou (např. ids="123,456,789")<br>
        limit - maximální počet zobrazených referencí (výchozí: 3)<br>
        columns - počet sloupců (výchozí: 3)<br>
        <br>
        Příklad použití shortcode:<br>
        [testimonials ids="123,456,789"]<br>
        [testimonials ids="123,456,789" limit="2" columns="2"]<br>
        <br>
         Design na stránce vypadá např <a href="' . $url . '" target="_blank">takto </a>
        <br>
        <br>
        ';
    echo ($text);
  }
}
