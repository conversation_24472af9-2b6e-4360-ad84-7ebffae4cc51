<?php

namespace Umimeweby\UWTheme\ShortCodes;

abstract class Abstract_UW_Shortcode
{


    /**
     * 
     * @param mixed $atts 
     * @return string 
     */
    abstract public function get_shortcode_content($atts):string;
    abstract public function get_shortcode_code():string;

    public function __construct()
    {
        add_shortcode($this->get_shortcode_code(), [$this, 'get_shortcode_content']);
    }

    protected function sanitize_output(string $content): string {
        return wp_kses_post($content);
    }

    /**
     * 
     * @return array<mixed>
     */
    protected function get_default_attributes(): array {
        return [];
    }

    /**
     * 
     * @param mixed $atts 
     * @return array<mixed>
     */
    protected function prepare_attributes($atts): array {
        return wp_parse_args($atts, $this->get_default_attributes());
    }
}
