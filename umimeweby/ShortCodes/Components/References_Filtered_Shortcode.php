<?php

namespace Umimeweby\UWTheme\ShortCodes\Components;

use Um<PERSON>weby\UWTheme\ShortCodes\Abstract_UW_Shortcode;

class References_Filtered_Shortcode extends Abstract_UW_Shortcode
{

    public const SHORTCODE = 'uw-references-filtered';

    /**
     * 
     * @param mixed $atts 
     * @return string 
     */
    public function get_shortcode_content($atts): string
    {
        $attributes = $this->prepare_attributes($atts);
        return $this->get_html_code($attributes);
    }

    public function get_shortcode_code(): string
    {
        return self::SHORTCODE;
    }

    /**
     * 
     * @return array<mixed>
     */
    protected function get_default_attributes(): array
    {
        return [
            'type' => 'ids', // 'ids' nebo 'tag'
            'ids' => '',
            'tagname' => '',
            'limit' => -1,
            'title' => '',
            'description' => ''
        ];
    }

    /**
     * 
     * @param array<mixed> $attributes
     * @return string 
     */
    private function get_html_code(array $attributes): string
    {
        ob_start();
        include(get_stylesheet_directory() . '/template-parts/content/reference/references_filtered.php');
        $result = ob_get_clean();
        return $result;
    }
}
