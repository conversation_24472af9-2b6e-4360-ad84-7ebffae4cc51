<?php

namespace Umimeweby\UWTheme\ShortCodes\Components;

use Um<PERSON>weby\UWTheme\ShortCodes\Abstract_UW_Shortcode;
use Umimeweby\UWTheme\CustomPostType\Text_Testimonials_Custom_Post_Type;

class Text_Testimonials_Shortcode extends Abstract_UW_Shortcode
{
    public const SHORTCODE = 'uwtexttestimonials';

    public function get_shortcode_code(): string
    {
        return self::SHORTCODE;
    }

    protected function get_default_attributes(): array
    {
        return [
            'ids' => '',
            'limit' => 3,
            'columns' => 3,
        ];
    }

    public function get_shortcode_content($atts): string
    {
        $attributes = $this->prepare_attributes($atts);
        
        // Sanitize and validate attributes
        $ids = sanitize_text_field($attributes['ids']);
        $limit = max(1, intval($attributes['limit']));
        $columns = max(1, min(3, intval($attributes['columns'])));
        
        // Get testimonials
        $testimonials = $this->get_testimonials($ids, $limit);
        if ($columns > count($testimonials))
        {
            $columns =  count($testimonials);
        }
        
        // Debug: Check if we have testimonials
        if (empty($testimonials)) return '';
        return $this->render_testimonials($testimonials, $columns);
    }

    /**
     * Get testimonials based on IDs or limit
     * 
     * @param string $ids
     * @param int $limit
     * @return array<\WP_Post>
     */
    private function get_testimonials(string $ids, int $limit): array
    {
        $query_args = [
            'post_type' => Text_Testimonials_Custom_Post_Type::CPT_KEY,
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'orderby' => 'menu_order',
            'order' => 'ASC',
        ];
        
        // If specific IDs are provided, use them
        if (!empty($ids)) {
            $id_array = array_filter(array_map('intval', explode(',', $ids)));
            if (!empty($id_array)) {
                $query_args['post__in'] = $id_array;
                $query_args['orderby'] = 'post__in';
                $query_args['posts_per_page'] = count($id_array);
            }
        }
        
        $query = new \WP_Query($query_args);
        return $query->posts;
    }

    /**
     * Render testimonials HTML
     * 
     * @param array<\WP_Post> $testimonials
     * @param int $columns
     * @return string
     */
    private function render_testimonials(array $testimonials, int $columns): string
    {
        $flex_classes = $this->get_flex_classes($columns);
        
        $html = '<div class="w-full mx-auto">';
        $html .= '<div class="' . esc_attr($flex_classes) . '">';
        
        foreach ($testimonials as $testimonial) {
            $html .= $this->render_single_testimonial($testimonial);
        }
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $this->sanitize_output($html);
    }

    /**
     * Get CSS grid classes based on column count (responsive)
     * 
     * @param int $columns
     * @return string
     */
    private function get_flex_classes(int $columns): string
    {
        // Always start with 1 column on mobile, then scale up
        $base_classes = 'grid gap-6';
        
        switch ($columns) {
            case 1:
                return $base_classes . ' grid-cols-1 md:max-w-[50%] md:mx-auto';
            case 2:
                return $base_classes . ' grid-cols-1 md:grid-cols-2';
            case 3:
            default:
                return $base_classes . ' grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
        }
    }

    /**
     * Render single testimonial card
     * 
     * @param \WP_Post $testimonial
     * @return string
     */
    private function render_single_testimonial(\WP_Post $testimonial): string
    {
        // Get custom fields
        $person_name = Text_Testimonials_Custom_Post_Type::get_field_value(
            Text_Testimonials_Custom_Post_Type::TEXT_TESTIMONIALS_FIELD_PERSON_NAME, 
            $testimonial->ID
        );
        $position_company = Text_Testimonials_Custom_Post_Type::get_field_value(
            Text_Testimonials_Custom_Post_Type::TEXT_TESTIMONIALS_FIELD_POSITION_COMPANY, 
            $testimonial->ID
        );
        $photo_logo = Text_Testimonials_Custom_Post_Type::get_field_value(
            Text_Testimonials_Custom_Post_Type::TEXT_TESTIMONIALS_FIELD_PHOTO_LOGO, 
            $testimonial->ID
        );
        $optional_title = Text_Testimonials_Custom_Post_Type::get_field_value(
            Text_Testimonials_Custom_Post_Type::TEXT_TESTIMONIALS_FIELD_OPTIONAL_TITLE, 
            $testimonial->ID
        );

        $html = '<div class="bg-gray-50 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow duration-200 w-full">';
        
        // Optional title
        if (!empty($optional_title)) {
            $html .= '<h3 class="text-lg font-semibold text-gray-900 mb-3">' . esc_html($optional_title) . '</h3>';
        }
        
        // Testimonial content
        $content = apply_filters('the_content', $testimonial->post_content);
        $html .= '<div class="text-gray-700 leading-relaxed mb-6 prose">';
        $html .= wp_kses_post($content);
        $html .= '</div>';
        
        // Author section
        $html .= '<div class="flex items-center space-x-4">';
        
        // Photo/Logo
        if (!empty($photo_logo)) {
            $image_url = '';
            $image_alt = $person_name ?? 'Testimonial author';
            
            // Handle Meta Box single_image field type
            if (is_array($photo_logo)) {
                // single_image field returns image data directly in array
                if (isset($photo_logo['sizes']['thumbnail']['url'])) {
                    // Use thumbnail size for better performance
                    $image_url = $photo_logo['sizes']['thumbnail']['url'];
                } elseif (isset($photo_logo['url'])) {
                    // Fallback to main URL
                    $image_url = $photo_logo['url'];
                } elseif (isset($photo_logo['full_url'])) {
                    // Fallback to full URL
                    $image_url = $photo_logo['full_url'];
                }
                
                // Get alt text
                if (!empty($photo_logo['alt'])) {
                    $image_alt = $photo_logo['alt'];
                } elseif (!empty($photo_logo['title'])) {
                    $image_alt = $photo_logo['title'];
                }
            } elseif (is_numeric($photo_logo)) {
                // Fallback for attachment ID format
                $attachment_id = intval($photo_logo);
                $image_url = wp_get_attachment_image_url($attachment_id, 'thumbnail');
                $image_alt = get_post_meta($attachment_id, '_wp_attachment_image_alt', true) ?: $image_alt;
            }
            
            if (!empty($image_url)) {
                $html .= '<div class="flex-shrink-0">';
                $html .= '<img class="w-12 h-12 rounded-full object-cover border-2 border-gray-200" ';
                $html .= 'src="' . esc_url($image_url) . '" ';
                $html .= 'alt="' . esc_attr($image_alt) . '" />';
                $html .= '</div>';
            }
        }
        
        // Author info
        $html .= '<div class="flex-grow">';
        if (!empty($person_name)) {
            $html .= '<div class="font-semibold text-gray-900">' . esc_html($person_name) . '</div>';
        }
        if (!empty($position_company)) {
            $html .= '<div class="text-sm text-gray-600">' . esc_html($position_company) . '</div>';
        }
        $html .= '</div>';
        
        $html .= '</div>'; // End author section
        $html .= '</div>'; // End card
        
        return $html;
    }
}
