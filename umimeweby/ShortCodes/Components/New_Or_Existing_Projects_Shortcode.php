<?php

namespace Umimeweby\UWTheme\ShortCodes\Components;

use Umimeweby\UWTheme\ShortCodes\Abstract_UW_Shortcode;

class New_Or_Existing_Projects_Shortcode extends Abstract_UW_Shortcode
{

    public const SHORTCODE = 'uw-new-or-existing-component';

    public function get_shortcode_content($atts): string 
    { 
        return $this->get_html_code();
    }

    public function get_shortcode_code(): string 
    { 
        return self::SHORTCODE;
    }

        private function get_html_code(): string
    {
        ob_start();
        include(get_stylesheet_directory() . '/template-parts/components/services/_new_or_existing_projects.php');
        $result = ob_get_clean();
        return $result;
    }

}
