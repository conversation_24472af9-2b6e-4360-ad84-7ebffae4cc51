<?php

namespace Umimeweby\UWTheme\ShortCodes\Components;

use Umimeweby\UWTheme\ShortCodes\Abstract_UW_Shortcode;

class References_By_Category_Shortcode extends Abstract_UW_Shortcode
{

    public const SHORTCODE = 'uw-references-by-category';

    /**
     * 
     * @param mixed $atts 
     * @return string 
     */
    public function get_shortcode_content($atts): string
    {
        return $this->get_html_code();
    }

    public function get_shortcode_code(): string
    {
        return self::SHORTCODE;
    }

    private function get_html_code(): string
    {
        ob_start();
        include(get_stylesheet_directory() . '/template-parts/content/reference/references_by_category.php');
        $result = ob_get_clean();
        return $result;
    }
}
