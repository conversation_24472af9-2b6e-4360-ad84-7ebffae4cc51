<?php

namespace Umimeweby\UWTheme\ShortCodes;

use Um<PERSON><PERSON>by\UWTheme\ShortCodes\Components\Carousel_References_Shortcode;
use Um<PERSON>weby\UWTheme\ShortCodes\Components\Client_Logos_Shortcode;
use Umime<PERSON>by\UWTheme\ShortCodes\Components\Mini_Case_Studies_Shortcode;
use Umimeweby\UWTheme\ShortCodes\Components\New_Or_Existing_Projects_Shortcode;
use Umimeweby\UWTheme\ShortCodes\Components\References_By_Category_Shortcode;
use Umimeweby\UWTheme\ShortCodes\Components\References_Filtered_Shortcode;
use Umimeweby\UWTheme\ShortCodes\Components\Technologies_Shortcode;
use Umimeweby\UWTheme\ShortCodes\Components\Text_Testimonials_Shortcode;
use Umimeweby\UWTheme\ShortCodes\Components\Why_Select_Us_Shortcode;
use Umimeweby\UWTheme\ShortCodes\CTA\CTA_Newsletter_Card_Shortcode;

class Shortcode_Provider_Service
{

    /**
     * 
     * @var array<mixed>
     */
    private array $shortcodes = [];

    /**
     * Register all shortcodes
     */
    public function register(): void
    {
        $this->shortcodes = [
            new Mini_Case_Studies_Shortcode(),
            new CTA_Newsletter_Card_Shortcode(),
            new Client_Logos_Shortcode(),
            new Why_Select_Us_Shortcode(),
            new Technologies_Shortcode(),
            new Carousel_References_Shortcode(),
            new References_By_Category_Shortcode(),
            new References_Filtered_Shortcode(),
            new New_Or_Existing_Projects_Shortcode(),
            new Text_Testimonials_Shortcode()
        ];
    }

    /**
     * Get array of all registered shortcodes
     * @return Abstract_UW_Shortcode[]
     */
    public function getRegisteredShortcodes(): array
    {
        return $this->shortcodes;
    }
}
