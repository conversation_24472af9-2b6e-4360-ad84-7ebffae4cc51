<?php

namespace Umimeweby\UWTheme\ShortCodes\CTA;

use Um<PERSON>weby\UWTheme\ShortCodes\Abstract_UW_Shortcode;

class CTA_Newsletter_Card_Shortcode extends Abstract_UW_Shortcode
{
    public const SHORTCODE = 'cta-nl-card';

    public function get_shortcode_code(): string
    {
        return self::SHORTCODE;
    }

    protected function get_default_attributes(): array
    {
        return [
            'title' => 'Každý nov<PERSON><br>u vás v emailu',
            'subtitle' => 'Užitečné tipy z oblasti využití webu a online světa pro váš byznys a firmu.'
        ];
    }

    /**
     * provides form HTML in shortcode output
     *
     * @param  mixed $atts
     * @return string
     */
    public function get_shortcode_content($atts): string
    {
        $attributes = $this->prepare_attributes($atts);
        return $this->get_html_code($attributes);
    }

    /**
     * 
     * @param mixed $attributes 
     * 
     */
    private function get_html_code($attributes): string
    {        
        ob_start();
        include(get_stylesheet_directory() . '/template-parts/promo-sections/newsletter/_newsletter_card_cta.php');
        $result = ob_get_clean();
        return $result;
    }
}
