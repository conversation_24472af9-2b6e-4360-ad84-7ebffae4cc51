<?php

namespace Umimeweby\UWTheme\AdminTables\CustomTaxonomy;

use Umimeweby\UWTheme\Taxonomies\UW_Service_Custom_Taxonomy;

class UW_Services_Taxonomy_Columns
{


    public function __construct()
    {
        // Přidání nového sloupce do admin tabulky
        add_filter('manage_edit-' . UW_Service_Custom_Taxonomy::TAXONOMY_KEY . '_columns', [
            $this,
            'add_custom_columns'
        ]);

        // Zobrazení hodnoty custom field v novém sloupci
        add_filter(
            'manage_' . UW_Service_Custom_Taxonomy::TAXONOMY_KEY . '_custom_column',
            [
                $this,
                'add_custom_columns_content'
            ],
            10,
            3
        );
    }


    /**
     * 
     * @param mixed $columns 
     * @return array<mixed>
     */
    public function add_custom_columns($columns):array
    {
        // Vytvoření nového pole sloupců s přidaným custom field
        $new_columns = array();

        // Zkopírování existujíc<PERSON>ch sloupců až po sloupec 'name'
        foreach ($columns as $key => $title) {
            $new_columns[$key] = $title;
            // Vložení nového sloupce za název
            if ($key == 'name') {
                // Změň 'custom_field_name' na název tvého custom field
                $new_columns[UW_Service_Custom_Taxonomy::TAXONOMY_PREFIX . UW_Service_Custom_Taxonomy::TAXONOMY_FIELD_ORDER] = __('Pořadí', 'text-domain');
            }
        }

        return $new_columns;
    }



    /**
     * 
     * @param mixed $content 
     * @param mixed $column_name 
     * @param mixed $term_id 
     * @return mixed 
     */
    public function add_custom_columns_content($content, $column_name, $term_id)
    {
        if ($column_name === UW_Service_Custom_Taxonomy::TAXONOMY_PREFIX . UW_Service_Custom_Taxonomy::TAXONOMY_FIELD_ORDER) {
            // Získání hodnoty custom field pro daný term
            $value = get_term_meta($term_id, UW_Service_Custom_Taxonomy::TAXONOMY_PREFIX . UW_Service_Custom_Taxonomy::TAXONOMY_FIELD_ORDER, true);
            return $value ? $value : '-';
        }
        return $content;
    }
}
