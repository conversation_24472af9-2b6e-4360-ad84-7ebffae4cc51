<?php

namespace Umimeweby\UWTheme\AdminTables;

use Umimeweby\UWTheme\AdminTables\CustomTaxonomy\UW_Services_Taxonomy_Columns;

class Admin_Tables_Modifications_Provider
{

    /**
     * 
     * @var array<mixed>
     */
    private array $modifications = [];

    public function register_modifications(): void
    {
        $this->modifications = [
            new PageTemplateColumn(),
            new UW_Services_Taxonomy_Columns()
        ];
    }

    /**
     * Get all registered modifications
     * @return array<mixed> 
     */
    public function getModifications(): array
    {
        return $this->modifications;
    }
}
