<?php

namespace Umimeweby\UWTheme\AdminTables;

class PageTemplateColumn
{
    public function __construct()
    {
        // Přidání nového sloupce do seznamu stránek
        add_filter('manage_pages_columns', [$this, 'addTemplateColumn']);

        // Naplnění sloupce daty
        add_action('manage_pages_custom_column', [$this, 'displayTemplateColumn'], 10, 2);
    }

    /**
     * Přidá nový sloupec do tabulky stránek
     * 
     * @param mixed $columns 
     * @return mixed 
     */
    public function addTemplateColumn($columns)
    {
        $columns['page_template'] = __('Šablona stránky', 'uw-theme');
        return $columns;
    }

    /**
     * Zobrazí název šablony pro každou stránku
     * 
     * @param mixed $column_name 
     * @param mixed $post_id 
     * @return void 
     */
    public function displayTemplateColumn($column_name, $post_id)
    {
        if ($column_name !== 'page_template') {
            return;
        }

        $template = get_page_template_slug($post_id);

        if (empty($template)) {
            echo __('Výchozí šablona', 'uw-theme');
            return;
        }

        // Získání všech dostupných šablon
        $templates = wp_get_theme()->get_page_templates();

        // Zobrazení názvu šablony, pokud existuje
        if (isset($templates[$template])) {
            echo esc_html($templates[$template]);
        } else {
            echo esc_html($template);
        }
    }
}
