<?php

namespace Umimeweby\UWTheme\Service\EmailSender;

class Order_Form_Sprava_WP_Email_Sender
{
    public function send($data)
    {

        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: <EMAIL>'  // Změň na skutečný odesílatel
        );

        wp_mail(['<EMAIL>', '<EMAIL>'], "Objednávka - Wordpress, správa webu", $this->get_message($data), $headers);
    }

    public function get_message($data)
    {

        date_default_timezone_set('Europe/Prague');

        $content = '';
        $content .= '<strong>Jméno: </strong>' . $data['order-name'] . '<br/>';
        $content .= '<strong>Příjmení: </strong>' . $data['order-lname'] . '<br/>';
        $content .= '<strong>Tel.: </strong>' . $data['order-telephone'] . '<br/>';
        $content .= '<strong>Email: </strong>' . $data['order-email'] . '<br/>';
        $content .= '<strong>Firma: </strong>' . $data['order-jmeno'] . '<br/>';
        $content .= '<strong>IČ: </strong>' . $data['order-ic'] . '<br/>';
        $content .= '<strong>url: </strong>' . $data['order-url'] . '<br/>';
        $content .= '<strong>Vybraný plán: </strong>' . $data['order-text'] . '<br/>';
        $content .= '<strong>Souhlas s obchodníma podmínkama: </strong>' . $data['agree-terms'] . '<br/>';

        return $content;
    }
}
