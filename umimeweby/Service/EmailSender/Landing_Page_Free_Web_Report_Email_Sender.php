<?php

namespace Umimeweby\UWTheme\Service\EmailSender;

class Landing_Page_Free_Web_Report_Email_Sender
{


    /**
     *
     * @param  mixed[] $data
     * @return void
     */
    public function send(array $data): void
    {

        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: <EMAIL>'  // Změň na skutečný odesílatel
        );

        wp_mail(['<EMAIL>', '<EMAIL>'], "LP[free-web-report] - nový požadavek", $this->get_message($data), $headers);
    }

    /**
     * Undocumented function
     *
     * @param  array<mixed>  $data
     * @return string
     */
    public function get_message(array $data):string
    {

        date_default_timezone_set('Europe/Prague');

        $content = '';
        $content .= '<strong>Jméno: </strong>' . $data['name'] . '<br/>';
        $content .= '<strong>Příjmení: </strong>' . $data['lname'] . '<br/>';
        $content .= '<strong>Tel.: </strong>' . $data['telephone'] . '<br/>';
        $content .= '<strong>Email: </strong>' . $data['email'] . '<br/>';
        $content .= '<strong>Web: </strong>' . $data['web'] . '<br/>';

        return $content;
    }
}
