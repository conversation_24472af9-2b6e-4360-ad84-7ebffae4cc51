<?php

namespace Umimeweby\UWTheme\Service\EmailSender;

class Contact_Form_Sprava_WP_Email_Sender
{
    public function send($data)
    {

        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: <EMAIL>'  // Změň na skutečný odesílatel
        );

        wp_mail(['<EMAIL>', '<EMAIL>'], "Kontaktní formulář - Wordpress, správa webu", $this->get_message($data), $headers);
    }

    public function get_message($data)
    {

        date_default_timezone_set('Europe/Prague');

        $content = '';
        $content .= '<strong>Jméno: </strong>' . $data['name'] . '<br/>';
        $content .= '<strong>Příjmení: </strong>' . $data['lname'] . '<br/>';
        $content .= '<strong>Tel.: </strong>' . $data['telephone'] . '<br/>';
        $content .= '<strong>Email: </strong>' . $data['email'] . '<br/>';
        $content .= '<strong>Zprava: </strong>' . $data['message'] . '<br/>';

        return $content;
    }
}
