<?php
namespace Umimeweby\UWTheme\Service\EmailSender;

class Generic_Form_Email_Sender
{
    /**
     * @param mixed[] $data
     */
    public function send(array $data) : void
    {

        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: <EMAIL>'  // Změň na skutečný odesílatel
        );

        wp_mail(['<EMAIL>', '<EMAIL>'], "Kontaktní formulář - Wordpress, správa webu", $this->get_message($data), $headers);
    }

    /**
     * @param mixed[] $data
     */
    public function get_message(array $data) : string
    {

        date_default_timezone_set('Europe/Prague');

        $content = '';
        $content .= '<strong>Jméno: </strong>' . $data['first-name'] . '<br/>';
        $content .= '<strong>Příjmení: </strong>' . $data['last-name'] . '<br/>';
        $content .= '<strong>Tel.: </strong>' . $data['tel'] . '<br/>';
        $content .= '<strong>Email: </strong>' . $data['email'] . '<br/>';
        $content .= '<strong>Zprava: </strong>' . $data['message'] . '<br/>';

        return $content;
    }
}