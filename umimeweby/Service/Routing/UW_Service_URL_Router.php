<?php

namespace Umimeweby\UWTheme\Service\Routing;

use Um<PERSON>weby\UWTheme\CustomPostType\UW_Service_Custom_Post_Type;
use Umimeweby\UWTheme\Taxonomies\UW_Service_Custom_Taxonomy;

/**
 * Custom URL Router pro UW Services
 * 
 * Řeší prioritní routing pro /sluzby URLs:
 * 1. /sluzby/nazev_postu → single post (priorita 1)
 * 2. /sluzby/nazev_kategorie → taxonomy archive (priorita 2)  
 * 3. /sluzby → CPT archive
 * 4. /sluzby/cokoliv_jineho → 404
 */
class UW_Service_URL_Router
{
    public function __construct()
    {
        add_action('init', [$this, 'add_custom_rewrite_rules'], 10);
        add_filter('query_vars', [$this, 'register_query_vars']);
        add_filter('request', [$this, 'handle_custom_query_vars']);
        add_action('template_redirect', [$this, 'handle_routing_logic']);
    }

    /**
     * <PERSON><PERSON>id<PERSON> custom rewrite rules pro /sluzby routing
     */
    public function add_custom_rewrite_rules(): void
    {
        // Rule pro /sluzby/something - zachytí vše pod /sluzby/
        add_rewrite_rule(
            '^sluzby/([^/]+)/?$',
            'index.php?uw_service_router=1&uw_service_slug=$matches[1]',
            'top'
        );
        
        // Rule pro /sluzby - archive page
        add_rewrite_rule(
            '^sluzby/?$',
            'index.php?post_type=' . UW_Service_Custom_Post_Type::CPT_KEY,
            'top'
        );
    }

    /**
     * Registruje custom query vars
     * 
     * @param mixed $vars 
     * @return array<mixed> 
     */
    public function register_query_vars($vars): array
    {
        $vars[] = 'uw_service_router';
        $vars[] = 'uw_service_slug';
        return $vars;
    }

    /**
     * Zpracuje custom query vars a rozhodne o routingu
     * 
     * @param mixed $query_vars 
     * @return array<mixed>
     */
    public function handle_custom_query_vars($query_vars): array
    {
        // Pokud není náš custom router aktivní, vrátíme původní query
        if (!isset($query_vars['uw_service_router'])) {
            return $query_vars;
        }

        $slug = $query_vars['uw_service_slug'] ?? '';
        
        if (empty($slug)) {
            return $query_vars;
        }

        // Priorita 1: Zkusíme najít post s tímto slugem
        $post = get_page_by_path($slug, OBJECT, UW_Service_Custom_Post_Type::CPT_KEY);
        
        if ($post && $post->post_status === 'publish') {
            // Našli jsme post - přesměrujeme na single post
            return [
                'post_type' => UW_Service_Custom_Post_Type::CPT_KEY,
                'name' => $slug,
                'uw_service_router' => '1'
            ];
        }

        // Priorita 2: Zkusíme najít taxonomy term s tímto slugem
        $term = get_term_by('slug', $slug, UW_Service_Custom_Taxonomy::TAXONOMY_KEY);
        
        if ($term && !is_wp_error($term)) {
            // Našli jsme taxonomy term - přesměrujeme na taxonomy archive
            return [
                'taxonomy' => UW_Service_Custom_Taxonomy::TAXONOMY_KEY,
                'term' => $slug,
                'uw_service_router' => '1'
            ];
        }

        // Nic jsme nenašli - bude 404
        return [
            'error' => '404',
            'uw_service_router' => '1'
        ];
    }

    /**
     * Zpracuje routing logiku na template_redirect
     */
    public function handle_routing_logic(): void
    {
        if (!get_query_var('uw_service_router')) {
            return;
        }

        $slug = get_query_var('uw_service_slug');
        
        if (empty($slug)) {
            return;
        }

        // Priorita 1: Post
        $post = get_page_by_path($slug, OBJECT, UW_Service_Custom_Post_Type::CPT_KEY);
        if ($post && $post->post_status === 'publish') {
            // WordPress už má správné query vars nastavené
            return;
        }

        // Priorita 2: Taxonomy
        $term = get_term_by('slug', $slug, UW_Service_Custom_Taxonomy::TAXONOMY_KEY);
        if ($term && !is_wp_error($term)) {
            // WordPress už má správné query vars nastavené
            return;
        }

        // Priorita 3: 404
        global $wp_query;
        $wp_query->set_404();
        status_header(404);
    }

    /**
     * Flush rewrite rules při aktivaci
     */
    public static function flush_rewrite_rules(): void
    {
        flush_rewrite_rules();
    }
}
