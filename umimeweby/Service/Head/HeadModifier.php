<?php

namespace Umimeweby\UWTheme\Service\Head;

class HeadModifier
{

    public function __construct()
    {
        add_action('wp_head', [$this, 'insert_smarlook_code']);
    }

    public function insert_smarlook_code(): void
    {

        $current_domain = $_SERVER['HTTP_HOST'];

        if (($current_domain !== 'umimeweby.cz') && ($current_domain !== 'www.umimeweby.cz'))
        {
            return;
        }

           
        echo " <script type='text/javascript'> ";
        echo "
        window.smartlook||(function(d) {
            var o=smartlook=function(){ o.api.push(arguments)},h=d.getElementsByTagName('head')[0];
            var c=d.createElement('script');o.api=new Array();c.async=true;c.type='text/javascript';
            c.charset='utf-8';c.src='https://web-sdk.smartlook.com/recorder.js';h.appendChild(c);
            })(document);
            smartlook('init', '4c53f119981668f47c3cd76026b5c8f058a91d40', { region: 'eu' });
        </script>";


    }
}
