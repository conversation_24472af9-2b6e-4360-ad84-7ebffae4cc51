<?php

namespace Umimeweby\UWTheme\Service\GetResponse;

use Getresponse\Sdk\GetresponseClientFactory;
use Getresponse\Sdk\Operation\Contacts\CreateContact\CreateContact;
use Getresponse\Sdk\Operation\Model\CampaignReference;
use Getresponse\Sdk\Operation\Model\NewContact;
use Umimeweby\UWTheme\Settings\UW_GetReponse_Settings;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;

class GetResponse_Manager
{

    // 'ZBclb' is ID for API-UW-test list in GetReponse, for testing purposes only 
    // use wp-config.php and php function 'define' to define constant UW_NL_LIST_ID with newsletter list id


    public function __construct()
    {
     
    }

    public function add_email_to_newsletter_list(string $email):bool
    {
        $api_key = $this->get_api_key();
        if (is_null($api_key))
        {
            return false;
        }

        $newletter_id = $this->get_nl_id();
        if (is_null($newletter_id))
        {
            return false;
        }

        $client = GetresponseClientFactory::createWithApiKey($api_key);


        $list =  new CampaignReference($newletter_id);
        $new_contact = new NewContact($list, $email);
        $createContactOperation = new CreateContact($new_contact);
        $response = $client->call($createContactOperation);

        if ($response->isSuccess())
        {
            return true;
        }

        return false;
        
    }

    private function get_api_key():?string
    {
        $result = null;
        $api_key = rwmb_meta(
            UW_Settings_Page::PREFIX . UW_GetReponse_Settings::FIELD_GETRESPONSE_APIKEY,
            ['object_type' => 'setting'],
            UW_GetReponse_Settings::OPTION_GETRESPONSE_SETTING
        );
        if (empty($api_key) || is_null($api_key))
        {
            return $result;
        }

        return $api_key;
    }


    private function get_nl_id():?string
    {
        $result = null;
        $nl_id = rwmb_meta(
            UW_Settings_Page::PREFIX . UW_GetReponse_Settings::FIELD_GETRESPONSE_NLLISTID,
            ['object_type' => 'setting'],
            UW_GetReponse_Settings::OPTION_GETRESPONSE_SETTING
        );
        if (empty($nl_id) || is_null($nl_id))
        {
            return $result;
        }

        return $nl_id;

    }
}
