<?php

namespace Umimeweby\UWTheme\CustomFields;

class About_Us_Hero_Bottom
{

    const PREFIX = 'about_us_hero_bottom_';

    const ABOUT_US_HERO_TITLE = 'title';
    const ABOUT_US_HERO_DESC = 'desc';

    public function __construct()
    {
        add_filter('rwmb_meta_boxes', [$this, 'custom_fields_why_we']);
    }

    public function custom_fields_why_we(mixed $meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => __('Obsah sekce - O nás pod hero carouselem', 'uw-child-theme'),
            'post_types' => ['page'],
            'include' => [
                'relation' => 'AND',
                'slug' => ['homepage'],
            ],
            'fields' => [
                [
                    'name'             => __('Nadpis', 'uw-child-theme'),
                    'id'               => self::PREFIX . self::ABOUT_US_HERO_TITLE,
                    'type'             => 'text',
                ],
                [
                    'name'    => __('Popis', 'uw-child-theme'),
                    'id'      => self::PREFIX . self::ABOUT_US_HERO_DESC,
                    'type'    => 'wysiwyg',
                    'raw'     => false,
                    'options' => [
                        'textarea_rows' => 4,
                        'teeny'         => true,
                    ],
                ],
            ]
        ];
        return $meta_boxes;
    }
}
