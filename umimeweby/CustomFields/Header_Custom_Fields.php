<?php

namespace Umimeweby\UWTheme\CustomFields;

class Header_Custom_Fields
{

    const PREFIX = 'header_';
    const HEADER_CUSTOM_FIELDS_DESCRIPTION = 'description';
    const HEADER_CUSTOM_FIELDS_ANOTHER_DESCRIPTION = 'another_description';

    public function __construct()
    {
        add_filter('rwmb_meta_boxes', [$this, 'custom_fields_header']);
    }

    public function custom_fields_header(mixed $meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => __('Obsah headeru', 'uw-child-theme'),
            'post_types' => ['page'],
            'include' => [
                'relation' => 'AND',
                'slug' => ['o-nas', 'sluzby', 'wordpress-sprava', 'kontakt', 'reference'],
            ],
            'fields' => [
                [
                    'name'  => __('Popis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::HEADER_CUSTOM_FIELDS_DESCRIPTION,
                    'type'  => 'textarea',
                ],
                [
                    'name'  => __('<PERSON><PERSON><PERSON> popis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::HEADER_CUSTOM_FIELDS_ANOTHER_DESCRIPTION,
                    'type'  => 'textarea',
                ],
            ]
        ];
        return $meta_boxes;
    }

}