<?php

namespace Umimeweby\UWTheme\CustomFields\Services;

class Demand_Deliver_Custom_Fields
{

    const PREFIX = 'demand_deliver_';

    const DEMAND_DELIVER_FIELD_TITLE = 'title';
    const DEMAND_DELIVER_FIELD_DESCRIPTION = 'description';
    const DEMAND_DELIVER_FIELD_GROUP = 'group';
    const DEMAND_DELIVER_FIELD_GROUP_CATEGORY = 'category';
    const DEMAND_DELIVER_FIELD_GROUP_LEFT_TEXT_TITLE = 'left_text_title';
    const DEMAND_DELIVER_FIELD_GROUP_LEFT_TEXT_DESCRIPTION = 'left_text_description';
    const DEMAND_DELIVER_FIELD_GROUP_RIGHT_TEXT_TITLE = 'right_text_title';
    const DEMAND_DELIVER_FIELD_GROUP_RIGHT_TEXT_DESCRIPTION = 'right_text_description';

    public function __construct()
    {
        add_filter('rwmb_meta_boxes', [$this, 'custom_fields_demand_deliver']);
    }

    public function custom_fields_demand_deliver(mixed $meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => __('Obsah sekce - Co dalšího vaše weby tíží', 'uw-child-theme'),
            'post_types' => ['page' , 'post'],
            'include' => [
                'relation' => 'AND',
                'slug' => ['nase-sluzby'],
            ],
            'fields' => [
                [
                    'name'  => __('Nadpis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::DEMAND_DELIVER_FIELD_TITLE,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Popis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::DEMAND_DELIVER_FIELD_DESCRIPTION,
                    'type'    => 'textarea',
                ],
                [
                    'name'          => __( 'Kategorie a Text', 'uw-child-theme' ),
                    'id'            => self::PREFIX . self::DEMAND_DELIVER_FIELD_GROUP,
                    'type'          => 'group',
                    'clone'         => true,
                    'collapsible'   => true,
                    'sort_clone'    => true,
                    'default_state' => 'collapsed',
                    'group_title'   => 'Kategorie a Text {#}',
                    'fields'        => [
                        [
                            'name'  => __('Kategorie', 'uw-child-theme'),
                            'id'    => self::PREFIX . self::DEMAND_DELIVER_FIELD_GROUP_CATEGORY,
                            'type'  => 'text',
                        ],
                        [
                            'name'  => __('Levý Text - Nadpis kategorie', 'uw-child-theme'),
                            'id'    => self::PREFIX . self::DEMAND_DELIVER_FIELD_GROUP_LEFT_TEXT_TITLE,
                            'type'  => 'text',
                        ],
                        [
                            'name'  => __('Levý Text - Popis kategorie', 'uw-child-theme'),
                            'id'    => self::PREFIX . self::DEMAND_DELIVER_FIELD_GROUP_LEFT_TEXT_DESCRIPTION,
                            'type'    => 'wysiwyg',
                            'raw'     => true,
                            'options' => [
                                'textarea_rows'       => 2,
                                'media_buttons'       => false,
                            ],
                        ],
                        [
                            'name'  => __('Pravý Text - Nadpis kategorie', 'uw-child-theme'),
                            'id'    => self::PREFIX . self::DEMAND_DELIVER_FIELD_GROUP_RIGHT_TEXT_TITLE,
                            'type'  => 'text',
                        ],
                        [
                            'name'  => __('Pravý Text - Popis kategorie', 'uw-child-theme'),
                            'id'    => self::PREFIX . self::DEMAND_DELIVER_FIELD_GROUP_RIGHT_TEXT_DESCRIPTION,
                            'type'    => 'wysiwyg',
                            'raw'     => true,
                            'options' => [
                                'textarea_rows'       => 2,
                                'media_buttons'       => false,
                            ],
                        ],
                    ]
                ],
            ]
        ];
        return $meta_boxes;
    }

}