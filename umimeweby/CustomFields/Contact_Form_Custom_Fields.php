<?php

namespace Umimeweby\UWTheme\CustomFields;

class Contact_Form_Custom_Fields
{

    const PREFIX = 'contact_form_';

    const CONTACT_FORM_FIELD_TITLE = 'title';
    const CONTACT_FORM_FIELD_DESCRIPTION = 'description';
    const CONTACT_FORM_FIELD_ANOTHER_DESCRIPTION = 'another_description';

    public function __construct()
    {
        add_filter('rwmb_meta_boxes', [$this, 'custom_fields_contact_form']);
    }

    public function custom_fields_contact_form(mixed $meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => __('Obsah - Kontaktního formuláře', 'uw-child-theme'),
            'post_types' => ['page'],
            'include' => [
                'relation' => 'AND',
                'slug' => ['o-nas', 'sluzby', 'wordpress-sprava'],
            ],
            'fields' => [
                [
                    'name'  => __('Nadpis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::CONTACT_FORM_FIELD_TITLE,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Popis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::CONTACT_FORM_FIELD_DESCRIPTION,
                    'type'    => 'wysiwyg',
                    'raw'     => true,
                    'options' => [
                        'textarea_rows'       => 2,
                        'media_buttons'       => false,
                    ],
                ],
                [
                    'name'  => __('Další popis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::CONTACT_FORM_FIELD_ANOTHER_DESCRIPTION,
                    'type'    => 'wysiwyg',
                    'raw'     => true,
                    'options' => [
                        'textarea_rows'       => 2,
                        'media_buttons'       => false,
                    ],
                ],
            ]
        ];
        return $meta_boxes;
    }
}