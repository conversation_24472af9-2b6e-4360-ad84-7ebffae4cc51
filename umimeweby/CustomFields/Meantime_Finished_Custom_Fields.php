<?php

namespace Umimeweby\UWTheme\CustomFields;

use function Sodium\add;

class Meantime_Finished_Custom_Fields
{

    const PREFIX = 'meantime_finished_';

    const MEANTIME_FINISHED_CUSTOM_FIELDS_TITLE = 'title';
    const MEANTIME_FINISHED_CUSTOM_FIELDS_DESCRIPTION = 'description';
    const MEANTIME_FINISHED_CUSTOM_FIELDS_GROUP = 'group';
    const MEANTIME_FINISHED_CUSTOM_FIELDS_GROUP_IMAGE = 'group_image';
    const MEANTIME_FINISHED_CUSTOM_FIELDS_BTN_TEXT = 'btn_text';
    const MEANTIME_FINISHED_CUSTOM_FIELDS_BTN_LINK = 'btn_link';

    public function __construct()
    {
        add_filter('rwmb_meta_boxes', [$this, 'custom_fields_meantime_finished']);
    }

    public function custom_fields_meantime_finished(mixed $meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => __('Obsah sekce - Mezitím jsme dokončili', 'uw-child-theme'),
            'post_types' => ['page'],
            'include' => [
                'relation' => 'AND',
                'slug' => ['homepage'],
            ],
            'fields' => [
                [
                    'name'  => __('Nadpis sekce - Mezitím jsme dokončili', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::MEANTIME_FINISHED_CUSTOM_FIELDS_TITLE,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Popis sekce - Mezitím jsme dokončili', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::MEANTIME_FINISHED_CUSTOM_FIELDS_DESCRIPTION,
                    'type'  => 'textarea',
                ],
                [
                    'name'          => __( 'Obrázek', 'uw-child-theme' ),
                    'id'            => self::PREFIX . self::MEANTIME_FINISHED_CUSTOM_FIELDS_GROUP,
                    'type'          => 'group',
                    'clone'         => true,
                    'collapsible'   => true,
                    'sort_clone'    => true,
                    'default_state' => 'collapsed',
                    'group_title'   => 'Obrázek {#}',
                    'fields'        => [
                        [
                            'name'             => __( 'Obrázek', 'uw-child-theme' ),
                            'id'               => self::PREFIX . self::MEANTIME_FINISHED_CUSTOM_FIELDS_GROUP_IMAGE,
                            'type'             => 'image_advanced',
                            'force_delete'     => false,
                            'max_file_uploads' => 1,
                            'max_clone' => 1,
                            'max_status'       => false,
                        ],
                    ]
                ],
                [
                    'name'  => __('Text tlačítka', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::MEANTIME_FINISHED_CUSTOM_FIELDS_BTN_TEXT,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Odkaz v tlačítku', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::MEANTIME_FINISHED_CUSTOM_FIELDS_BTN_LINK,
                    'type'        => 'post',
                    'post_type' => 'page',
                    'field_type'  => 'select_advanced',
                    'placeholder' => 'Vyberte si stránku',
                    'query_args'  => [
                        'post_status'    => 'publish',
                        'posts_per_page' => - 1,
                    ],
                ],
            ]
        ];
        return $meta_boxes;
    }

}