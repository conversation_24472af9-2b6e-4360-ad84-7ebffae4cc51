<?php

namespace Umimeweby\UWTheme\CustomFields\AboutUs;

class Team_Custom_Fields
{

    const PREFIX = 'team_';

    const TEAM_FIELD_TITLE = 'title';
    const TEAM_FIELD_DESCRIPTION = 'description';
    const TEAM_FIELD_GROUP = 'group';
    const TEAM_FIELDS_GROUP_IMAGE = 'group_image';
    const TEAM_GROUP_NAME_AND_SURNAME = 'group_name';
    const TEAM_GROUP_POSITION = 'group_position';
    const TEAM_GROUP_DESCRIPTION = 'group_description';

    public function __construct()
    {
        add_filter('rwmb_meta_boxes', [$this, 'custom_fields_team']);
    }

    public function custom_fields_team(mixed $meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => __('Obsah sekce - Team', 'uw-child-theme'),
            'post_types' => ['page'],
            'include' => [
                'relation' => 'AND',
                'slug' => ['o-nas'],
            ],
            'fields' => [
                [
                    'name'  => __('Nadpis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::TEAM_FIELD_TITLE,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Popis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::TEAM_FIELD_DESCRIPTION,
                    'type'  => 'textarea',
                ],
                [
                    'name' => __('Team', 'uw-child-theme'),
                    'id' => self::PREFIX . self::TEAM_FIELD_GROUP,
                    'type' => 'group',
                    'clone' => true,
                    'collapsible' => true,
                    'sort_clone' => true,
                    'default_state' => 'collapsed',
                    'group_title' => 'Pracovník {#}',
                    'fields' => [
                        [
                            'name'             => __( 'Fotka', 'uw-child-theme' ),
                            'id'               => self::PREFIX . self::TEAM_FIELDS_GROUP_IMAGE,
                            'type'             => 'image_advanced',
                            'force_delete'     => false,
                            'max_file_uploads' => 1,
                            'max_status'       => false,
                        ],
                        [
                            'name'  => __('Jméno a příjmení', 'uw-child-theme'),
                            'id'    => self::PREFIX . self::TEAM_GROUP_NAME_AND_SURNAME,
                            'type'  => 'text',
                        ],
                        [
                            'name'  => __('Pozice', 'uw-child-theme'),
                            'id'    => self::PREFIX . self::TEAM_GROUP_POSITION,
                            'type'  => 'text',
                        ],
                        [
                            'name'  => __('Popis', 'uw-child-theme'),
                            'id'    => self::PREFIX . self::TEAM_GROUP_DESCRIPTION,
                            'type'  => 'textarea',
                        ],
                    ]
                ],
            ]
        ];
        return $meta_boxes;
    }

}