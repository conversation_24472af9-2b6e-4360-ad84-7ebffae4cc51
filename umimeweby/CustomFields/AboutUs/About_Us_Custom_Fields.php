<?php

namespace Umimeweby\UWTheme\CustomFields\AboutUs;

class About_Us_Custom_Fields
{

    const PREFIX = 'about_us';

    const ABOUT_US_FIELD_TITLE = 'title';
    const ABOUT_US_FIELD_DESCRIPTION = 'description';

    public function __construct()
    {
        add_filter('rwmb_meta_boxes', [$this, 'custom_fields_about_us']);
    }

    public function custom_fields_about_us(mixed $meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => __('Obsah sekce - O nás', 'uw-child-theme'),
            'post_types' => ['page'],
            'include' => [
                'relation' => 'AND',
                'slug' => ['o-nas'],
            ],
            'fields' => [
                [
                    'name'  => __('Nadpis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::ABOUT_US_FIELD_TITLE,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Popis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::ABOUT_US_FIELD_DESCRIPTION,
                    'type'    => 'wysiwyg',
                    'raw'     => false,
                    'options' => [
                        'textarea_rows'       => 10,
                        'media_buttons'       => false,
                        'teeny'         => true,
                    ],
                ],
            ]
        ];
        return $meta_boxes;
    }
}