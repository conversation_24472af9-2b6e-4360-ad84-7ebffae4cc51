<?php

namespace Umimeweby\UWTheme\CustomFields\PageWithHeroTemplate;

class PageWithHero_Custom_Fields
{

    const PREFIX = 'pagewithhero_';

    const HEADER1 = 'header1';
    const HEADER2 = 'header2';
    const SUBHEADER_AREA = 'subheader_area';

    public function __construct()
    {
        add_filter('rwmb_meta_boxes', [$this, 'custom_fields']);
    }

    public function custom_fields(mixed $meta_boxes): mixed
    {
        $meta_boxes[] = [
            'title' => __('Obsah do <PERSON> na stránce', 'uw-child-theme'),
            'post_types' => ['page'],
            'include' => [
                'relation' => 'AND',
                'template' => ['page-templates/page-with-hero.php'],
            ],
            'fields'     => [
                [
                    'name' => __('Header 1', 'uw-child-theme'),
                    'id'   => self::PREFIX . self::HEADER1,
                    'type' => 'textarea',
                ],
                [
                    'name' => __('Header 2', 'uw-child-theme'),
                    'id'   => self::PREFIX . self::HEADER2,
                    'type' => 'text',
                ],
                [
                    'name' => __('Subheader area', 'uw-child-theme'),
                    'id'   => self::PREFIX . self::SUBHEADER_AREA,
                    'type' => 'textarea',
                    'rows'        => 5,
                ],

            ],
        ];

        return $meta_boxes;
    }
}
