<?php

namespace Umimeweby\UWTheme\CustomFields\Reference\Single;

class Single_Case_Study_Custom_Fields
{

    const CPT_KEY = 'nase-reference';

    const PREFIX = 'single_case_study_';

    const SINGLE_CASE_STUDY_FIELD_TITLE = 'title';
    const SINGLE_CASE_STUDY_FIELD_DESCRIPTION = 'description';

    public function __construct()
    {
        add_filter('rwmb_meta_boxes', [$this, 'custom_single_case_study_fields']);
    }

    public function custom_single_case_study_fields(mixed $meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => __('O<PERSON><PERSON> sekce - <PERSON><PERSON><PERSON><PERSON> studie', 'uw-child-theme'),
            'post_types' => self::CPT_KEY,
            'fields' => [
                [
                    'name'  => __('Nadpis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::SINGLE_CASE_STUDY_FIELD_TITLE,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Popis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::SINGLE_CASE_STUDY_FIELD_DESCRIPTION,
                    'type'  => 'textarea',
                ],
            ]
        ];
        return $meta_boxes;
    }

}