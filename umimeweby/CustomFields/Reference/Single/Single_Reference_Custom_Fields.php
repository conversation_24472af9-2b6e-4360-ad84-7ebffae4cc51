<?php

namespace Umimeweby\UWTheme\CustomFields\Reference\Single;

class Single_Reference_Custom_Fields
{
    const CPT_KEY = 'nase-reference';

    const PREFIX = 'single_reference_';

    const SINGLE_REFERENCE_FIELD_CLIENT = 'client';

    const SINGLE_REFERENCE_FIELD_SUBTITLE = 'subtitle';
    const SINGLE_REFERENCE_FIELD_MONTH_YEAR = 'month_year';
    const SINGLE_REFERENCE_FIELD_DESCRIPTION = 'description';
    const SINGLE_REFERENCE_FIELD_DESCRIPTION_WHAT_CREATE = 'description_what_create';
    const SINGLE_REFERENCE_FIELD_CATEGORY = 'category';
    const SINGLE_REFERENCE_FIELD_DESCRIPTION_FOR_CATEGORY = 'description_for_category';

    public function __construct()
    {
        add_filter('rwmb_meta_boxes', [$this, 'custom_single_references_fields']);
    }

    public function custom_single_references_fields(mixed $meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => __('Obsah Detail Reference', 'uw-child-theme'),
            'post_types' => self::CPT_KEY,
            'fields' => [
                [
                    'name'  => __('Klient - jméno firmy/zadavatele', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::SINGLE_REFERENCE_FIELD_CLIENT,
                    'type'  => 'text',
                ],                
                [
                    'name'  => __('Podnadpis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::SINGLE_REFERENCE_FIELD_SUBTITLE,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Měsíc a rok', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::SINGLE_REFERENCE_FIELD_MONTH_YEAR,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Popis', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::SINGLE_REFERENCE_FIELD_DESCRIPTION,
                    'type'    => 'wysiwyg',
                    'raw'     => true,
                    'options' => [
                        'textarea_rows'       => 2,
                        'media_buttons'       => false,
                    ],
                ],
                [
                    'name'  => __('Nadpis - Co jsme vytvořili', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::SINGLE_REFERENCE_FIELD_DESCRIPTION_WHAT_CREATE,
                    'type'  => 'textarea',
                ],
                [
                    'name'  => __('Kategorie', 'uw-child-theme'),
                    'desc' => 'Případová studie nebo Reference',
                    'id'    => self::PREFIX . self::SINGLE_REFERENCE_FIELD_CATEGORY,
                    'type'  => 'text',
                ],
                [
                    'name'  => __('Popis pro danou kategorii', 'uw-child-theme'),
                    'id'    => self::PREFIX . self::SINGLE_REFERENCE_FIELD_DESCRIPTION_FOR_CATEGORY,
                    'type'  => 'textarea',
                ],
            ]
        ];
        return $meta_boxes;
    }

}