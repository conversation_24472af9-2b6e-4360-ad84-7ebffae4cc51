<?php

namespace Umimeweby\UWTheme\CustomFields;

class Our_Blog_Custom_Fields
{

    const PREFIX = 'our_blog_';

    const OUR_BLOG_FIELD_TITLE = 'title';
    const OUR_BLOG_FIELD_DESCRIPTION = 'description';


    public function __construct()
    {
        add_filter('rwmb_meta_boxes', [$this, 'custom_fields_our_blogs']);
    }

    public function custom_fields_our_blogs(mixed $meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => __('Obsah sekce - Z našeho blogu', 'uw-child-theme'),
            'post_types' => ['page'],
            'include' => [
                'relation' => 'AND',
                'slug' => ['homepage'],
            ],
            'fields' => [
                [
                    'name' => __('Nadpis sekce - Z našeho blogu', 'uw-child-theme'),
                    'id' => self::PREFIX . self::OUR_BLOG_FIELD_TITLE,
                    'type' => 'text',
                ],
                [
                    'name' => __('Popis sekce - Z našeho blogu', 'uw-child-theme'),
                    'id' => self::PREFIX . self::OUR_BLOG_FIELD_DESCRIPTION,
                    'type' => 'textarea',
                ],
            ]
        ];
        return $meta_boxes;
    }

}