<?php

namespace Umimeweby\UWTheme\Form;

class Form_Provider
{
    /**
     * 
     * @var array<mixed>
     */
    private array $uw_forms = [];

    public function __construct() {

        $this->uw_forms = [
            new Newsletter_Form(),
        ];
        ;
    }


    /**
     * Get all registered Forms
     * 
     * @return array<mixed>
     */
    public function get_forms():array
    {
        return $this->uw_forms;
    }

}
