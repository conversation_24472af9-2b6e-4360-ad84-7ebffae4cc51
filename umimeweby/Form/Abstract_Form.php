<?php

namespace Umimeweby\UWTheme\Form;

abstract class Abstract_Form
{

     abstract protected function get_form_name();
     abstract protected function handle_form_submission();
     abstract protected function get_form_html_code();
     abstract protected function is_form_valid();

     /**
      * defines default form action URL to send POST data
      *
      * @return string
      */
     public function get_form_action() : string
     {
        return esc_url(admin_url('admin-post.php'));
     }

    /**
     * Loads from data from POST to array
     */
    protected function load_data_from_post(): array
    {
        $data = [];
        $post_data =  $_POST;
        foreach ($post_data as $key => $value)
        {
            $data[$key] = htmlspecialchars($value ?? '');
        }
        return $data;
    }

}