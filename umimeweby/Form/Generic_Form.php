<?php

namespace Umimeweby\UWTheme\Form;

use Umimeweby\UWTheme\Service\EmailSender\Generic_Form_Email_Sender;

class Generic_Form extends Abstract_Form
{

    public const SHORTCODE = 'uw-generic-form';

    public const SPAM_CONTROL_STRING = 'Zd<PERSON> se, že uživatel není robot.';
    public const SPAM_CONTROL_FIELD = 'dulezita-kontrola';

    public const THANKYOU_PAGE = 'dekujeme-za-zpravu';

    public string $nonce_field_name = 'contact-form-generic-nonce';
    public string $form_id = 'uw-generic-form-contact';

    private Generic_Form_Email_Sender $email_sender;

    /**
     *  
     * @var array <mixed>
     */
    public array $formdata;

    public function __construct()
    {
        add_action('admin_post_' . $this->get_form_name(), [$this, 'handle_form_submission']);
        add_action('admin_post_nopriv_' . $this->get_form_name(), [$this, 'handle_form_submission']);
        add_shortcode(self::SHORTCODE, [$this, 'get_shortcode_content']);

        $this->email_sender = new Generic_Form_Email_Sender();
    }

    /**
     * provides form HTML in shortcode output
     *
     * @param  mixed $atts
     * @return string
     */
    public function get_shortcode_content($atts)
    {
        return $this->get_form_html_code();
    }

    /**
     * facade to handling form methods
     *
     * @return void
     */
    public function public_form_handling(): void
    {
        $this->handle_form_submission();
    }



    public function get_form_name(): string
    {
        return 'uw_wordpress_generic_form';
    }

    public function get_form_html_code(): string
    {
        ob_start();
        // list of variables passed to form HTML generator
        $form_id = $this->form_id;
        $form_action = $this->get_form_action();
        $wp_action = $this->get_form_name();
        $nonce_name = $this->nonce_field_name;
        $spam_control_string = self::SPAM_CONTROL_STRING;
        $spam_control_field = self::SPAM_CONTROL_FIELD;

        include(get_stylesheet_directory() . '/template-parts/form/_generic-form.php');
        $result = ob_get_clean();
        return $result;
    }

    public function handle_form_submission(): void
    {

        $this->formdata = $this->load_data_from_post();

        if (!$this->is_form_valid()) {

            $redirect = (home_url(wp_get_referer()));
            exit(wp_redirect($redirect));
        }

        // send email
        $this->email_sender->send($this->formdata);

        $redirect = (home_url(self::THANKYOU_PAGE));
        exit(wp_redirect($redirect));

    }

    protected function is_form_valid(): bool
    {

        $result = true;

        // nonce check
        if (!isset($this->formdata['_wpnonce']) || !wp_verify_nonce($this->formdata['_wpnonce'], $this->nonce_field_name)) {
            $result = false;
        }

        // custom antispam check
        if ($this->formdata[self::SPAM_CONTROL_FIELD] !==  self::SPAM_CONTROL_STRING) {
            $result =  false;
        }

        return $result;
    }
}
