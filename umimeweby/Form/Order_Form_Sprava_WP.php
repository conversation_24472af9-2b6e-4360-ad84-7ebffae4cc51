<?php

namespace Umimeweby\UWTheme\Form;

use Umimeweby\UWTheme\Service\EmailSender\Order_Form_Sprava_WP_Email_Sender;


class Order_Form_Sprava_WP extends Abstract_Form
{
    private $email_sender;

    public function __construct()
    {
        $this->email_sender = new Order_Form_Sprava_WP_Email_Sender();
    }

    public function get_form_name()
    {
        return 'uw_wordpress_sprava_order_form';
    }

    protected function is_form_valid()
    {
        return true;
    }


    public function get_form_html_code(): string
    {
        ob_start();
        include(get_stylesheet_directory() . '/template-parts/form/_order-form-sprava-wp.php');
        $result = ob_get_clean();
        return $result;
    }

    public function handle_form_submission()
    {

        if (!$this->is_form_valid()) {
            return;
        }

        if (!isset($_POST['_wpnonce']) || !wp_verify_nonce($_POST['_wpnonce'], 'order-form-sprava-wp-nonce')) {
            return;
        }

        $data = $this->load_data_from_post();

        // send emails only if not robot
        if ($data['dulezita_kontrola_objednavky'] === 'Zdá se, že uživatel není robot.') {
            $this->email_sender->send($data);

            $redirect_to_path = 'dekujeme-za-zpravu';
            $redirect = home_url(htmlspecialchars($redirect_to_path ?? ''));
            exit(wp_redirect($redirect));
        }

        // If it is a robot, the form is redirected but the email is not sent
        $redirect_to_path = 'dekujeme-za-zpravu';
        $redirect = home_url(htmlspecialchars($redirect_to_path ?? ''));
        exit(wp_redirect($redirect));
    }
}
