<?php

namespace Umimeweby\UWTheme\Form;

use Umimeweby\UWTheme\Service\GetResponse\GetResponse_Manager;
use Umimeweby\UWTheme\Settings\UW_GetReponse_Settings;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;

class Newsletter_Form extends Abstract_Form
{

    public const SHORTCODE = 'uw-nl-01-form';
    public const FORM_NAME = 'uw_nl_01_form';

    public const SPAM_CONTROL_STRING = 'Zdá se, že uživatel není robot.';
    public const SPAM_CONTROL_FIELD = 'dulezita-kontrola';

    public const THANKYOU_PAGE = 'podekovani';

    public string $form_id = 'uw-nl-1-form';

    public string $nonce_field_name = 'uwnl1-form-nonce';

    private GetResponse_Manager $getreponse_manager;

    private string $thankyou_page;



    /**
     *  
     * @var array <mixed>
     */
    public array $formdata;


    public function __construct()
    {
        add_action('admin_post_' . $this->get_form_name(), [$this, 'public_form_handling']);
        add_action('admin_post_nopriv_' . $this->get_form_name(), [$this, 'public_form_handling']);
        add_shortcode(self::SHORTCODE, [$this, 'get_shortcode_content']);
        $this->getreponse_manager = new GetResponse_Manager();

    }


    /**
     * provides form HTML in shortcode output
     *
     * @param  mixed $atts
     * @return string
     */
    public function get_shortcode_content($atts)
    {
        return $this->get_form_html_code();
    }

    /**
     * facade to handling form methods
     *
     * @return void
     */
    public function public_form_handling(): void
    {
        $this->handle_form_submission();
    }



    /**
     * 
     * @return 'uw_nl_01_form' 
     */
    protected function get_form_name()
    {
        return  self::FORM_NAME;
    }

    /**
     * 
     * @return never 
     */
    protected function handle_form_submission()
    {
        $this->formdata = $this->load_data_from_post();

        if (!$this->is_form_valid()) {
            $redirect = (home_url(wp_get_referer()));
            exit(wp_redirect($redirect));
        }        

        $result = $this->getreponse_manager->add_email_to_newsletter_list($this->formdata['email']);
        if (!$result)
        {
            exit(wp_redirect(home_url()));
        }

        $this->thankyou_page = rwmb_meta(
            UW_Settings_Page::PREFIX . UW_GetReponse_Settings::FIELD_GETRESPONSE_THANKYOUPAGE,
            ['object_type' => 'setting'],
            UW_GetReponse_Settings::OPTION_GETRESPONSE_SETTING
        );

        if (get_post($this->thankyou_page))
        {
            exit(wp_redirect(get_permalink($this->thankyou_page)));
        }

        wp_redirect(home_url());
        exit;
    }

    /**
     * 
     * @return string|false 
     */
    protected function get_form_html_code()
    {
        ob_start();

        // list of variables passed to form HTML generator
        $form_id = $this->form_id;
        $form_action = $this->get_form_action();
        $wp_action = $this->get_form_name();
        $nonce_name = $this->nonce_field_name;
        $spam_control_string = self::SPAM_CONTROL_STRING;
        $spam_control_field = self::SPAM_CONTROL_FIELD;

        include(get_stylesheet_directory() . '/template-parts/form/_newsletter-1-form.php');
        $result = ob_get_clean();
        return $result;
    }

    /**
     * 
     * @return bool 
     */
    protected function is_form_valid()
    {
        $result = true;

        // nonce check
        if (!isset($this->formdata['_wpnonce']) || !wp_verify_nonce($this->formdata['_wpnonce'], $this->nonce_field_name)) {
            $result = false;
        }

        // custom antispam check
        if ($this->formdata[self::SPAM_CONTROL_FIELD] !==  self::SPAM_CONTROL_STRING) {
            $result =  false;
        }

        // check if email is valid
        if (!is_email($this->formdata['email'])) {
            $result =  false;
        }        

        return $result;
    }
}
