<?php

namespace Umimeweby\UWTheme\Taxonomies;

class Reference_Custom_Taxonomy
{
    const PREFIX = 'nase-reference_';

    const TAXONOMY_CATEGORY = 'kategorie';
    const TAXONONY_FIELD_PORADI = 'taxonomy_type_reference_order';

    public function __construct()
    {
        add_action('init', [$this, 'custom_taxonomy_reference']);
        add_filter('rwmb_meta_boxes', [$this,'define_taxonomy_fields']);
    }

    function custom_taxonomy_reference():void {
        $labels = [
            'name'                       => esc_html__( 'Kategorie', 'uw-child-theme' ),
            'singular_name'              => esc_html__( 'Kategorie', 'uw-child-theme' ),
            'menu_name'                  => esc_html__( 'Kategorie', 'uw-child-theme' ),
            'search_items'               => esc_html__( 'Search Kategorie', 'uw-child-theme' ),
            'popular_items'              => esc_html__( 'Popular Kategorie', 'uw-child-theme' ),
            'all_items'                  => esc_html__( 'All Kategorie', 'uw-child-theme' ),
            'parent_item'                => esc_html__( 'Parent Kategorie', 'uw-child-theme' ),
            'parent_item_colon'          => esc_html__( 'Parent Kategorie:', 'uw-child-theme' ),
            'edit_item'                  => esc_html__( 'Edit Kategorie', 'uw-child-theme' ),
            'view_item'                  => esc_html__( 'View Kategorie', 'uw-child-theme' ),
            'update_item'                => esc_html__( 'Update Kategorie', 'uw-child-theme' ),
            'add_new_item'               => esc_html__( 'Add New Kategorie', 'uw-child-theme' ),
            'new_item_name'              => esc_html__( 'New Kategorie Name', 'uw-child-theme' ),
            'separate_items_with_commas' => esc_html__( 'Separate kategorie with commas', 'uw-child-theme' ),
            'add_or_remove_items'        => esc_html__( 'Add or remove kategorie', 'uw-child-theme' ),
            'choose_from_most_used'      => esc_html__( 'Choose most used kategorie', 'uw-child-theme' ),
            'not_found'                  => esc_html__( 'No kategorie found.', 'uw-child-theme' ),
            'no_terms'                   => esc_html__( 'No kategorie', 'uw-child-theme' ),
            'filter_by_item'             => esc_html__( 'Filter by kategorie', 'uw-child-theme' ),
            'items_list_navigation'      => esc_html__( 'Kategorie list pagination', 'uw-child-theme' ),
            'items_list'                 => esc_html__( 'Kategorie list', 'uw-child-theme' ),
            'most_used'                  => esc_html__( 'Most Used', 'uw-child-theme' ),
            'back_to_items'              => esc_html__( '&larr; Go to Kategorie', 'uw-child-theme' ),
            'text_domain'                => esc_html__( 'uw-child-theme', 'uw-child-theme' ),
        ];
        $args = [
            'label'              => esc_html__( 'Kategorie', 'uw-child-theme' ),
            'labels'             => $labels,
            'description'        => '',
            'public'             => true,
            'publicly_queryable' => true,
            'hierarchical'       => false,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'show_in_nav_menus'  => true,
            'show_in_rest'       => true,
            'show_tagcloud'      => true,
            'show_in_quick_edit' => true,
            'show_admin_column'  => false,
            'query_var'          => true,
            'sort'               => false,
            'meta_box_cb'        => 'post_tags_meta_box',
            'rest_base'          => '',
            'rewrite'            => [
                'with_front'   => false,
                'hierarchical' => false,
            ],
        ];
        register_taxonomy( self::TAXONOMY_CATEGORY, ['nase-reference'], $args );
    }

    /**
     * @param array<mixed> $meta_boxes
     */
    public function define_taxonomy_fields($meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' => '',
            'taxonomies' => [self::TAXONOMY_CATEGORY],
            'fields'     => [
                [
                    'name'              => __('Pořadí', 'uolweb'),
                    'id'                => self::PREFIX . self::TAXONONY_FIELD_PORADI,
                    'type'              => 'number',
                    'label_description' => __('Pořadí tlačítka reference pro filtr na stránce Reference', 'uolweb'),
                    'min'               => 0,
                ],
            ],
        ];

        return $meta_boxes;
    }

}