<?php

namespace Umimeweby\UWTheme\Taxonomies;

class Custom_Taxonomy_Provider_Service
{


  /**
   * 
   * @var array<mixed>
   */
  private array $mb_custom_taxonomies = [];
  /**
   * 
   * @var array<mixed>
   */
  private array $wp_custom_taxonomies = [];

  public function register_metabox_related_taxonomies(): void
  {
    $this->mb_custom_taxonomies = [
      new UW_Service_Custom_Taxonomy(),
      new Reference_Custom_Taxonomy()
    ];
  }

  public function register_wordpress_based_taxonomies(): void
  {
    $this->wp_custom_taxonomies[] = [];
  }

  /**
   * Get array of all registered custom taxonomies that rely on metabox plugin
   * @return array<mixed>
   */
  public function get_mb_custom_taxonomies(): array
  {
    return $this->mb_custom_taxonomies;
  }

  /**
   * Get array of all registered custom custom taxonomies that rely on plain wordpress
   * @return array<mixed>
   */
  public function get_wp_custom_taxonomies(): array
  {
    return $this->wp_custom_taxonomies;
  }
}
