<?php

namespace Umimeweby\UWTheme\Taxonomies;

use Umimeweby\UWTheme\CustomPostType\UW_Service_Custom_Post_Type;

class UW_Service_Custom_Taxonomy
{

    const TAXONOMY_KEY = 'uw-service-category';
    
    const TAXONOMY_PREFIX = 'uw-service-category_';

    const TAXONOMY_FIELD_ORDER = 'uwsc-order';

    public function __construct()
    {
        add_action('init', [$this, 'uw_service_category_register_taxonomy']);
        add_filter('rwmb_meta_boxes', [$this,'define_taxonomy_fields']);

    }

    public function uw_service_category_register_taxonomy():void
    {
        $labels = [
            'name'                       => esc_html__('Kategorie UW Služeb', 'uw-child-theme'),
            'singular_name'              => esc_html__('Kategorie UW Služeb', 'uw-child-theme'),
            'menu_name'                  => esc_html__('Kategorie UW Služeb', 'uw-child-theme'),
            'search_items'               => esc_html__('Search Kategorie UW Služeb', 'uw-child-theme'),
            'popular_items'              => esc_html__('Popular Kategorie UW Služeb', 'uw-child-theme'),
            'all_items'                  => esc_html__('All Kategorie UW Služeb', 'uw-child-theme'),
            'parent_item'                => esc_html__('Parent Kategorie UW Služeb', 'uw-child-theme'),
            'parent_item_colon'          => esc_html__('Parent Kategorie UW Služeb:', 'uw-child-theme'),
            'edit_item'                  => esc_html__('Edit Kategorie UW Služeb', 'uw-child-theme'),
            'view_item'                  => esc_html__('View Kategorie UW Služeb', 'uw-child-theme'),
            'update_item'                => esc_html__('Update Kategorie UW Služeb', 'uw-child-theme'),
            'add_new_item'               => esc_html__('Add New Kategorie UW Služeb', 'uw-child-theme'),
            'new_item_name'              => esc_html__('New Kategorie UW Služeb Name', 'uw-child-theme'),
            'separate_items_with_commas' => esc_html__('Separate kategorie uw služeb with commas', 'uw-child-theme'),
            'add_or_remove_items'        => esc_html__('Add or remove kategorie uw služeb', 'uw-child-theme'),
            'choose_from_most_used'      => esc_html__('Choose most used kategorie uw služeb', 'uw-child-theme'),
            'not_found'                  => esc_html__('No kategorie uw služeb found.', 'uw-child-theme'),
            'no_terms'                   => esc_html__('No kategorie uw služeb', 'uw-child-theme'),
            'filter_by_item'             => esc_html__('Filter by kategorie uw služeb', 'uw-child-theme'),
            'items_list_navigation'      => esc_html__('Kategorie UW Služeb list pagination', 'uw-child-theme'),
            'items_list'                 => esc_html__('Kategorie UW Služeb list', 'uw-child-theme'),
            'most_used'                  => esc_html__('Most Used', 'uw-child-theme'),
            'back_to_items'              => esc_html__('&larr; Go to Kategorie UW Služeb', 'uw-child-theme'),
            'text_domain'                => esc_html__('uw-child-theme', 'uw-child-theme'),
        ];
        $args = [
            'label'              => esc_html__('Kategorie UW Služeb', 'uw-child-theme'),
            'labels'             => $labels,
            'description'        => 'Definice základních kategorií pro Custom Post UW Služby (UW services)',
            'public'             => true,
            'publicly_queryable' => true,
            'hierarchical'       => false,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'show_in_nav_menus'  => false,
            'show_in_rest'       => true,
            'show_tagcloud'      => true,
            'show_in_quick_edit' => true,
            'show_admin_column'  => true,
            'query_var'          => true,
            'sort'               => false,
            '_slug_changed'      => true,
            'meta_box_cb'        => false,
            'rest_base'          => '',
            'rewrite'            => [
                'slug'         => 'sluzby',
                'with_front'   => false,
                'hierarchical' => false,
            ],
        ];
        register_taxonomy(self::TAXONOMY_KEY, [UW_Service_Custom_Post_Type::CPT_KEY], $args);
    }


        /**
     * @param array<mixed> $meta_boxes
     */
    public function define_taxonomy_fields($meta_boxes): mixed
    {

        $meta_boxes[] = [
            'title' =>  __('Doplňující údaje', 'uw-child-theme'),
            'taxonomies' => [self::TAXONOMY_KEY],
            'fields'     => [
                [
                    'name'              => __('Pořadí ve výpisu', 'uw-child-theme'),
                    'id'                => self::TAXONOMY_PREFIX . self::TAXONOMY_FIELD_ORDER,
                    'type'              => 'number',
                    'label_description' => __('Pořadí boxu kategorie při zobrazení', 'uw-child-theme'),
                    'min'               => 0,
                ],
            ],
        ];

        return $meta_boxes;
    }
    

}
