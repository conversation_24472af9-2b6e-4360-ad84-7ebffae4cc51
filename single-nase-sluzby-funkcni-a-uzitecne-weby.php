<?php

use Um<PERSON>weby\UWTheme\CustomPostType\Our_Services_Custom_Post_Type;
use Umimeweby\UWTheme\Service\Calculator\Year_Calculator;

get_header();

$subtitle = rwmb_get_value(Our_Services_Custom_Post_Type::PREFIX . Our_Services_Custom_Post_Type::OUR_SERVICES_CUSTOM_FIELDS_SUBTITLE);

$title = rwmb_get_value(Our_Services_Custom_Post_Type::PREFIX . Our_Services_Custom_Post_Type::OUR_SERVICES_CUSTOM_FIELDS_TITLE);
$description = rwmb_get_value(Our_Services_Custom_Post_Type::PREFIX . Our_Services_Custom_Post_Type::OUR_SERVICES_CUSTOM_FIELDS_DESCRIPTION);

$calculator = new Year_Calculator();
$years =  $calculator->get_years_since_start();

global $post;
?>
<main class="w-full pt-4 md:mb-0 md:pt-0">
    <div class="px-4 md:px-5">
        <?php get_template_part('template-parts/breadcums') ?>
        <section class="relative z-50 my-14 mx-auto w-full max-w-[450px] md:max-w-[1170px] md:mb-[66px]">
            <div class="mb-4 mt-4 flex flex-col items-center gap-2.5 sm:flex-row sm:gap-8">
                <div class="z-10">
                    <h2 class="text-lg font-bold leading-normal text-center text-theme-purple sm:text-left sm:text-3xl">Proč webové aplikace od nás?</h2>
                </div>
            </div>
            <div class="grid w-full grid-cols-1 gap-6 mt-10 md:grid-cols-3 lg:gap-12">
                <div class="w-full">
                    <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/sprava/word-ball-1.svg" alt="ball" class="flex justify-center object-contain w-auto h-auto mx-auto md:mx-0 md:block">
                    <h2 class="mb-5 text-base font-bold leading-6 text-theme-purple sm:my-5 sm:text-xl sm:leading-[30px]"><?php echo ($years); ?> světelných let</h2>
                    <p class="text-sm italic font-normal leading-5 text-light-purple">Během kterých jsme obletěli nespočet galaxií, nalodili spoustu klientů a nasbírali zkušenosti z oblasti tvorby webů, programování, vývoje aplikací i spolupráce s různými firmami.</p>
                </div>
                <div class="w-full">
                    <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/sprava/word-ball-2.svg" alt="ball" class="flex justify-center object-contain w-auto h-auto mx-auto md:mx-0 md:block">
                    <h2 class="mb-5 text-base font-bold leading-6 text-theme-purple sm:my-5 sm:text-xl sm:leading-[30px]">1 zkušená posádka</h2>
                    <p class="text-sm italic font-normal leading-5 text-light-purple">Zajistíme cokoliv od konstrukce, designu, návrhu, přes vývoj a testování až po vypuštění rakety do Vesmíru. Máme skvělou zastupitelnost, proto za kormidlem vždy někdo stojí.</p>
                </div>
                <div class="w-full">
                    <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/sprava/word-ball-3.svg" alt="ball" class="flex justify-center object-contain w-auto h-auto mx-auto md:mx-0 md:block">
                    <h2 class="mb-5 text-base font-bold leading-6 text-theme-purple sm:my-5 sm:text-xl sm:leading-[30px]">47+ odstartovaných raket</h2>
                    <p class="text-sm italic font-normal leading-5 text-light-purple">O některé se stále staráme, jiné si letí po vlastní dráze. Vytvořili, upravili a zmodernizovali jsme mnoho e-shopů a webů a naprogramovali jsme řadu aplikací na míru.</p>
                </div>
            </div>
        </section>
        <section class="mx-auto my-14 w-full sm:my-24 sm:max-w-[70vw] md:max-w-[1170px]">
            <div class="mb-4 mt-4 flex flex-col items-center gap-2.5 sm:flex-row sm:gap-8">
                <div class="z-10">
                    <h2 class="text-lg font-bold leading-normal text-center text-theme-purple sm:text-left sm:text-3xl">Proč odstartovat vývoj aplikací?</h2>
                </div>
            </div>
            <div class="relative z-30 mx-auto mb-12 flex w-full max-w-[1050px] flex-col items-center justify-between gap-10 bg-white lg:mb-0 lg:flex-row xl:max-w-[1172px]">
                <div class="grid w-full grid-cols-1 gap-2 border-b border-[#d9d9d9] pb-14 sm:pb-24 md:grid-cols-1 lg:gap-6 xl:gap-12">
                    <div class="flex items-center gap-1">
                        <h1 class="relative inline-block text-[100px] font-extrabold leading-[150px] text-[#d9d9d9]">
                            1
                            <span class="pointer-events-none absolute top-1/2 -right-7 block h-[calc(100%-40px)] w-12 -translate-y-1/2 border-l border-[#d9d9d9] bg-white"></span>
                        </h1>
                        <div class="relative z-20">
                            <h5 class="text-xl font-bold leading-normal text-theme-purple">Zkušená posádka</h5>
                            <p class="text-sm font-normal leading-5 text-light-purple">O vývoj software se stará zkušená posádka, která rozumí programování, jiným galaxiím i hyperkosmickým technologiím. Ví, které tlačítko je třeba, a dolije vašemu byznysu to správné palivo. Zkonstruujeme webovou raketu, navrhneme design, vyvineme nové aplikace, zajistíme přímý let i strategickou analýzu. Marketing, SEO nebo třeba e-mailing zvládneme levou zadní.</p>
                        </div>
                    </div>
                    <div class="flex items-center gap-1">
                        <h1 class="relative inline-block text-[100px] font-extrabold leading-[150px] text-[#d9d9d9]">
                            2
                            <span class="pointer-events-none absolute top-1/2 -right-7 block h-[calc(100%-40px)] w-12 -translate-y-1/2 border-l border-[#d9d9d9] bg-white"></span>
                        </h1>
                        <div class="relative z-20">
                            <h5 class="text-xl font-bold leading-normal text-theme-purple">Hyperkosmické technologie</h5>
                            <p class="text-sm font-normal leading-5 text-light-purple">Vyznáme se v technologiích a vždy používáme takové, které znají ve všech galaxiích, drží krok s dobou a neustále se vyvíjejí. Díky tomu poletíte jako blesk a získáte kvalitní kód, který můžete kdykoli změnit, rozvinout nebo uzpůsobit svým potřebám. Poradíme si také s napojením na externí systémy, ať už jde o analytiku, e-mail marketing, CRM nebo cokoliv dalšího.</p>
                        </div>
                    </div>
                    <div class="flex items-center gap-1">
                        <h1 class="relative inline-block text-[100px] font-extrabold leading-[150px] text-[#d9d9d9]">
                            3
                            <span class="pointer-events-none absolute top-1/2 -right-7 block h-[calc(100%-40px)] w-12 -translate-y-1/2 border-l border-[#d9d9d9] bg-white"></span>
                        </h1>
                        <div class="relative z-20">
                            <h5 class="text-xl font-bold leading-normal text-theme-purple">Dlouhodobý let</h5>
                            <p class="text-sm font-normal leading-5 text-light-purple">Úspěšným startem naše práce nekončí. I nadále testujeme všechny funkcionality a děláme všechno pro to, abyste neskončili v černé díře. Podporujeme váš projekt a rozvíjíme nové funkcionality. Doléváme nejnovější palivo, modernizujeme technologie a navrhujeme vylepšení, se kterými předletíte konkurenci.</p>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:max-w-[560px]">
                    <div class="order-first h-max w-full rounded-[100px] bg-number px-16 lg:order-none">
                        <?php get_template_part('template-parts/components/_firm_numbers') ?>
                    </div>
                </div>
            </div>
        </section>
        <section class="relative z-50 my-14 mx-auto w-full max-w-[450px] md:max-w-[1170px] md:mb-[66px]">
            <div class="mb-4 mt-4 flex flex-col items-center gap-2.5 sm:flex-row sm:gap-8">
                <div class="z-10">
                    <h2 class="text-lg font-bold leading-normal text-center text-theme-purple sm:text-left sm:text-3xl">Co vás během letu čeká</h2>
                </div>
            </div>
            <div class="grid w-full grid-cols-1 gap-6 mt-10 md:grid-cols-3 lg:gap-12">
                <div class="w-full">
                    <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/ball/ball-1.png" height="100px" width="100px" alt="ball" class="flex justify-center object-contain mx-auto md:mx-0 md:block">
                    <h2 class="mb-5 text-base font-bold leading-6 text-theme-purple sm:my-5 sm:text-xl sm:leading-[30px]">Analýza</h2>
                    <p class="text-sm italic font-normal leading-5 text-light-purple">S námi v černé díře neskončíte! Než se začneme vrtat pod kapotou vaší rakety, pečlivě prozkoumáme stávající stav. Vyzjistíme všechny vaše požadavky a prozkoumáme možná řešení i funkcionality.</p>
                </div>
                <div class="w-full">
                    <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/ball/ball-2.png" height="100px" width="100px" alt="ball" class="flex justify-center object-contain mx-auto md:mx-0 md:block">
                    <h2 class="mb-5 text-base font-bold leading-6 text-theme-purple sm:my-5 sm:text-xl sm:leading-[30px]">Návrh</h2>
                    <p class="text-sm italic font-normal leading-5 text-light-purple">Po pečlivém přezkoumání vám předneseme naše návrhy. Dostanete od nás řešení jak z pohledu uživatele, tak z pohledu člena posádky. Všechno vám vysvětlíme a připravíme rozhraní i grafické návrhy.</p>
                </div>
                <div class="w-full">
                    <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/ball/ball-3.png" height="100px" width="100px" alt="ball" class="flex justify-center object-contain mx-auto md:mx-0 md:block">
                    <h2 class="mb-5 text-base font-bold leading-6 text-theme-purple sm:my-5 sm:text-xl sm:leading-[30px]">Vývoj</h2>
                    <p class="text-sm italic font-normal leading-5 text-light-purple">Vývoj software na zakázku začíná! Používáme hyperkosmické technologie, díky kterým posvištíte rychleji než světlo. Jsme neustále v kontaktu a hlásíme vám, co se na palubě rakety zrovna děje. Každou připomínku okamžitě zapracováváme.</p>
                </div>
                <div class="w-full">
                    <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/ball/ball-4.png" height="100px" width="100px" alt="ball" class="flex justify-center object-contain mx-auto md:mx-0 md:block">
                    <h2 class="mb-5 text-base font-bold leading-6 text-theme-purple sm:my-5 sm:text-xl sm:leading-[30px]">Testování</h2>
                    <p class="text-sm italic font-normal leading-5 text-light-purple">Dokončili jsme vývoj aplikace a přišel čas na poslední testování. Než odletíme do Vesmíru, otestujeme a zkontrolujeme dílčí části softwaru. Na testování se podílíme společně a případné chyby hledá celá naše posádka i vaši lidé.</p>
                </div>
                <div class="w-full">
                    <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/ball/ball-5.png" height="100px" width="100px" alt="ball" class="flex justify-center object-contain mx-auto md:mx-0 md:block">
                    <h2 class="mb-5 text-base font-bold leading-6 text-theme-purple sm:my-5 sm:text-xl sm:leading-[30px]">Odlet</h2>
                    <p class="text-sm italic font-normal leading-5 text-light-purple">Máme vše schváleno a odsouhlaseno. Teď už jen nasadíme software na míru do reálného provozu a připravíme se k letu. Žádné odklady ani zdržování. Nalodíme posádku, naposledy zkontrolujeme všechny kroky a letíme.</p>
                </div>
                <div class="w-full">
                    <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/ball/ball-6.png" height="100px" width="100px" alt="ball" class="flex justify-center object-contain mx-auto md:mx-0 md:block">
                    <h2 class="mb-5 text-base font-bold leading-6 text-theme-purple sm:my-5 sm:text-xl sm:leading-[30px]">Údržba a rozvoj</h2>
                    <p class="text-sm italic font-normal leading-5 text-light-purple">Úspěšným odletem to nekončí! I nadále analyzujeme chování lidí z jiných galaxií a hledáme mezery, které můžeme vyplnit. Sledujeme provoz aplikace, vyhodnocujeme výsledky a navrhujeme další změny na zlepšení</p>
                </div>
            </div>
        </section>
    </div>
    <div class="relative z-10 mt-10 mx-auto w-full max-w-[1440px] py-5 lg:px-16 xl:px-[133px] block  lg:h-[181px] mb-10 sm:mb-20 lg:mb-0">
        <svg class="absolute -top-24 left-1/2 z-10 hidden w-[99%] h-[3] lg:h-[280px] -translate-x-1/2 lg:block" xmlns="http://www.w3.org/2000/svg" width="1440" height="279" fill="none" viewBox="0 0 1440 279">
            <path fill="#fff" d="M76.157 210.956C90.465 250.588 128.08 277 170.216 277H1269.78c42.14 0 79.75-26.412 94.06-66.044L1440 0H0l76.157 210.956Z" />
            <path stroke="url(#a)" stroke-width="2" d="M120 278h1200" />
            <defs>
                <linearGradient id="a" x1="1320" x2="120" y1="278.978" y2="278.993" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#FE1CA6" stop-opacity="0" />
                    <stop offset=".505" stop-color="#FE1CA6" />
                    <stop offset="1" stop-color="#FE1CA6" stop-opacity="0" />
                </linearGradient>
            </defs>
        </svg>
        <div class="relative z-20 mx-auto  w-full max-w-[1210px] px-5 items-center justify-center md:justify-between gap-5 flex flex-col lg:flex-row text-center lg:text-left lg:-mt-12">
            <img src="<?= get_stylesheet_directory_uri() ?>/assets/images/rocket-wp.svg" alt="rocket" class="object-contain w-auto h-auto">
            <div class="md:max-w-[400px] 2xl:max-w-[600px] w-full">
                <h2 class="mb-4 text-base font-bold leading-normal sm:text-3xl text-theme-purple"><?= $title ?? '' ?></h2>
                <p class="text-sm font-normal leading-5 text-light-purple mb-7 lg:mb-0"><?= $description ?? '' ?></p>
            </div>
            <a href="<?= esc_url(home_url('/kontakt/')) ?>" class="btn-primary">Poslat signál<svg width="31" height="30" viewBox="0 0 31 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M0.62427 9.90397C-0.20809 10.6158 -0.20809 11.7684 0.62427 12.4801C4.29775 15.6175 7.98389 18.7423 11.6589 21.8797C12.4926 22.5919 13.8436 22.5916 14.6773 21.8797C15.8063 20.9145 16.9347 19.9485 18.0638 18.9833C15.0097 16.3902 11.9824 13.7455 8.90886 11.1824C11.9318 8.58328 14.9693 6.0008 18.0015 3.41241L14.6096 0.532872C13.7776 -0.177723 12.4249 -0.177525 11.5928 0.532872C7.93529 3.65515 4.28228 6.78208 0.62427 9.90397Z" fill="#FEFEFE" />
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.6968 11.0164C15.7521 13.6108 18.7846 16.2398 21.8515 18.8175L12.7591 26.5876C13.8898 27.5476 15.0242 28.5061 16.1507 29.4672C16.983 30.1776 18.3353 30.1776 19.1677 29.4672C22.825 26.3447 26.4784 23.2178 30.1363 20.0959C30.9687 19.3842 30.9687 18.2316 30.1363 17.5197C26.4617 14.3823 22.7764 11.2573 19.1017 8.12004C18.2679 7.40834 16.9171 7.40815 16.0834 8.12004C14.9544 9.08533 13.8259 10.0512 12.6968 11.0164Z" fill="#FEFEFE" />
                </svg>
            </a>
        </div>
    </div>
</main>

<?php get_footer();
