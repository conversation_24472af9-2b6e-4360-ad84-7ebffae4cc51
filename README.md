# Umíme Weby - Custom WordPress Theme

Custom WordPress theme pro firemní web Umíme Weby, založený na **[_tw (underscore tw)](https://underscoretw.com/)** foundation s moderní architekturou a Tailwind CSS.

## 🏗️ Architektura

Tento theme je postaven na **_tw (underscore tw)** foundation a využívá:

- **Namespace**: `Umimeweby\UWTheme\` pro všechny custom třídy
- **PSR-4 Autoloading** přes Composer
- **OOP přístup** pro všechny custom funkcionality
- **WordPress Coding Standards (WPCS)**
- **Moderní PHP 8.1+** s type hints a strict typing

### Struktura namespace

```
Umimeweby\UWTheme\
├── AdminDashboard\     # Správa admin dashboardu
├── AdminTables\        # Custom sloupce v admin tabulkách
├── CustomFields\       # Meta Box custom fields
├── CustomPostType\     # Custom post types
├── Form\              # Formuláře a jejich zpracování
├── Service\           # Služby a utility třídy
├── Settings\          # WordPress nastavení
├── ShortCodes\        # Custom shortcodes
└── Taxonomies\        # Custom taxonomie
```

## 🛠️ Tech Stack

### Backend
- **WordPress** 6.0+
- **PHP** 8.1+
- **_tw foundation** - [underscoretw.com](https://underscoretw.com/)
- **Composer** pro dependency management

### Frontend
- **Tailwind CSS** - utility-first CSS framework
- **FlowBite** - UI komponenty
- **Vanilla JavaScript** / jQuery
- **PostCSS** pro build proces

### Development Tools
- **PHPStan** - statická analýza kódu
- **PHP_CodeSniffer** - kontrola coding standards
- **WordPress Coding Standards (WPCS)**
- **npm** pro frontend build

## 📦 Composer Balíčky

### Production Dependencies
- `getresponse/sdk-php: ^3.0` - GetResponse API integrace

### Development Dependencies
- `phpstan/phpstan: ^1.10` - statická analýza
- `wp-coding-standards/wpcs: ^2.1.1` - WordPress coding standards
- `phpcompatibility/phpcompatibility-wp` - PHP kompatibilita
- `wp-cli/i18n-command` - internacionalizace

## 🚀 Instalace

### Předpoklady
- **WordPress** 6.0+
- **PHP** 8.1+
- **Node.js** 19+
- **Composer** 2.0+

### Požadované pluginy
1. **Meta Box** - https://cs.wordpress.org/plugins/meta-box/
2. **Meta Box AIO** - https://metabox.io/ (licencovaný plugin)
3. **WPS Hide Login** - https://cs.wordpress.org/plugins/wps-hide-login/
4. **WP Mail SMTP** - https://cs.wordpress.org/plugins/wp-mail-smtp/

### Kroky instalace

1. **Stažení a instalace WordPress** (verze 6.0+)

2. **Instalace požadovaných pluginů** (viz seznam výše)

3. **Naklonování theme**
   ```bash
   cd wp-content/themes/
   git clone [repository-url] umimeweby-cosmic-theme
   cd umimeweby-cosmic-theme
   ```

4. **Instalace dependencies** (pomocí Makefile)
   ```bash
   # Instalace všech dependencies (backend + frontend)
   make init
   
   # Nebo jednotlivě:
   make init-be    # Composer dependencies
   make init-fe    # NPM dependencies
   
   # První build
   npm run dev
   ```

5. **Aktivace theme** v WordPress administraci

## 🔧 Vývoj

### Development workflow

```bash
# Spuštění watch módu pro Tailwind CSS
npm run watch

# Build pro produkci
npm run bundle

# Kontrola kódu pomocí PHPStan
composer phpstan
# nebo pomocí Makefile
make pre-commit

# Regenerace PHPStan baseline
composer regenerate-baseline
# nebo pomocí Makefile
make regenerate-baseline
```

### Makefile příkazy

Projekt obsahuje Makefile pro zjednodušení běžných úkolů:

```bash
# Inicializace projektu (backend + frontend)
make init

# Inicializace pouze backend dependencies
make init-be

# Inicializace pouze frontend dependencies  
make init-fe

# Pre-commit kontrola (PHPStan analýza)
make pre-commit

# Regenerace PHPStan baseline
make regenerate-baseline
```

### Coding Standards

- Dodržuj **WordPress Coding Standards (WPCS)**
- Všechny custom třídy v namespace `Umimeweby\UWTheme\`
- Používej **PSR-4 autoloading**
- **OOP přístup** pro všechny custom funkcionality
- **Type hints** a strict typing pro PHP 8.1+
- Escapování všech výstupů (`esc_html`, `esc_attr`, atd.)
- Používej **WordPress API** místo přímých SQL dotazů

### Přidávání nových funkcionalit

1. Vytvoř novou třídu v odpovídajícím namespace
2. Zaregistruj třídu v `functions.php`
3. Dodržuj WordPress hooks pattern
4. Přidej PHPDoc komentáře
5. Otestuj pomocí `composer phpstan`

## 🎨 Tailwind CSS

Theme využívá **Tailwind CSS** s custom konfigurací:

- Konfigurace: `tailwind/tailwind.config.js`
- Custom styly: `tailwind/custom/`
- Typography plugin: `tailwind/tailwind-typography.config.js`

Přidávej [Tailwind utility classes](https://tailwindcss.com/docs/utility-first) podle potřeby.

## 📧 GetResponse Integrace

Theme obsahuje integraci s **GetResponse** pro newsletter:

- API integrace přes `getresponse/sdk-php`
- Nastavení v WordPress admin: **UW nastavení/GetResponse**
- Test list ID: `ZBclb` (pouze pro testování)

## 📄 Page Templates

Dostupné page templates:

- `page-templates/page-with-hero.php` - stránka s HERO sekcí
- `page-templates/landing-with-top-menu.php` - landing page s top menu

## 🔧 Shortcodes

Přehled všech shortcodů je dostupný v **WordPress Admin Dashboard** jako widget.

### Dostupné shortcodes

- **[uw-references-filtered](docs/shortcodes/uw-references-filtered.md)** - univerzální filtrování referencí (podle ID nebo tagu)
- **uw-references-by-category** - zobrazení referencí s filtrem kategorií
- **uw-carousel-references** - karusel referencí
- **uw-client-logos** - zobrazení log klientů
- **uw-mini-case-studies** - mini case studies
- **uw-technologies** - zobrazení technologií
- **uw-text-testimonials** - textové testimonials
- **uw-why-select-us** - proč si vybrat nás
- **uw-new-or-existing-projects** - nové nebo existující projekty
- **[cta-nl-card](docs/shortcodes/cta-nl-card.md)** - CTA newsletter karta s konfigurovatelným nadpisem a popiskem

## 🚀 Deployment

### Development
```bash
npm run watch
```

### Production
```bash
npm run bundle
```

Výsledný ZIP soubor nahraj přes "Upload Theme" v WordPress administraci.

## 📝 Git Workflow

Používáme **Conventional Commits**:

```bash
feat: přidání nové funkcionality
fix: oprava chyby
docs: aktualizace dokumentace
style: formátování kódu
refactor: refaktoring bez změny funkcionality
```

## 👥 Autoři

- **Vít Šafařík** - Vedoucí projektu a programátor

## 📚 Dokumentace

### Obecná dokumentace
- **_tw Documentation**: https://underscoretw.com/docs/
- **Tailwind CSS**: https://tailwindcss.com/docs/
- **WordPress Codex**: https://codex.wordpress.org/
- **Meta Box**: https://docs.metabox.io/

### Projektová dokumentace
- **[UW Service URL Router](UW_Service_URL_Router_README.md)** - Dokumentace pro custom URL routing systém

## 🆘 Podpora

Pro technické dotazy a podporu kontaktuj vývojový tým.

---

*Tento theme je založen na [_tw (underscore tw)](https://underscoretw.com/) foundation a využívá moderní WordPress development praktiky.*
