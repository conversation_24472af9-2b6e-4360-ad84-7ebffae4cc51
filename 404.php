<?php

get_header();


?>

<main class="w-full pt-4 md:mb-0 md:pt-0">
    <div class="px-4 md:px-5">
        <section class="mx-auto my-14 w-full sm:my-24 sm:max-w-[70vw] md:max-w-[1170px] text-theme-purple">
            <div class="mb-4 mt-4 flex flex-col items-center gap-2.5 sm:flex-row sm:gap-8">
                <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/sprava/planet 1.svg" alt="rocket wordpress" class="z-10 object-contain w-10 h-auto sm:w-24">
                <div>
                    <h3 class="text-lg font-bold leading-normal text-center text-theme-purple sm:text-left sm:text-3xl">Ups! Zdá se, že stránka, kterou hled<PERSON>te, se ztratila ve spleti internetu.</h3>
                </div>
            </div>
            <p class="text-base font-normal leading-5">
                <PERSON><PERSON> nebo<PERSON>, nic nen<PERSON>tra<PERSON>! Zatímco se naš tým intenzivně věnuje klientským projektům, které posouvají hranice možného, pro vás jsme připravili:
            </p>
            <ul class="text-base list-disc my-2 pl-5">
                <li><a class="underline text-dark-purple" href="<?= get_home_url() ?>/blog">poslední aktuální článek</a> na našem webu - rádi píšeme o tom, co skutečně děláme</li>
                <li><a class="underline text-dark-purple" href="<?= get_home_url() ?>/reference">poslední projekty</a>, které jsme pustili do světa - dáváme záležet na tom, co děláme</li>
                <li><a class="underline text-dark-purple" href="<?= get_home_url() ?>/blog/efektivni-digitalni-transformace-case-study-online-kurzu">projekt</a>, který vyhrál ocenění IEA 2024 - rádi to zopakujeme společně s vaším webem</li>
            </ul>
            <p class="text-base font-normal leading-5 mb-14">
                A pokud máte otázku, potřebujete radu či jen tak se s námi seznámit, <a class="underline text-dark-purple" href="<?= get_home_url() ?>/kontakt">sjednejte schůzku</a>. Je to také zdarma.
            </p>
        </section>
    </div>
    <div class="relative z-10 mt-10 mx-auto w-full max-w-[1440px] py-5 lg:px-16 xl:px-[133px] block  lg:h-[181px] mb-10 sm:mb-20 lg:mb-0">
        <svg class="absolute -top-24 left-1/2 z-10 hidden w-[99%] h-[3] lg:h-[280px] -translate-x-1/2 lg:block" xmlns="http://www.w3.org/2000/svg" width="1440" height="279" fill="none" viewBox="0 0 1440 279">
            <path fill="#fff" d="M76.157 210.956C90.465 250.588 128.08 277 170.216 277H1269.78c42.14 0 79.75-26.412 94.06-66.044L1440 0H0l76.157 210.956Z" />
            <path stroke="url(#a)" stroke-width="2" d="M120 278h1200" />
            <defs>
                <linearGradient id="a" x1="1320" x2="120" y1="278.978" y2="278.993" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#FE1CA6" stop-opacity="0" />
                    <stop offset=".505" stop-color="#FE1CA6" />
                    <stop offset="1" stop-color="#FE1CA6" stop-opacity="0" />
                </linearGradient>
            </defs>
        </svg>
    </div>
</main>

<?php get_footer();
