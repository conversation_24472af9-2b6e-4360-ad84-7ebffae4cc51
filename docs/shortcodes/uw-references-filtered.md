# Shortcode: uw-references-filtered

## Popis

Shortcode `uw-references-filtered` je univerzální řešení pro zobrazení referencí s různými typy filtrování. Kombinuje funkcionalitu shortcodů `uw-references-by-ids` a `uw-references-by-tag` do jed<PERSON>, čí<PERSON>ž eliminuje duplikaci kódu a zjednodušuje maintenance.

## Použití

```
[uw-references-filtered type="ids" ids="123,456,789" title="Vybrané projekty" description="Naše nejlepší realizace"]
[uw-references-filtered type="tag" tagname="wordpress" limit="3" title="WordPress projekty"]
```

## Parametry

| Parametr | Typ | Povinný | Výchozí hodnota | Popis |
|----------|-----|---------|-----------------|-------|
| `type` | string | **Ano** | `"ids"` | Typ filtrování: `"ids"` nebo `"tag"` |
| `ids` | string | Podmíněně* | `""` | Seznam ID referencí oddělených čárkami (povinný pro type="ids") |
| `tagname` | string | Podmíněně* | `""` | Slug WordPress tagu (povinný pro type="tag") |
| `limit` | number | Ne | `-1` | Maximální počet referencí (pouze pro type="tag", -1 = všechny) |
| `title` | string | Ne | `""` | Nadpis sekce |
| `description` | string | Ne | `""` | Popis sekce |

*Podmíněně povinný: `ids` je povinný pro `type="ids"`, `tagname` je povinný pro `type="tag"`

## Příklady použití

### Filtrování podle ID
```
[uw-references-filtered type="ids" ids="123,456,789"]
[uw-references-filtered type="ids" ids="123,456,789" title="Naše top projekty" description="Výběr nejlepších realizací"]
```

### Filtrování podle tagu
```
[uw-references-filtered type="tag" tagname="wordpress"]
[uw-references-filtered type="tag" tagname="wordpress" limit="6"]
[uw-references-filtered type="tag" tagname="e-commerce" limit="3" title="E-shop řešení"]
```

### Kombinace parametrů
```
[uw-references-filtered type="tag" tagname="webove-aplikace" limit="4" title="Webové aplikace" description="Naše custom aplikace"]
```

## Technické detaily

### Soubory
- **Třída**: `umimeweby/ShortCodes/Components/References_Filtered_Shortcode.php`
- **Template**: `template-parts/content/reference/references_filtered.php`
- **Registrace**: `umimeweby/ShortCodes/Shortcode_Provider_Service.php`

### Funkcionalita
1. **Validace typu**: Kontroluje se, že `type` je buď `"ids"` nebo `"tag"`
2. **Podmíněná validace**: Podle typu se validují odpovídající parametry
3. **Dynamické WP_Query**: Query se sestavuje podle typu filtrování
4. **Jednotný layout**: Používá stejný responzivní layout pro oba typy
5. **Zobrazení**: Používá stejnou kartu jako ostatní reference shortcodes

### Logika podle typu

#### Type: "ids"
- Validuje a parsuje parametr `ids`
- Používá `post__in` pro WP_Query
- Zachovává pořadí podle zadaných ID (`orderby => 'post__in'`)
- Ignoruje parametr `limit`

#### Type: "tag"
- Validuje parametr `tagname`
- Používá `tax_query` pro WordPress tagy
- Respektuje parametr `limit`
- Výchozí WordPress pořadí

### Bezpečnost
- Validace typu filtrování
- Sanitizace všech vstupních parametrů
- Používá se `wp_reset_postdata()` po WP_Query
- Výstupy jsou escapovány pomocí WordPress funkcí

## Chování

### Neplatný typ
- Pokud `type` není `"ids"` ani `"tag"`, shortcode se nezobrazí

### Type="ids" bez IDs
- Pokud nejsou zadána žádná ID, shortcode se nezobrazí

### Type="tag" bez tagname
- Pokud není zadán tagname, shortcode se nezobrazí

### Žádné výsledky
- Pokud nejsou nalezeny žádné reference, shortcode se nezobrazí

## Styling

Shortcode používá responzivní layout, který se přizpůsobuje počtu referencí:

### Layout podle počtu referencí
- **Všechny karty**: Mají stejnou pevnou šířku (320px na medium+ zařízeních)
- **1 reference**: Vycentrovaná karta
- **2 reference**: 2 karty vycentrované vedle sebe
- **3+ referencí**: Karty v řadách po 3, vždy vycentrované

### CSS třídy
- Pevná šířka karet pro konzistentní vzhled
- Karty s hover efekty
- Automatické centrování podle počtu referencí
- Responzivní chování (mobilní vs desktop)

## Výhody oproti původním shortcodes

### DRY princip
- ✅ Jeden kód místo duplikace
- ✅ Snadnější maintenance
- ✅ Konzistentní chování

### Flexibilita
- ✅ Jeden shortcode pro více typů filtrování
- ✅ Možnost budoucího rozšíření (např. `type="category"`)
- ✅ Jednotné API

### Performance
- ✅ Méně registrovaných shortcodes
- ✅ Sdílený template kód

## Migrace z původních shortcodes

### Z uw-references-by-ids
```
// Původní
[uw-references-by-ids ids="123,456,789" title="Projekty"]

// Nový
[uw-references-filtered type="ids" ids="123,456,789" title="Projekty"]
```

### Z uw-references-by-tag
```
// Původní
[uw-references-by-tag tagname="wordpress" limit="3" title="WordPress"]

// Nový
[uw-references-filtered type="tag" tagname="wordpress" limit="3" title="WordPress"]
```

## Poznámky pro vývojáře

- Shortcode je automaticky registrován přes `Shortcode_Provider_Service`
- Používá abstraktní třídu `Abstract_UW_Shortcode`
- Template obsahuje podmíněnou logiku pro oba typy filtrování
- Dodržuje WordPress Coding Standards
- Obsahuje type hints pro PHP 8.1+
- Připraven pro budoucí rozšíření o další typy filtrování

## Budoucí rozšíření

Shortcode je navržen tak, aby bylo snadné přidat další typy filtrování:
- `type="category"` - filtrování podle kategorií
- `type="author"` - filtrování podle autora
- `type="date"` - filtrování podle data
- `type="meta"` - filtrování podle custom fields

## Kompatibilita

- **WordPress**: 6.0+
- **PHP**: 8.1+
- **Backward compatibility**: Původní shortcodes zůstávají funkční
