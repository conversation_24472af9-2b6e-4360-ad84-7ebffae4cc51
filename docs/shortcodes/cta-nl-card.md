# Shortcode: cta-nl-card

## Popis

Shortcode `cta-nl-card` zobrazuje kartu s formulářem pro přihlášení k newsletteru. Karta obsahuje konfigurovatelný nadpis a popisek, spolu s formulářem pro zadání emailové adresy.

## Použití

```
[cta-nl-card]
[cta-nl-card title="Vlastní nadpis" subtitle="Vlastní popisek"]
```

## Parametry

| Parametr | Typ | Povinný | Výchozí hodnota | Popis |
|----------|-----|---------|-----------------|-------|
| `title` | string | Ne | `"Každý nový článek<br>u vás v emailu"` | Hlavní nadpis karty (podporuje HTML tagy) |
| `subtitle` | string | Ne | `"Užitečné tipy z oblasti využití webu a online světa pro váš byznys a firmu."` | Popisný text pod nadpisem |

## Příklady použití

### Základn<PERSON> použití
```
[cta-nl-card]
```
Zobrazí kartu s výchozími texty.

### S vlastním nadpisem
```
[cta-nl-card title="Zůstaňte v obraze"]
```
Změní pouze nadpis, popisek zůstane výchozí.

### S vlastním nadpisem a popiskem
```
[cta-nl-card title="Novinky ze světa webu" subtitle="Pravidelné tipy a triky pro váš online úspěch."]
```

### S HTML v nadpisu
```
[cta-nl-card title="Každý týden<br>nové tipy" subtitle="Praktické rady přímo do vaší schránky."]
```

## Technické detaily

### Soubory
- **Třída**: `umimeweby/ShortCodes/CTA/CTA_Newsletter_Card_Shortcode.php`
- **Template**: `template-parts/promo-sections/newsletter/_newsletter_card_cta.php`
- **Registrace**: Automaticky přes konstruktor třídy

### Funkcionalita
1. **Parametry**: Zpracovává parametry pomocí `shortcode_atts()`
2. **Výchozí hodnoty**: Definuje výchozí texty pro oba parametry
3. **Sanitizace**: Používá `wp_kses_post()` pro title a `esc_html()` pro subtitle
4. **Template**: Načítá template pomocí `include()` s output bufferingem

### Architektura
- **Dědičnost**: Dědí z `Abstract_UW_Shortcode`
- **Metody**:
  - `get_shortcode_code()`: Vrací konstantu shortcode
  - `get_default_attributes()`: Definuje výchozí parametry
  - `get_shortcode_content()`: Hlavní metoda pro generování obsahu
  - `get_html_code()`: Načítá a vrací HTML z template

### Bezpečnost
- **Title**: Sanitizován pomocí `wp_kses_post()` - povoluje bezpečné HTML tagy
- **Subtitle**: Sanitizován pomocí `esc_html()` - escapuje všechny HTML znaky
- **Output**: Celý výstup je sanitizován pomocí `sanitize_output()` z abstraktní třídy

## Design a styling

### CSS třídy
Karta používá Tailwind CSS třídy:
- `max-w-md`: Maximální šířka karty
- `border-2 border-light-purple`: Fialový border
- `p-12`: Padding uvnitř karty
- `rounded-lg shadow`: Zaoblené rohy a stín
- `dark:bg-gray-800 dark:border-gray-700`: Dark mode podpora

### Responzivní chování
- **Nadpis**: `text-[22px] md:text-3xl` - menší na mobilu, větší na desktopu
- **Text**: `text-xs md:text-sm` - responzivní velikost textu
- **Centrování**: `text-center md:text-left` - centrovaný na mobilu, vlevo na desktopu

### Formulář
Karta obsahuje shortcode `[uw-nl-01-form]` pro newsletter formulář.

## Použití v admin dashboardu

Shortcode je dokumentován v WordPress admin dashboardu v widgetu "UW Theme - Available Shortcodes" s:
- Názvem a syntaxí shortcode
- Popisem parametrů
- Příklady použití
- Odkazem na screenshot designu

## Výhody

### Flexibilita
- ✅ Konfigurovatelné texty
- ✅ Podpora HTML v nadpisu
- ✅ Výchozí hodnoty pro snadné použití

### Bezpečnost
- ✅ Správná sanitizace podle typu obsahu
- ✅ Používá WordPress security funkce

### Konzistence
- ✅ Používá abstraktní třídu pro jednotnou architekturu
- ✅ Dodržuje WordPress Coding Standards
- ✅ Konzistentní s ostatními shortcodes v projektu

## Kompatibilita

- **WordPress**: 6.0+
- **PHP**: 8.1+
- **Tailwind CSS**: Vyžaduje Tailwind CSS pro správné zobrazení
- **FlowBite**: Kompatibilní s FlowBite UI komponentami

## Poznámky pro vývojáře

- Shortcode automaticky registruje sám sebe v konstruktoru
- Template je načítán pomocí `get_stylesheet_directory()`
- Používá output buffering pro zachycení template výstupu
- Dodržuje DRY princip pomocí abstraktní třídy
- Připraven pro budoucí rozšíření o další parametry

## Budoucí rozšíření

Shortcode lze snadno rozšířit o další parametry:
- `button_text` - text tlačítka formuláře
- `placeholder` - placeholder pro email input
- `success_message` - zpráva po úspěšném odeslání
- `theme` - různé barevné varianty karty
- `size` - různé velikosti karty

## Migrace a změny

### Verze 1.0
- Základní funkcionalita s pevnými texty

### Verze 2.0 (aktuální)
- ✅ Přidány parametry `title` a `subtitle`
- ✅ Migrace na abstraktní třídu
- ✅ Vylepšená sanitizace
- ✅ Aktualizovaná dokumentace
