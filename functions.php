<?php

/**
 * Umíme Weby functions and definitions
 *
 * Custom WordPress theme založený na _tw (underscore tw) foundation
 * s moderní architekturou využívající namespace a PSR-4 autoloading.
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 * @link https://underscoretw.com/docs/
 *
 * @package Umíme_Weby
 * @namespace Umimeweby\UWTheme
 * @since 0.0.10
 *
 * Architektura:
 * - Namespace: Umimeweby\UWTheme\ pro všechny custom třídy
 * - PSR-4 Autoloading přes Composer
 * - OOP přístup pro všechny custom funkcionality
 * - WordPress Coding Standards (WPCS)
 * - PHP 8.1+ s type hints a strict typing
 */

require __DIR__ . '/vendor/autoload.php';

if (!defined('UW_VERSION')) {
    // Replace the version number of the theme on each release.
    define('UW_VERSION', '0.0.10');
}

use Umimeweby\UWTheme\AdminDashboard\Dashboard_UW_Widgets_Manager;
use Umimeweby\UWTheme\AdminTables\Admin_Tables_Modifications_Provider;
use Umimeweby\UWTheme\CustomFields\PageWithHeroTemplate\PageWithHero_Custom_Fields;
use Umimeweby\UWTheme\CustomPostType\Custom_Post_Type_Provider_Service;
use \Umimeweby\UWTheme\Form\Contact_Form_Sprava_WP;
use Umimeweby\UWTheme\Form\Form_Provider;
use Umimeweby\UWTheme\Form\Generic_Form;
use Umimeweby\UWTheme\Form\Landing_Page_Free_Web_Report_Form;
use Umimeweby\UWTheme\Form\Newsletter_Form;
use \Umimeweby\UWTheme\Form\Order_Form_Sprava_WP;
use Umimeweby\UWTheme\Service\Head\HeadModifier;
use Umimeweby\UWTheme\Service\Redirect\Redirect_Manager;
use Umimeweby\UWTheme\Service\Routing\UW_Service_URL_Router;
use Umimeweby\UWTheme\Settings\UW_Settings_Provider;
use Umimeweby\UWTheme\ShortCodes\Shortcode_Provider_Service;
use Umimeweby\UWTheme\Taxonomies\Custom_Taxonomy_Provider_Service;


$shortcode_provider_service = new Shortcode_Provider_Service();
$shortcode_provider_service->register();

$dashboard_uw_widgets_manager =  new Dashboard_UW_Widgets_Manager();

$admin_table_modification_provider = new Admin_Tables_Modifications_Provider();
$admin_table_modification_provider->register_modifications();

$custom_post_types_provider = new Custom_Post_Type_Provider_Service();
$custom_taxonomy_provider = new Custom_Taxonomy_Provider_Service();


$redirect_manager = new Redirect_Manager();
$uw_service_url_router = new UW_Service_URL_Router();

$form_provider = new Form_Provider();

$sprava_wp_contact_form = new Contact_Form_Sprava_WP();
add_action('admin_post_' . $sprava_wp_contact_form->get_form_name(), [$sprava_wp_contact_form, 'handle_form_submission']);
add_action('admin_post_nopriv_' . $sprava_wp_contact_form->get_form_name(), [$sprava_wp_contact_form, 'handle_form_submission']);


$sprava_wp_order_form = new Order_Form_Sprava_WP();
add_action('admin_post_' . $sprava_wp_order_form->get_form_name(), [$sprava_wp_order_form, 'handle_form_submission']);
add_action('admin_post_nopriv_' . $sprava_wp_order_form->get_form_name(), [$sprava_wp_order_form, 'handle_form_submission']);

$free_report_form = new Landing_Page_Free_Web_Report_Form();
$generic_form = new Generic_Form();




if (!function_exists('uw_setup')) :
    /**
     * Sets up theme defaults and registers support for various WordPress features.
     *
     * Note that this function is hooked into the after_setup_theme hook, which
     * runs before the init hook. The init hook is too late for some features, such
     * as indicating support for post thumbnails.
     */
    function uw_setup(): void
    {
        /*
         * Make theme available for translation.
         * Translations can be filed in the /languages/ directory.
         * If you're building a theme based on Umíme Weby, use a find and replace
         * to change 'uw' to the name of your theme in all the template files.
         */
        load_theme_textdomain('uw', get_template_directory() . '/languages');

        // Add default posts and comments RSS feed links to head.
        add_theme_support('automatic-feed-links');

        /*
         * Let WordPress manage the document title.
         * By adding theme support, we declare that this theme does not use a
         * hard-coded <title> tag in the document head, and expect WordPress to
         * provide it for us.
         */
        add_theme_support('title-tag');

        /*
         * Enable support for Post Thumbnails on posts and pages.
         *
         * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
         */
        add_theme_support('post-thumbnails');

        // This theme uses wp_nav_menu() in two locations.
        register_nav_menus(
            array(
                'primary' => 'Primary menu',
                'footer_service' => 'Footer menu services',
                'footer_about' => 'Footer menu about',
            )
        );

        /*
         * Switch default core markup for search form, comment form, and comments
         * to output valid HTML5.
         */
        add_theme_support(
            'html5',
            array(
                'search-form',
                'comment-form',
                'comment-list',
                'gallery',
                'caption',
                'style',
                'script',
            )
        );

        // Add theme support for selective refresh for widgets.
        add_theme_support('customize-selective-refresh-widgets');

        // Add support for editor styles.
        add_theme_support('editor-styles');

        // Enqueue editor styles.
        add_editor_style('style-editor.css');

        // Add support for responsive embedded content.
        add_theme_support('responsive-embeds');

        // Remove support for block templates.
        remove_theme_support('block-templates');
    }
endif;
add_action('after_setup_theme', 'uw_setup');

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function uw_widgets_init(): void
{
    register_sidebar(
        array(
            'name' => __('Footer', 'uw'),
            'id' => 'sidebar-1',
            'description' => __('Add widgets here to appear in your footer.', 'uw'),
            'before_widget' => '<section id="%1$s" class="widget %2$s">',
            'after_widget' => '</section>',
            'before_title' => '<h2 class="widget-title">',
            'after_title' => '</h2>',
        )
    );
}

add_action('widgets_init', 'uw_widgets_init');

/**
 * Enqueue scripts and styles.
 */
function uw_scripts(): void
{
    wp_enqueue_style('uw-swiper-style', get_template_directory_uri() . '/assets/styles/swiper-bundle.min.css');
    wp_enqueue_style('uw-style', get_stylesheet_uri(), array(), UW_VERSION);
    wp_enqueue_script('uw-script', get_template_directory_uri() . '/js/script.min.js', array(), UW_VERSION, true);
    wp_enqueue_script('counter', get_template_directory_uri() . '/js/counter.js', array(), UW_VERSION, true);
    wp_enqueue_script('uw-script-swiper', get_template_directory_uri() . '/js/swiper-bundle.min.js', array(), UW_VERSION, true);

    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}

add_action('wp_enqueue_scripts', 'uw_scripts');

/**
 * @param array<mixed> $settings
 */
function uw_tinymce_add_class($settings): mixed
{
    $settings['body_class'] = 'block-editor-block-list__layout';
    return $settings;
}

add_filter('tiny_mce_before_init', 'uw_tinymce_add_class');

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
require get_template_directory() . '/inc/template-functions.php';

// Register a metabox
if (is_plugin_active('meta-box/meta-box.php')) {


    $uw_settings_provider =  new UW_Settings_Provider();
    $uw_settings_provider->register_uw_settings();
    
    new \Umimeweby\UWTheme\CustomFields\Our_Blog_Custom_Fields();
    new \Umimeweby\UWTheme\CustomFields\Meantime_Finished_Custom_Fields();
    new \Umimeweby\UWTheme\CustomFields\Header_Custom_Fields();
    new \Umimeweby\UWTheme\CustomFields\Contact_Form_Custom_Fields();
    new \Umimeweby\UWTheme\CustomFields\AboutUs\About_Us_Custom_Fields();
    new \Umimeweby\UWTheme\CustomFields\AboutUs\Why_We_About_Us_Custom_Fields();
    new \Umimeweby\UWTheme\CustomFields\AboutUs\Team_Custom_Fields();
    new \Umimeweby\UWTheme\CustomFields\Services\Demand_Deliver_Custom_Fields();
    
    new \Umimeweby\UWTheme\CustomFields\Reference\Single\Single_Reference_Custom_Fields();
    new \Umimeweby\UWTheme\CustomFields\Reference\Single\Single_Case_Study_Custom_Fields();
        
    
    new \Umimeweby\UWTheme\CustomPostType\Our_Services_Custom_Post_Type();
    new \Umimeweby\UWTheme\CustomPostType\Reference_Custom_Post_Type();
    new \Umimeweby\UWTheme\CustomPostType\Text_Testimonials_Custom_Post_Type();
    new \Umimeweby\UWTheme\Taxonomies\Reference_Custom_Taxonomy();
    new \Umimeweby\UWTheme\ShortCodes\Firm_Numbers();
    new \Umimeweby\UWTheme\CustomFields\About_Us_Hero_Bottom();    

    new PageWithHero_Custom_Fields();

    $custom_post_types_provider->register_metabox_related_cpt();
    $custom_taxonomy_provider->register_metabox_related_taxonomies();


}

// Allow SVG uploads
/**
 * @param array<mixed> $mimes
 */
function enable_svg_upload($mimes): mixed
{
    $mimes['svg'] = 'image/svg+xml';
    return $mimes;
}

add_filter('upload_mimes', 'enable_svg_upload');

function add_svg_icons_to_menu(string $item_output, object $item, string $depth, object $args): string
{
    if ('primary' === $args->theme_location) {
        switch ($item->title) {
            case 'Homepage':
                $icon_svg = '<svg class="flex h-4 w-5 shrink-0 lg:h-[17px] lg:w-[26px]" xmlns="http://www.w3.org/2000/svg" width="26" height="17" viewBox="0 0 26 17" fill="none">
                                <path fill="#fff" d="M17.143 7.357A3.414 3.414 0 0 0 20.56 3.93 3.414 3.414 0 0 0 17.143.5a3.424 3.424 0 0 0-3.429 3.429 3.424 3.424 0 0 0 3.429 3.428ZM8 7.357a3.414 3.414 0 0 0 3.417-3.428A3.414 3.414 0 0 0 8 .5a3.424 3.424 0 0 0-3.429 3.429A3.424 3.424 0 0 0 8 7.357Zm0 2.286c-2.663 0-8 1.337-8 4V16.5h16v-2.857c0-2.663-5.337-4-8-4Zm9.143 0c-.332 0-.709.023-1.109.057 1.326.96 2.252 2.251 2.252 3.943V16.5h6.857v-2.857c0-2.663-5.337-4-8-4Z" />
                            </svg>';
                break;
            case 'Služby':
                $icon_svg = '<svg class="h-4 w-5 lg:h-[17px] lg:w-[26px]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 21" width="22" height="21" fill="none">
                                <path fill="#fff" d="M5.427 12.079a4.21 4.21 0 0 0-4.21 4.21 4.21 4.21 0 0 0 4.21 4.211 4.21 4.21 0 0 0 4.21-4.21 4.21 4.21 0 0 0-4.21-4.211Zm6.295 0V20.5h8.421v-8.421h-8.421ZM5.406.5.143 9.974H10.67L5.406.5Zm12.895.526c-1.116 0-1.905.59-2.368 1.232-.464-.642-1.253-1.232-2.369-1.232-1.642 0-2.895 1.348-2.895 2.895 0 2.105 2.548 3.6 5.263 6.053 2.716-2.453 5.264-3.948 5.264-6.053 0-1.547-1.253-2.895-2.895-2.895Z" />
                            </svg>';
                break;
            case 'Reference':
                $icon_svg = '<svg class="h-4 w-4 lg:h-[19px] lg:w-[19px]" xmlns="http://www.w3.org/2000/svg" width="19" height="19"
                    viewBox="0 0 19 19" fill="none">
                    <path fill="#fff" d="M.195 10.5h8V.5h-8v10Zm0 8h8v-6h-8v6Zm10 0h8v-10h-8v10Zm0-18v6h8v-6h-8Z" />
                  </svg>';
                break;
            case 'O nás':
                $icon_svg = '<svg class="flex h-4 w-5 shrink-0 lg:h-[17px] lg:w-[26px]" xmlns="http://www.w3.org/2000/svg"
                    width="26" height="17" viewBox="0 0 26 17" fill="none">
                    <path fill="#fff"
                      d="M17.143 7.357A3.414 3.414 0 0 0 20.56 3.93 3.414 3.414 0 0 0 17.143.5a3.424 3.424 0 0 0-3.429 3.429 3.424 3.424 0 0 0 3.429 3.428ZM8 7.357a3.414 3.414 0 0 0 3.417-3.428A3.414 3.414 0 0 0 8 .5a3.424 3.424 0 0 0-3.429 3.429A3.424 3.424 0 0 0 8 7.357Zm0 2.286c-2.663 0-8 1.337-8 4V16.5h16v-2.857c0-2.663-5.337-4-8-4Zm9.143 0c-.332 0-.709.023-1.109.057 1.326.96 2.252 2.251 2.252 3.943V16.5h6.857v-2.857c0-2.663-5.337-4-8-4Z" />
                  </svg>';
                break;
            case 'Blog':
                $icon_svg = '<svg class="h-4 w-5 lg:h-[21px] lg:w-[23px]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 23 21" width="23" height="21" fill="none">
                                <path fill="#fff" d="m22.418.5-1.856 1.856L18.717.5l-1.855 1.856L15.006.5l-1.844 1.856L11.306.5 9.451 2.356 7.606.5 5.751 2.356 3.895.5 2.051 2.356.195.5v17.778c0 1.222 1 2.222 2.223 2.222h17.777c1.223 0 2.223-1 2.223-2.222V.5ZM10.194 18.278H2.418V11.61h7.777v6.667Zm10 0h-7.777v-2.222h7.777v2.222Zm0-4.445h-7.777v-2.222h7.777v2.222Zm0-4.444H2.418V6.056h17.777v3.333Z" />
                            </svg>';
                break;
            case 'Kontakt':
                $icon_svg = '<svg class="h-4 w-4 lg:h-[21px] lg:w-[19px]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 19 21" width="19" height="21" fill="none">
                                <path fill="#fff" d="M16.418 2.5h-1v-2h-2v2h-8v-2h-2v2h-1a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14c1.1 0 2-.9 2-2v-14c0-1.1-.9-2-2-2Zm-7 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3Zm6 12h-12v-1c0-2 4-3.1 6-3.1s6 1.1 6 3.1v1Z" />
                            </svg>';
                break;
            default:
                $icon_svg = '';
                break;
        }
        $item_output = preg_replace('/(<a.*?>)/', '$1' . $icon_svg, $item_output);
    }
    return $item_output;
}

add_filter('walker_nav_menu_start_el', 'add_svg_icons_to_menu', 10, 4);

new HeadModifier();

/**
 * Flush rewrite rules při změnách URL struktury
 * Spustí se při aktivaci tématu nebo při změnách
 */
function uw_flush_rewrite_rules_on_activation(): void
{
    UW_Service_URL_Router::flush_rewrite_rules();
}

// Hook pro flush rewrite rules při aktivaci tématu
add_action('after_switch_theme', 'uw_flush_rewrite_rules_on_activation');

// Můžeš také spustit manuálně při potřebě
if (isset($_GET['flush_rewrite_rules']) && current_user_can('manage_options')) {
    uw_flush_rewrite_rules_on_activation();
    wp_redirect(remove_query_arg('flush_rewrite_rules'));
    exit;
}
