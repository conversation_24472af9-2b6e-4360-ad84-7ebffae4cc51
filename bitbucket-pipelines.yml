image: php:8.2-cli

pipelines:
  default:
    - step:
        name: Setup and PHPStan Analysis
        caches:
          - composer
        script:
          - apt-get update && apt-get install -y unzip curl
          - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
          - echo "memory_limit = 1024M" > $PHP_INI_DIR/conf.d/php-memory-limits.ini
          - export COMPOSER_ALLOW_SUPERUSER=1
          - composer install --no-scripts
          - php vendor/bin/phpstan analyse --debug

  pull-requests:
    '**':
      - step:
          name: PHPStan Analysis for PR
          caches:
            - composer
          script:
            - apt-get update && apt-get install -y unzip curl
            - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
            - echo "memory_limit = 1024M" > $PHP_INI_DIR/conf.d/php-memory-limits.ini
            - export COMPOSER_ALLOW_SUPERUSER=1
            - composer install --no-scripts
            - php vendor/bin/phpstan analyse --debug

  branches:
    demo:
      - step:
          name: Build and Deploy Demo
          caches:
            - composer
            - node
          script:
            - apt-get update && apt-get install -y unzip curl lftp
            - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
            - export COMPOSER_ALLOW_SUPERUSER=1
            - composer install --no-dev --no-scripts
            - echo "memory_limit = 1024M" > $PHP_INI_DIR/conf.d/php-memory-limits.ini
            - curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
            - export NVM_DIR="$HOME/.nvm"
            - '[ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"'
            - nvm install 18.16.1
            - nvm use 18.16.1
            - node --version
            - npm --version
            - npm install
            - npm run production
            - |
              exclude_patterns=""
              while IFS= read -r line; do
              [[ "$line" =~ ^#.*$ ]] || [[ -z "$line" ]] && continue  # Ignorujte komentáře a prázdné řádky
              exclude_patterns+=" --exclude-glob '$line'"
              done < .lftp_ignore
            - lftp -u $SFTP_USERNAME,$SFTP_PASSWORD sftp://$SFTP_SERVER -e "mirror --reverse --verbose --only-newer     $exclude_patterns ./ /public_html/wp-content/themes/umimeweby-cosmic-theme; quit"
    master:
      - step:
          name: Build and Deploy Production
          caches:
            - composer
            - node
          script:
            - apt-get update && apt-get install -y unzip curl lftp
            - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
            - export COMPOSER_ALLOW_SUPERUSER=1
            - composer install --no-dev --no-scripts
            - echo "memory_limit = 1024M" > $PHP_INI_DIR/conf.d/php-memory-limits.ini
            - curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
            - export NVM_DIR="$HOME/.nvm"
            - '[ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"'
            - nvm install 18.16.1
            - nvm use 18.16.1
            - node --version
            - npm --version
            - npm install
            - npm run production
            - |
              exclude_patterns=""
              while IFS= read -r line; do
              [[ "$line" =~ ^#.*$ ]] || [[ -z "$line" ]] && continue  # Ignorujte komentáře a prázdné řádky
              exclude_patterns+=" --exclude-glob '$line'"
              done < .lftp_ignore
            - lftp -u $SFTP_PROD_USERNAME,$SFTP_PROD_PASSWORD sftp://$SFTP_PROD_SERVER -e "mirror --reverse --verbose --only-newer     $exclude_patterns ./ /home/<USER>/public_html/wp-content/themes/umimeweby-cosmic-theme; quit"