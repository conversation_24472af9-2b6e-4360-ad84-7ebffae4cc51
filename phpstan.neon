includes:

    - phpstan-baseline.neon
    
parameters:
    level: 6
    paths:
        - ./
    excludePaths:
        - inc/template-functions.php
        - inc/template-tags.php   
        - template-parts/content/
        - template-parts/layout/
        # tady první 4 řádky jsou nadějné k opravě, dos<PERSON><PERSON><PERSON><PERSON><PERSON> refactoring, k<PERSON><PERSON> bude čas
        - js/
        - languages/
        - vendor/
        - javascript/
        - node_scripts/
        - node_modules/
    ignoreErrors:
        -
            messages:
                - '#Function rwmb_meta not found.#'
                - '#Function rwmb_set_meta not found.#'
                - '#Function rwmb_get_value not found.#'
                - '#Function rwmb_the_value not found.#'
                - '#Function rwmb_get_field_settings not found.#'
                - '#Function rwmb_get_object_fields not found.#'
                - '#Function rwmb_get_registry not found.#'
            reportUnmatched: false
            # tady jde o funkce externího pluginu Metabox, který není zohledněn
            # viz https://docs.metabox.io/category/functions/
            # výhodou nalink<PERSON>í by <PERSON><PERSON><PERSON> bylo, že bude hlídat
            # správn<PERSON> uplatnění funkcí - použité parametry a návratové hodnoty
        -
            message: '#Variable (.*) might not be defined.#'
            path: template-parts/*
            #identifier: variable.undefined
            # tady jde o proměnné v šablonách (ať už includované nebo předané přes args)
            # nevymysleli jsme jak to vyřešit
            # (ani get_template_part a pole $args nestačí, ale asi to bude cesta)
            # bude pak potřeba užít get_template_part + napsat phpstan extension, která to pochopí
            # ale zatím tedy template-parts ignorujeme, abychom mohli psát šablony a nebotnalo to
