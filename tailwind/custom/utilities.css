/**
 * Custom styles to immediately follow Tailwind’s `utilities` layer
 *
 * Add your own utility classes to this theme. Complex utility classes should
 * be added using Tailwind’s plugin system:
 *
 * https://tailwindcss.com/docs/plugins#adding-utilities
 */
.hide-it {
    @apply invisible opacity-0 pointer-events-none;
}

@layer utilities {

.uw-code {
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: white;
    text-align: left;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: #1f2937;



}

@media (min-width: 640px) {
    .uw-code {
        font-size: 1rem;
        line-height: 1.5rem;
    }
}


.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    @apply underline text-theme-pink hover:no-underline;
}

[class~="not-prose"]::marker {
    @apply text-dark-purple;
}


.uw-pink-underline {
    @apply border-b-2 border-theme-pink
}

}