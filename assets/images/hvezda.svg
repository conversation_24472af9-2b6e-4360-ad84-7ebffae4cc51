<svg width="72" height="68" viewBox="0 0 72 68" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_26_64)">
<path d="M34.1534 1.43986C34.8365 -0.202728 37.1635 -0.202729 37.8466 1.43986L46.111 21.3098C46.399 22.0022 47.0502 22.4754 47.7978 22.5353L69.2491 24.2551C71.0224 24.3972 71.7414 26.6102 70.3904 27.7676L54.0468 41.7676C53.4772 42.2555 53.2284 43.021 53.4025 43.7506L58.3957 64.6833C58.8085 66.4138 56.926 67.7815 55.4078 66.8542L37.0425 55.6368C36.4025 55.2458 35.5975 55.2458 34.9575 55.6368L16.5922 66.8542C15.074 67.7815 13.1915 66.4138 13.6043 64.6833L18.5975 43.7506C18.7716 43.021 18.5228 42.2555 17.9532 41.7676L1.60965 27.7676C0.258578 26.6102 0.97763 24.3972 2.75094 24.2551L24.2022 22.5353C24.9498 22.4754 25.601 22.0022 25.889 21.3098L34.1534 1.43986Z" fill="url(#paint0_radial_26_64)"/>
</g>
<defs>
<filter id="filter0_b_26_64" x="-14.092" y="-14.792" width="100.184" height="96.9458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="7.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_26_64"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_26_64" result="shape"/>
</filter>
<radialGradient id="paint0_radial_26_64" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(36 37) rotate(90) scale(40)">
<stop stop-color="#F8DA6F"/>
<stop offset="1" stop-color="#FFC700"/>
</radialGradient>
</defs>
</svg>
