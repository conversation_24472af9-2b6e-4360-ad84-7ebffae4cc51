{"require": {"php": ">=8.1", "getresponse/sdk-php": "^3.0"}, "require-dev": {"phpstan/phpstan": "^1.10", "phpstan/extension-installer": "^1.2", "szepeviktor/phpstan-wordpress": "^1.1", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/phpcompatibility-wp": "2.1.3", "sirbrillig/phpcs-changed": "^2.5.1", "sirbrillig/phpcs-variable-analysis": "^2.7.0", "wp-cli/i18n-command": "^2.4.1", "wp-coding-standards/wpcs": "^2.1.1"}, "scripts": {"phpstan": "php vendor/bin/phpstan analyse --debug", "regenerate-baseline": "php vendor/bin/phpstan analyse --level 6 --generate-baseline"}, "autoload": {"psr-4": {"Umimeweby\\UWTheme\\": "<PERSON><PERSON><PERSON><PERSON>"}}, "config": {"optimize-autoloader": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "phpstan/extension-installer": true}}}