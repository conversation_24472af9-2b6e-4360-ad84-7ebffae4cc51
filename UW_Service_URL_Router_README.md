# UW Service URL Router

Tento router implementuje prioritní URL routing pro Custom Post Type `uw-service` a jeho taxonomy `uw-service-category` pod společným slugem `/sluzby`.

## Funkčnost

### URL Struktura
- `/sluzby` → Archive page pro CPT uw-service
- `/sluzby/nazev-postu` → Single post (priorita 1)
- `/sluzby/nazev-kategorie` → Taxonomy archive (priorita 2)
- `/sluzby/neexistujici-slug` → 404 Not Found

### Prioritní řešení konfliktů
1. **Priorita 1**: Pokud existuje post s daným slugem → zobrazí single post
2. **Priorita 2**: Pokud existuje taxonomy term s daným slugem → zobrazí taxonomy archive
3. **Priorita 3**: Pokud nic neexistuje → zobrazí 404

## Implementace

### Soubory
- `UW_Service_URL_Router.php` - Hlavní router třída
- `UW_Service_Custom_Post_Type.php` - CPT s rewrite slug 'sluzby'
- `UW_Service_Custom_Taxonomy.php` - Taxonomy s rewrite slug 'sluzby'

### Aktivace
Router se aktivuje automaticky při načtení tématu přes `functions.php`.

### Flush Rewrite Rules
Po změnách je potřeba flush rewrite rules:

1. **Automaticky**: Při aktivaci tématu
2. **Manuálně**: Přidej `?flush_rewrite_rules=1` do URL (pouze pro adminy)
3. **WordPress admin**: Settings → Permalinks → Save Changes

## Testování

### Testovací scénáře
1. Vytvoř CPT post s slugem např. `webove-stranky`
2. Vytvoř taxonomy term s slugem např. `marketing`
3. Testuj URL:
   - `/sluzby/webove-stranky` → měl by zobrazit single post
   - `/sluzby/marketing` → měl by zobrazit taxonomy archive
   - `/sluzby/neexistuje` → měl by zobrazit 404
   - `/sluzby` → měl by zobrazit CPT archive

### Konfliktní situace
Pokud existuje post i taxonomy term se stejným slugem, post má prioritu (priorita 1).

## Debugging

### Query vars
Router používá tyto custom query vars:
- `uw_service_router` - indikuje aktivní router
- `uw_service_slug` - zachycený slug z URL

### Hooks
- `init` - registrace rewrite rules a query vars
- `request` - zpracování query vars
- `template_redirect` - finální routing logika

## Poznámky

- Router je kompatibilní s WordPress Coding Standards
- Používá namespace `Umimeweby\UWTheme\Service\Routing`
- Všechny metody mají type hints pro PHP 8.1+
- Router je SEO friendly a neovlivňuje výkon
